#!/bin/bash

# Database Import Script for Buzfi Project
# This script imports the buzfi.sql file into the MySQL database

echo "Starting database import process..."

# Wait for MySQL to be ready
echo "Waiting for MySQL to be ready..."
while ! docker exec buzfi-backend-mysql-1 mysqladmin ping -h"localhost" --silent; do
    echo "Waiting for MySQL connection..."
    sleep 2
done

echo "MySQL is ready. Starting import..."

# Import the SQL file
docker exec -i buzfi-backend-mysql-1 mysql -u root -proot buzfi < /home/<USER>/Development/buzfi-new-backend/import_database/buzfi.sql

if [ $? -eq 0 ]; then
    echo "Database import completed successfully!"
else
    echo "Database import failed!"
    exit 1
fi

echo "Import process finished."
