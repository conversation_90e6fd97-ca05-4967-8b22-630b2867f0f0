# Buzfi Laravel Project - Docker Management Makefile
# This Makefile provides shortcuts for managing the Dockerized Laravel application

# Default target
.DEFAULT_GOAL := help

# Variables
LARADOCK_DIR = laradock
COMPOSE_FILE = $(LARADOCK_DIR)/docker-compose.yml
SERVICES = nginx php-fpm mysql phpmyadmin redis workspace

# Colors for output
GREEN = \033[0;32m
YELLOW = \033[1;33m
RED = \033[0;31m
NC = \033[0m # No Color

.PHONY: help start stop restart build logs shell mysql-shell import-db clean status

help: ## Show this help message
	@echo "$(GREEN)Buzfi Laravel Project - Docker Management$(NC)"
	@echo "$(YELLOW)Available commands:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

start: ## Start all Docker services
	@echo "$(GREEN)Starting Buzfi Laravel application...$(NC)"
	cd $(LARADOCK_DIR) && docker-compose up -d $(SERVICES)
	@echo "$(GREEN)Services started successfully!$(NC)"
	@echo "$(YELLOW)Application URL: http://localhost/buzfi-new-backend$(NC)"
	@echo "$(YELLOW)phpMyAdmin URL: http://localhost/phpmyadmin$(NC)"

stop: ## Stop all Docker services
	@echo "$(RED)Stopping all services...$(NC)"
	cd $(LARADOCK_DIR) && docker-compose stop
	@echo "$(GREEN)All services stopped.$(NC)"

restart: ## Restart all Docker services
	@echo "$(YELLOW)Restarting all services...$(NC)"
	cd $(LARADOCK_DIR) && docker-compose restart $(SERVICES)
	@echo "$(GREEN)All services restarted.$(NC)"

build: ## Build/rebuild Docker containers
	@echo "$(YELLOW)Building Docker containers...$(NC)"
	cd $(LARADOCK_DIR) && docker-compose build $(SERVICES)
	@echo "$(GREEN)Build completed.$(NC)"

logs: ## Show logs from all services
	cd $(LARADOCK_DIR) && docker-compose logs -f

logs-nginx: ## Show Nginx logs
	cd $(LARADOCK_DIR) && docker-compose logs -f nginx

logs-php: ## Show PHP-FPM logs
	cd $(LARADOCK_DIR) && docker-compose logs -f php-fpm

logs-mysql: ## Show MySQL logs
	cd $(LARADOCK_DIR) && docker-compose logs -f mysql

shell: ## Access workspace container shell
	@echo "$(GREEN)Accessing workspace shell...$(NC)"
	cd $(LARADOCK_DIR) && docker-compose exec workspace bash

mysql-shell: ## Access MySQL shell
	@echo "$(GREEN)Accessing MySQL shell...$(NC)"
	cd $(LARADOCK_DIR) && docker-compose exec mysql mysql -u root -proot buzfi

import-db: ## Import database from import_database/buzfi.sql
	@echo "$(YELLOW)Importing database...$(NC)"
	./import_database/import.sh
	@echo "$(GREEN)Database import completed.$(NC)"

status: ## Show status of all containers
	@echo "$(GREEN)Container Status:$(NC)"
	cd $(LARADOCK_DIR) && docker-compose ps

clean: ## Remove all containers and volumes (WARNING: This will delete all data!)
	@echo "$(RED)WARNING: This will remove all containers and volumes!$(NC)"
	@echo "$(RED)All data will be lost. Are you sure? [y/N]$(NC)"
	@read -r REPLY; \
	if [ "$$REPLY" = "y" ] || [ "$$REPLY" = "Y" ]; then \
		cd $(LARADOCK_DIR) && docker-compose down -v --remove-orphans; \
		docker system prune -f; \
		echo "$(GREEN)Cleanup completed.$(NC)"; \
	else \
		echo "$(YELLOW)Cleanup cancelled.$(NC)"; \
	fi

install: ## Initial setup - build and start services
	@echo "$(GREEN)Setting up Buzfi Laravel application...$(NC)"
	cd $(LARADOCK_DIR) && docker-compose build $(SERVICES)
	cd $(LARADOCK_DIR) && docker-compose up -d $(SERVICES)
	@echo "$(YELLOW)Waiting for services to be ready...$(NC)"
	sleep 30
	@echo "$(GREEN)Setup completed!$(NC)"
	@echo "$(YELLOW)Application URL: http://localhost/buzfi-new-backend$(NC)"
	@echo "$(YELLOW)phpMyAdmin URL: http://localhost/phpmyadmin$(NC)"

composer-install: ## Install PHP dependencies
	@echo "$(GREEN)Installing PHP dependencies...$(NC)"
	cd $(LARADOCK_DIR) && docker-compose exec workspace composer install

artisan: ## Run Laravel Artisan commands (usage: make artisan cmd="migrate")
	cd $(LARADOCK_DIR) && docker-compose exec workspace php artisan $(cmd)

migrate: ## Run Laravel migrations
	@echo "$(GREEN)Running Laravel migrations...$(NC)"
	cd $(LARADOCK_DIR) && docker-compose exec workspace php artisan migrate

seed: ## Run Laravel seeders
	@echo "$(GREEN)Running Laravel seeders...$(NC)"
	cd $(LARADOCK_DIR) && docker-compose exec workspace php artisan db:seed

fresh: ## Fresh migration with seeding
	@echo "$(GREEN)Running fresh migrations with seeding...$(NC)"
	cd $(LARADOCK_DIR) && docker-compose exec workspace php artisan migrate:fresh --seed
