<?php

namespace App\Console\Commands;

use App\Notifications\order\PendingOrderNotification;
use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Models\Order;
use Illuminate\Support\Facades\Log;
class PendingOrderCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'PendingOrderCommand';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Log::channel('command_logs')->info('Pending Order Command Run ');
        $current_time = Carbon::now();

        $orders = Order::whereNotNull('pending_order_email')
            ->where('payment_status', 'unpaid')
            ->where('delivery_status', 'pending')
            ->where('pending_order_email', '<=', $current_time)
            ->get();
        Log::channel('command_logs')->info('Pending Order Command Orders data '.print_r($orders,true));
        foreach ($orders as $order) {
            Log::channel('command_logs')->info('Pending Order Command data '.print_r($order,true));
            $order->pending_order_email = null;
            $order->save();
            $array = array();
            $array['subject'] = translate('Secure Your Order  ') . ' - ' . $order->code . ' ' . ' Complete Your Payment ';
            $array['order'] = $order;
            $array['user_name'] = $order->user->name;
            try {
                $order->user->notify(new PendingOrderNotification($array));
                $command = 'php ' . base_path() . '/artisan queue:work --verbose --tries=1 --timeout=220 > /dev/null &';
                exec($command);
            } catch (\Exception $e) {
                Log::channel('email_logs')->error('Error occurred while sending Pending Order Notification email in Pending Order Command : ' . $e->getMessage());
            }

        }
        return Command::SUCCESS;
    }
}
