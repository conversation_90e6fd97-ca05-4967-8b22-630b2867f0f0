<?php

namespace App\Console\Commands;

use App\Models\Order;
use App\Notifications\order\PendingOrderReminderNotification;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
class PendingOrderReminderCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'PendingOrderReminderCommand';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $endRange = Carbon::now();

        $orders = Order::where('payment_status', 'unpaid')
            ->where('delivery_status', 'pending')
            ->whereNotNull('pending_order_reminder_email')
            ->where('pending_order_reminder_email', '<=', $endRange)
            ->get();

        if ($orders) {
            foreach ($orders as $order) {
                $order->pending_order_reminder_email = null;
                $order->save();
                $array = array();
                $array['order'] = $order;
                $array['user_name'] = $order->user->name;
                $array['subject'] = translate('Just a Little Nudge - Secure Your Order Soon');
                $order->user->notify(new PendingOrderReminderNotification($array));
            }
        }


        // Log::channel('command_logs')->error('Error occurred while sending Ready to Be Shipped email in OrderController : ' . print_r($orders, true));
        return Command::SUCCESS;
    }
}
