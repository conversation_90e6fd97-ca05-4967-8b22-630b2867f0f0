<?php

namespace App\Console\Commands;

use App\Services\EmailVerificationPerformanceService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class OptimizeEmailVerificationPerformance extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'email-verification:optimize
                            {--batch-size=1000 : Number of records to process in each batch}
                            {--warm-cache : Warm up cache for frequently accessed emails}
                            {--optimize-db : Optimize database indexes}
                            {--monitor : Show performance monitoring results}
                            {--cleanup : Run cleanup operations}';

    /**
     * The console command description.
     */
    protected $description = 'Optimize email verification performance through caching, indexing, and cleanup';

    /**
     * Performance service instance
     */
    protected $performanceService;

    /**
     * Create a new command instance.
     */
    public function __construct(EmailVerificationPerformanceService $performanceService)
    {
        parent::__construct();
        $this->performanceService = $performanceService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting email verification performance optimization...');

        $batchSize = (int) $this->option('batch-size');

        // Run cleanup operations
        if ($this->option('cleanup')) {
            $this->runCleanupOperations($batchSize);
        }

        // Optimize database indexes
        if ($this->option('optimize-db')) {
            $this->optimizeDatabaseIndexes();
        }

        // Warm up cache
        if ($this->option('warm-cache')) {
            $this->warmUpCache();
        }

        // Show performance monitoring
        if ($this->option('monitor')) {
            $this->showPerformanceMetrics();
        }

        // If no specific options, run all optimizations
        if (!$this->option('cleanup') && !$this->option('optimize-db') && 
            !$this->option('warm-cache') && !$this->option('monitor')) {
            $this->runAllOptimizations($batchSize);
        }

        $this->info('Email verification performance optimization completed!');
        return 0;
    }

    /**
     * Run all optimization operations
     */
    private function runAllOptimizations(int $batchSize): void
    {
        $this->runCleanupOperations($batchSize);
        $this->optimizeDatabaseIndexes();
        $this->warmUpCache();
        $this->showPerformanceMetrics();
    }

    /**
     * Run cleanup operations
     */
    private function runCleanupOperations(int $batchSize): void
    {
        $this->info('Running cleanup operations...');

        try {
            $results = $this->performanceService->batchProcessOTPOperations();

            $this->table(
                ['Operation', 'Records Processed'],
                [
                    ['Expired OTPs Cleaned', $results['expired_cleaned']],
                    ['Old Records Deleted', $results['old_records_deleted']],
                    ['Resend Counts Reset', $results['resend_counts_reset']],
                    ['Cache Keys Cleared', $results['cache_cleared']]
                ]
            );

            $this->info('✅ Cleanup operations completed successfully');

            // Log the cleanup results
            Log::info('Email verification cleanup completed', $results);

        } catch (\Exception $e) {
            $this->error('❌ Cleanup operations failed: ' . $e->getMessage());
            Log::error('Email verification cleanup failed', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Optimize database indexes
     */
    private function optimizeDatabaseIndexes(): void
    {
        $this->info('Optimizing database indexes...');

        try {
            $optimizations = $this->performanceService->optimizeDatabaseQueries();

            if (empty($optimizations)) {
                $this->info('✅ All database indexes are already optimized');
            } else {
                $this->info('✅ Database optimizations applied:');
                foreach ($optimizations as $optimization) {
                    $this->line('  • ' . $optimization);
                }
            }

            // Log the optimizations
            Log::info('Database indexes optimized', ['optimizations' => $optimizations]);

        } catch (\Exception $e) {
            $this->error('❌ Database optimization failed: ' . $e->getMessage());
            Log::error('Database optimization failed', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Warm up cache for frequently accessed emails
     */
    private function warmUpCache(): void
    {
        $this->info('Warming up cache...');

        try {
            // Get frequently accessed emails from recent records
            $frequentEmails = \App\Models\EmailVerify::select('email')
                ->where('created_at', '>=', now()->subHours(24))
                ->groupBy('email')
                ->havingRaw('COUNT(*) > 1')
                ->limit(100)
                ->pluck('email')
                ->toArray();

            if (empty($frequentEmails)) {
                $this->info('ℹ️  No frequently accessed emails found for cache warming');
                return;
            }

            $warmedUp = $this->performanceService->warmUpEmailCaches($frequentEmails);

            $this->info("✅ Cache warmed up for {$warmedUp} frequently accessed emails");

            // Log the cache warming
            Log::info('Email verification cache warmed up', [
                'emails_warmed' => $warmedUp,
                'total_frequent_emails' => count($frequentEmails)
            ]);

        } catch (\Exception $e) {
            $this->error('❌ Cache warming failed: ' . $e->getMessage());
            Log::error('Cache warming failed', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Show performance metrics
     */
    private function showPerformanceMetrics(): void
    {
        $this->info('Gathering performance metrics...');

        try {
            // Get performance metrics
            $metrics = $this->performanceService->getPerformanceMetrics();

            $this->info('📊 Performance Metrics:');
            $this->table(
                ['Metric', 'Value'],
                [
                    ['Cached Email Lookup', $metrics['cached_email_lookup_ms'] . ' ms'],
                    ['Direct Email Lookup', $metrics['direct_email_lookup_ms'] . ' ms'],
                    ['Cached Verification Check', $metrics['cached_verification_ms'] . ' ms'],
                    ['Cache Hit Improvement', $metrics['cache_hit_improvement'] . '%'],
                ]
            );

            // Show cache statistics if available
            if (isset($metrics['cache_statistics']) && is_array($metrics['cache_statistics'])) {
                $this->info('💾 Cache Statistics:');
                $cacheStats = $metrics['cache_statistics'];
                
                $cacheTable = [];
                foreach ($cacheStats as $key => $value) {
                    $cacheTable[] = [ucwords(str_replace('_', ' ', $key)), $value];
                }
                
                $this->table(['Statistic', 'Value'], $cacheTable);
            }

            // Monitor query performance
            $queryMetrics = $this->performanceService->monitorQueryPerformance();
            
            $this->info('🔍 Query Performance:');
            $queryTable = [];
            foreach ($queryMetrics as $queryName => $data) {
                $status = $data['status'] === 'success' ? '✅' : '❌';
                $time = $data['status'] === 'success' ? $data['execution_time_ms'] . ' ms' : 'Error';
                $queryTable[] = [ucwords(str_replace('_', ' ', $queryName)), $time, $status];
            }
            
            $this->table(['Query Type', 'Execution Time', 'Status'], $queryTable);

            // Log the metrics
            Log::info('Email verification performance metrics', $metrics);

        } catch (\Exception $e) {
            $this->error('❌ Failed to gather performance metrics: ' . $e->getMessage());
            Log::error('Performance metrics gathering failed', ['error' => $e->getMessage()]);
        }
    }
}