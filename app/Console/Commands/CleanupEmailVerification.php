<?php

namespace App\Console\Commands;

use App\Models\EmailVerify;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CleanupEmailVerification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email-verification:cleanup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up expired OTPs and reset resend counts for email verification';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting email verification cleanup...');

        try {
            // Clean up expired OTPs
            $expiredOtpsCount = EmailVerify::cleanupExpiredOTPs();
            $this->info("Cleaned up {$expiredOtpsCount} expired OTPs");

            // Reset resend counts for records older than 1 hour
            $resetCount = EmailVerify::resetExpiredResendCounts();
            $this->info("Reset resend count for {$resetCount} records");

            // Delete old verified records (older than 24 hours)
            $deletedCount = EmailVerify::where('is_verify', true)
                ->where('updated_at', '<', now()->subDay())
                ->delete();
            $this->info("Deleted {$deletedCount} old verified records");

            $this->info('Email verification cleanup completed successfully');
            Log::info('Email verification cleanup completed', [
                'expired_otps' => $expiredOtpsCount,
                'reset_counts' => $resetCount,
                'deleted_verified' => $deletedCount
            ]);

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Email verification cleanup failed: ' . $e->getMessage());
            Log::error('Email verification cleanup failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return Command::FAILURE;
        }
    }
}
