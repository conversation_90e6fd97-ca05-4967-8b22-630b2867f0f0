<?php

namespace App\Console\Commands;

use App\Services\EmailVerificationDatabaseService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class CleanupEmailVerificationRecords extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email-verification:cleanup 
                            {--dry-run : Show what would be cleaned up without making changes}
                            {--stats : Show database statistics}
                            {--integrity : Run integrity checks and fixes}
                            {--optimize : Optimize database indexes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up expired email verification records and optimize database performance';

    /**
     * Email verification database service
     *
     * @var EmailVerificationDatabaseService
     */
    protected $databaseService;

    /**
     * Create a new command instance.
     */
    public function __construct(EmailVerificationDatabaseService $databaseService)
    {
        parent::__construct();
        $this->databaseService = $databaseService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Email Verification Database Maintenance');
        $this->info('=====================================');

        try {
            // Show statistics if requested
            if ($this->option('stats')) {
                $this->showStatistics();
            }

            // Run integrity checks if requested
            if ($this->option('integrity')) {
                $this->runIntegrityChecks();
            }

            // Optimize database if requested
            if ($this->option('optimize')) {
                $this->optimizeDatabase();
            }

            // Run cleanup (default action)
            if (!$this->option('stats') && !$this->option('integrity') && !$this->option('optimize')) {
                $this->runCleanup();
            }

            $this->info('✅ Email verification database maintenance completed successfully');

        } catch (\Exception $e) {
            $this->error('❌ Email verification database maintenance failed: ' . $e->getMessage());
            Log::error('Email verification cleanup command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }

        return 0;
    }

    /**
     * Show database statistics
     */
    protected function showStatistics()
    {
        $this->info('📊 Database Statistics');
        $this->line('');

        $stats = $this->databaseService->getDatabaseStatistics();

        // Email verification records
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Email Verification Records', $stats['email_verify_records']['total']],
                ['Verified Records', $stats['email_verify_records']['verified']],
                ['Pending Verification', $stats['email_verify_records']['pending']],
                ['Expired Records', $stats['email_verify_records']['expired']],
                ['In Cooldown Period', $stats['email_verify_records']['in_cooldown']],
                ['Total Users', $stats['users_count']]
            ]
        );

        // Performance metrics
        if (!empty($stats['performance_metrics'])) {
            $this->line('');
            $this->info('⚡ Performance Metrics');
            $this->table(
                ['Query Type', 'Execution Time (ms)'],
                [
                    ['User Email Lookup', $stats['performance_metrics']['user_email_lookup_ms']],
                    ['Email Verify Lookup', $stats['performance_metrics']['email_verify_lookup_ms']]
                ]
            );
        }

        $this->line('');
    }

    /**
     * Run database cleanup
     */
    protected function runCleanup()
    {
        if ($this->option('dry-run')) {
            $this->info('🔍 Dry Run Mode - No changes will be made');
            $this->line('');
        }

        $this->info('🧹 Running Email Verification Cleanup');
        $this->line('');

        if (!$this->option('dry-run')) {
            $results = $this->databaseService->cleanupExpiredRecords();

            $this->table(
                ['Cleanup Action', 'Records Affected'],
                [
                    ['Expired OTPs Cleared', $results['expired_otps_cleared']],
                    ['Resend Counts Reset', $results['resend_counts_reset']],
                    ['Old Records Deleted', $results['old_records_deleted']]
                ]
            );

            $totalCleaned = array_sum($results);
            if ($totalCleaned > 0) {
                $this->info("✅ Cleaned up {$totalCleaned} records total");
            } else {
                $this->info('✅ No cleanup needed - database is already clean');
            }
        } else {
            $this->warn('Dry run mode - actual cleanup would be performed here');
        }

        $this->line('');
    }

    /**
     * Run integrity checks
     */
    protected function runIntegrityChecks()
    {
        $this->info('🔍 Running Database Integrity Checks');
        $this->line('');

        $results = $this->databaseService->ensureDatabaseIntegrity();

        $this->table(
            ['Integrity Check Result', 'Count'],
            [
                ['Checks Passed', $results['integrity_checks_passed']],
                ['Issues Found', $results['integrity_issues_found']],
                ['Issues Fixed', $results['issues_fixed']]
            ]
        );

        if (!empty($results['issues'])) {
            $this->line('');
            $this->warn('Issues Found:');
            foreach ($results['issues'] as $issue) {
                $this->line("  • {$issue}");
            }
        }

        if ($results['issues_fixed'] > 0) {
            $this->info("✅ Fixed {$results['issues_fixed']} integrity issues");
        } else {
            $this->info('✅ No integrity issues found');
        }

        $this->line('');
    }

    /**
     * Optimize database performance
     */
    protected function optimizeDatabase()
    {
        $this->info('⚡ Optimizing Database Performance');
        $this->line('');

        // Check authentication compatibility
        $this->info('Checking authentication compatibility...');
        $compatibility = $this->databaseService->verifyAuthenticationCompatibility();

        $compatibilityTable = [
            ['User Table', $compatibility['user_table_compatible'] ? '✅ Compatible' : '❌ Issues Found'],
            ['Email Verify Table', $compatibility['email_verify_table_compatible'] ? '✅ Compatible' : '❌ Issues Found'],
            ['Sanctum Tokens', $compatibility['sanctum_tokens_compatible'] ? '✅ Compatible' : '❌ Issues Found']
        ];

        $this->table(['Component', 'Status'], $compatibilityTable);

        // Optimize email lookup performance
        $this->info('Optimizing email lookup performance...');
        $optimization = $this->databaseService->optimizeEmailLookupPerformance();

        $this->table(
            ['Optimization Action', 'Result'],
            [
                ['Indexes Checked', $optimization['indexes_checked']],
                ['Indexes Created', $optimization['indexes_created']],
                ['Performance Optimized', $optimization['performance_optimized'] ? '✅ Yes' : '❌ No']
            ]
        );

        if ($optimization['indexes_created'] > 0) {
            $this->info("✅ Created {$optimization['indexes_created']} database indexes for better performance");
        } else {
            $this->info('✅ All required indexes are already in place');
        }

        $this->line('');
    }
}