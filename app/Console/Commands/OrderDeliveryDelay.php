<?php

namespace App\Console\Commands;

use App\Models\ActivityLog;
use App\Models\Order;
use App\Notifications\order\OrderDeliveryDelayNotification;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
class OrderDeliveryDelay extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'OrderDeliveryDelay';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $endRange = Carbon::now();
        $order_logs = ActivityLog::where('new_status', 'on_the_way')
            ->whereNotNull('email_end_time')
            ->where('email_end_time', '<=', $endRange)
            ->get();
        foreach ($order_logs as $order_log) {
            try {
                $order = Order::find($order_log->subject_id);
                $order_log->email_end_time = Null;
                $order_log->save();
                $array = array();
                $array['order_log'] = $order_log;
                $array['order_code'] = $order->code;
                $array['user_name'] = $order->user->name;
                $array['subject'] = translate('Just a Little Nudge - Secure Your Order Soon');
                $order->user->notify(new OrderDeliveryDelayNotification($array));
            } catch (\Exception $e) {
                Log::channel('command_logs')->error('Error occurred while sending  Order Delivery Delay Notification email in OrderDeliveryDelay command : ' . $e->getMessage());
            }
        }
        return Command::SUCCESS;
    }
}
