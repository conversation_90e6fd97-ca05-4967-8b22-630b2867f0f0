<?php

namespace App\Events;

use App\Models\UserNotification;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MessageEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $message;
    public $notification;
    public $messageType; // 'support', 'order', 'general'
    public $recipientIds;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($message, $messageType = 'general', array $recipientIds = [], UserNotification $notification = null)
    {
        $this->message = $message;
        $this->messageType = $messageType;
        $this->recipientIds = $recipientIds;
        $this->notification = $notification;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        $channels = [];

        // Add channels for specific recipients
        foreach ($this->recipientIds as $recipientId) {
            $channels[] = new PrivateChannel('notifications.' . $recipientId);
        }

        // Add admin channel for admin notifications
        if ($this->messageType === 'support' || empty($this->recipientIds)) {
            $channels[] = new PrivateChannel('admin.notifications');
        }

        // Add message-specific channel
        if (isset($this->message['id'])) {
            $channels[] = new PrivateChannel('message.' . $this->message['id']);
        }

        return $channels;
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs()
    {
        return 'new.message';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        $data = [
            'message' => $this->message,
            'message_type' => $this->messageType,
            'recipient_ids' => $this->recipientIds,
            'timestamp' => now()->toIso8601String(),
        ];

        if ($this->notification) {
            $data['notification'] = [
                'id' => $this->notification->id,
                'type' => $this->notification->type,
                'title' => $this->notification->title,
                'message' => $this->notification->message,
                'date' => $this->notification->created_at->toIso8601String(),
                'read' => (bool) $this->notification->read,
                'priority' => $this->notification->priority,
                'link' => $this->notification->link,
                'linkText' => $this->notification->link_text,
            ];
        }

        return $data;
    }
} 