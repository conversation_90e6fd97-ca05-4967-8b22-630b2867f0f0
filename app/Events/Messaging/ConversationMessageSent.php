<?php

namespace App\Events\Messaging;

use App\Models\Messaging\ConversationMessage;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ConversationMessageSent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The message instance.
     *
     * @var \App\Models\Messaging\ConversationMessage
     */
    public $message;

    /**
     * The conversation ID.
     *
     * @var string
     */
    public $conversationId;

    /**
     * Create a new event instance.
     *
     * @param  \App\Models\Messaging\ConversationMessage  $message
     * @param  string  $conversationId
     * @return void
     */
    public function __construct(ConversationMessage $message, $conversationId)
    {
        $this->message = $message;
        $this->conversationId = $conversationId;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        // Get the conversation
        $conversation = $this->message->conversation;
        
        // Broadcast to both users' private channels
        return [
            new PrivateChannel('user.' . $conversation->user1_id . '.conversations'),
            new PrivateChannel('user.' . $conversation->user2_id . '.conversations'),
            new PrivateChannel('conversation.' . $this->conversationId),
        ];
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs()
    {
        return 'message.new';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        // Load relationships to include in the broadcast
        $this->message->load(['sender', 'attachments']);
        
        return [
            'message' => [
                'id' => $this->message->id,
                'content' => $this->message->content,
                'sender_id' => $this->message->sender_id,
                'created_at' => $this->message->created_at,
                'attachments' => $this->message->attachments->map(function($attachment) {
                    return [
                        'id' => $attachment->id,
                        'name' => $attachment->name,
                        'url' => $attachment->url,
                        'type' => $attachment->type,
                    ];
                }),
            ],
            'conversation_id' => $this->conversationId,
        ];
    }
} 