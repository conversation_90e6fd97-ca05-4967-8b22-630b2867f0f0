<?php

namespace App\Events\Messaging;

use App\Models\Messaging\Message;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class MessageSent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The message instance.
     *
     * @var \App\Models\Messaging\Message
     */
    public $message;

    /**
     * The thread ID.
     *
     * @var string
     */
    public $threadId;

    /**
     * Create a new event instance.
     *
     * @param  \App\Models\Messaging\Message  $message
     * @return void
     */
    public function __construct(Message $message)
    {
        $this->message = $message;
        $this->threadId = $message->thread_id;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        // Broadcast to the thread channel and to all participants' private channels
        $channels = [
            new PrivateChannel('thread.' . $this->threadId),
        ];
        
        // Get all thread participants
        $participants = $this->message->thread->participants;
        
        foreach ($participants as $participant) {
            $channels[] = new PrivateChannel('user.' . $participant->user_id . '.messages');
        }
        
        return $channels;
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs()
    {
        return 'message.new';
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        // Load relationships to include in the broadcast
        $this->message->load(['sender', 'attachments']);
        
        return [
            'message' => $this->message,
            'thread_id' => $this->threadId,
        ];
    }
} 