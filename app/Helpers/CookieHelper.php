<?php

namespace App\Helpers;

class CookieHelper
{
    /**
     * Get secure cookie configuration based on environment
     */
    public static function getSecureConfig(): array
    {
        $isProduction = config('app.env') === 'production';
        $isHttps = request()->secure() || config('app.url', '') === 'https';

        return [
            'secure' => $isProduction && $isHttps,
            'same_site' => $isProduction ? 'none' : 'lax',
            'http_only' => true,
            'domain' => config('session.domain'),
            'path' => '/',
        ];
    }

    /**
     * Get Stripe-specific cookie configuration
     */
    public static function getStripeConfig(): array
    {
        $baseConfig = self::getSecureConfig();
        
        // Stripe requires SameSite=None for cross-origin requests in production
        if (config('app.env') === 'production') {
            $baseConfig['same_site'] = 'none';
            $baseConfig['secure'] = true;
        }

        return $baseConfig;
    }

    /**
     * Set cookie with proper security configuration
     */
    public static function setCookie(string $name, string $value, int $minutes = null, array $options = []): void
    {
        $config = array_merge(self::getSecureConfig(), $options);
        $minutes = $minutes ?? config('session.lifetime', 120);

        cookie()->queue(
            $name,
            $value,
            $minutes,
            $config['path'],
            $config['domain'],
            $config['secure'],
            $config['http_only'],
            false,
            $config['same_site']
        );
    }

    /**
     * Set Stripe-specific cookie
     */
    public static function setStripeCookie(string $name, string $value, int $minutes = null): void
    {
        $config = self::getStripeConfig();
        self::setCookie($name, $value, $minutes, $config);
    }

    /**
     * Get cookie headers for API responses
     */
    public static function getCookieHeaders(): array
    {
        $config = self::getSecureConfig();
        
        $headers = [];
        
        // Add security headers for cookie handling
        $headers['X-Content-Type-Options'] = 'nosniff';
        $headers['X-Frame-Options'] = 'DENY';
        $headers['Referrer-Policy'] = 'strict-origin-when-cross-origin';
        
        // Add SameSite policy header for better browser compatibility
        if ($config['same_site'] === 'none') {
            $headers['Cross-Origin-Opener-Policy'] = 'same-origin';
            $headers['Cross-Origin-Embedder-Policy'] = 'require-corp';
        }

        return $headers;
    }
} 