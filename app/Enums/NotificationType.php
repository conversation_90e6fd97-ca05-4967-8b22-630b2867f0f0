<?php

namespace App\Enums;

class NotificationType
{
    const ORDER = 'order';
    const PAYMENT = 'payment';
    const CART = 'cart';
    const STOCK = 'stock';
    const SUPPORT = 'support';
    const SYSTEM = 'system';
    const MESSAGE = 'message';
    const RETURN_REQUEST = 'return_request';
    const SHIPPING = 'shipping';
    const PROMOTION = 'promotion';
    const ACCOUNT = 'account';
    const REVIEW = 'review';
    const PRICE_ALERT = 'price_alert';
    const COMMISSION = 'commission';

    public static function all(): array
    {
        return [
            self::ORDER,
            self::PAYMENT,
            self::CART,
            self::STOCK,
            self::SUPPORT,
            self::SYSTEM,
            self::MESSAGE,
            self::RETURN_REQUEST,
            self::SHIPPING,
            self::PROMOTION,
            self::ACCOUNT,
            self::REVIEW,
            self::PRICE_ALERT,
            self::COMMISSION,
        ];
    }

    public static function getDisplayName(string $type): string
    {
        return match($type) {
            self::ORDER => 'Order',
            self::PAYMENT => 'Payment',
            self::CART => 'Cart',
            self::STOCK => 'Stock',
            self::SUPPORT => 'Support',
            self::SYSTEM => 'System',
            self::MESSAGE => 'Message',
            self::RETURN_REQUEST => 'Return Request',
            self::SHIPPING => 'Shipping',
            self::PROMOTION => 'Promotion',
            self::ACCOUNT => 'Account',
            self::REVIEW => 'Review',
            self::PRICE_ALERT => 'Price Alert',
            self::COMMISSION => 'Commission',
            default => ucfirst($type),
        };
    }
}
