<?php

namespace App\Exports;

use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;

class PaidPaymentOrderDetailsExport implements FromQuery, WithHeadings
{
    /**
     * Query to fetch paid payments data.
     */
    public function query()
    {
        return DB::table('orders as o')
            ->join('users as u', 'o.user_id', '=', 'u.id')
            ->join('order_details as od', 'o.id', '=', 'od.order_id')
            ->join('products as p', 'od.product_id', '=', 'p.id')
            ->leftJoin('suppliers as s', 'p.supplier_id', '=', 's.id')
            ->selectRaw('
            o.created_at AS `Order Date`,
            o.payment_status AS `Order Status`,
            o.id AS `Buzfi Order Number`,
            o.code AS `Order Code`,
            u.id AS `Customer ID`,
            u.email AS `Customer Email`,
            u.name AS `Customer Name`,
            GROUP_CONCAT(
                CONCAT(
                    "Product: ", p.name,
                    " (Quantity: ", od.quantity,
                    ", Price: ", od.price,
                    ")"
                ) SEPARATOR " | "
            ) AS `Product Details`,
            o.grand_total AS `Total Amount`,
            o.payment_charge_id AS `Charge ID`,
            o.payment_type AS `Paid Via`,
            FROM_UNIXTIME(o.date) AS `Trxn Date`,
            o.tracking_code AS `Trxn ID`,
            "n/a" AS `Charges`,
            "n/a" AS `Payout to Bank Amount`,
            "n/a" AS `Payout Status`,
            "n/a" AS `Payout Date`,
            "n/a" AS `Source/Supplier Category`,
            s.name AS `Source/Supplier Name`,
            s.email AS `Source/Supplier Email`,
            "n/a" AS `Source/Supplier Payment Status`
        ')
            ->where('o.payment_status', 'paid')
            ->groupBy('o.id', 'u.id')
            ->orderBy('o.created_at', 'desc');
    }

    /**
     * Headings for the export.
     */
    public function headings(): array
    {
        return [
            'Order Date',
            'Order Status',
            'Buzfi Order Number',
            'Order Code',
            'Customer ID',
            'Customer Email',
            'Customer Name',
            'Product Details',
            'Total Amount',
            'Charge ID',
            'Paid Via',
            'Trxn Date',
            'Trxn ID',
            'Charges',
            'Payout to Bank Amount',
            'Payout Status',
            'Payout Date',
            'Source/Supplier Category',
            'Source/Supplier Name',
            'Source/Supplier Email',
            'Source/Supplier Payment Status',
        ];
    }
}
