<?php

namespace App\Exports;

use App\Models\User;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\Exportable;

class CustomersExport implements FromQuery, WithHeadings, WithMapping
{
    use Exportable;

    protected $start_date;
    protected $end_date;
    protected $search;

    public function __construct($start_date = null, $end_date = null, $search = null)
    {
        $this->start_date = $start_date;
        $this->end_date = $end_date;
        $this->search = $search;
    }

    public function query()
    {
        $query = User::with(['orders.products', 'wishlists.product'])
                    ->where('user_type', 'customer')
                    ->whereNotNull('email_verified_at');

        if ($this->search) {
            $query->where(function ($q) {
                $q->where('name', 'like', '%' . $this->search . '%')
                  ->orWhere('email', 'like', '%' . $this->search . '%');
            });
        }

        if ($this->start_date && $this->end_date) {
            $query->whereBetween('created_at', [$this->start_date, $this->end_date]);
        }

        return $query;
    }

    public function headings(): array
    {
        return [
            'Customer Name',
            'Customer Email',
            'Phone Number',
            'Purchased Products',
            'Purchase Dates',
            'Wishlisted Products',
        ];
    }

    public function map($user): array
    {
        $purchased_products = $user->orders->flatMap(function ($order) {
            return $order->products->pluck('name');
        })->toArray();

        $purchase_dates = $user->orders->pluck('created_at')->toArray();

        $wishlisted_products = $user->wishlists->flatMap(function ($wishlist) {
            return $wishlist->product->pluck('name');
        })->toArray();

        return [
            $user->name,
            $user->email,
            $user->phone,
            implode(', ', $purchased_products),
            implode(', ', array_map(fn($date) => $date->format('Y-m-d'), $purchase_dates)),
            implode(', ', $wishlisted_products),
        ];
    }
}
