<?php

namespace App\Exports;

use App\Models\Order;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class OrdersExport implements FromCollection, WithHeadings, WithMapping
{
    public function collection()
    {
        // Fetch orders with necessary relationships
        return Order::with(['user', 'orderDetails.product', 'shop'])->get();
    }

    public function headings(): array
    {
        return [
            'Order Number',
            'Customer Name',
            'Address',
            'Customer Phone Number',
            'Tracking Info',
            'Purchase Date',
            'Product Names',
            'Product URLs',
            'Units Sold',
            'Total Orders',
            'GMV (Gross Merchandise Value)',
            'Canceled Sales',
            'Refund Sales',
            'Order Note',
            'Payment Method'
        ];
    }

    public function map($order): array
    {
        return [
            $order->code, // Order Number
            $order->user ? $order->user->name : 'Guest', // Customer Name
            $order->user ? $order->user->address ?? 'N/A' : 'N/A', // Customer Address
            $order->user ? $order->user->phone ?? 'N/A' : 'N/A', // Customer Phone Number
            $order->carrier_tracking_code ?? 'N/A', // Tracking Info
            $order->created_at->format('Y-m-d'), // Purchase Date
            implode(', ', $order->orderDetails->pluck('product.name')->toArray()), // Product Names
            implode(', ', $order->orderDetails->pluck('product.url')->toArray()), // Product URLs (Assuming 'url' is a valid field in product model)
            $order->orderDetails->sum('quantity'), // Units Sold
            $order->grand_total, // Total Orders (GMV)
            'N/A',
            $order->canceled_sales ?? 'N/A', // Canceled Sales (Assuming canceled sales is tracked)
            $order->refund_sales ?? 'N/A', // Refund Sales (Assuming refund sales is tracked)
            $order->order_note ?? 'N/A', // Order Note
            ucfirst(str_replace('_', ' ', $order->payment_type)) // Payment Method, formatted for readability
        ];
    }
}
