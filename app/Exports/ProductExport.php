<?php

namespace App\Exports;

use App\Models\Product;
use App\Models\Upload;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Illuminate\Support\Facades\Log;

class ProductExport implements FromCollection, WithHeadings
{
    public function collection()
    {
        $products = Product::with([
            'stocks'
        ])->get();

        $data = [];

        foreach ($products as $product) {

            $productData = [
                'ID' => $product->id,
                'Name' => $product->name,
                'Added By' => $product->added_by,
                'Supplier ID' => $product->supplier_id,
                'User ID' => $product->user_id,
                'Category ID' => $product->category_id,
                'Brand ID' => $product->brand_id,
                // 'Auction Product' => ($product->auction_product === 1? 1 : '0'),
                // 'Wholesale Product' => ($product->wholesale_product === 1? 1 : '0'),
                'Photos' => implode(', ', $this->convertPhotoUrls($product->photos)),
                'Thumbnail Img' => $this->getPhotoUrl($product->thumbnail_img),
                'Video Provider' => $product->video_provider,
                'Video Link' => $product->video_link,
                'Tags' => $product->tags,
                'Description' => $product->description,
                'Short Description' => $product->short_description,
                'Unit Price' => $product->unit_price,
                'dropshipper Price' => $product->b2b_price,
                'Purchase Price' => $product->purchase_price,
                'Variant Product' => $product->variant_product,
                "Today's Deal" => $product->todays_deal,
                'Published' => $product->published,
                'Approved' => $product->approved,
                'Stock Visibility State' => $product->stock_visibility_state,
                'Cash On Delivery' => $product->cash_on_delivery,
                'Featured' => $product->featured,
                'Seller Featured' => $product->seller_featured,
                'Current Stock' => $product->current_stock,
                'Unit' => $product->unit,
                'Weight' => $product->weight,
                'Length' => $product->length,
                'Width' => $product->width,
                'Height' => $product->height,
                'Allow Customer Review' => $product->allow_customer_review,
                'Position' => $product->position,
                'Min Qty' => $product->min_qty,
                'Low Stock Quantity' => $product->low_stock_quantity,
                'Discount' => $product->discount,
                'Discount Type' => $product->discount_type,
                'Discount Start Date' => ($product->discount_start_date !== 0 && $product->discount_start_date !== NULL) ? date('d-m-Y', $product->discount_start_date) : NULL,
                'Discount End Date' => ($product->discount_end_date != 0 && $product->discount_end_date !== NULL) ? date('d-m-Y', $product->discount_end_date) : NULL,
                'Tax' => $product->tax,
                'Tax Type' => $product->tax_type,
                'Shipping Type' => $product->shipping_type,
                'Shipping Cost' => $product->shipping_cost,
                'Is Quantity Multiplied' => $product->is_quantity_multiplied,
                'Est Shipping Days' => $product->est_shipping_days,
                'Num Of Sale' => $product->num_of_sale,
                'Meta Title' => $product->meta_title,
                'Meta Description' => $product->meta_description,
                'Meta Img' => $product->meta_img,
                'PDF' => $product->pdf,
                'Slug' => $product->slug,
                'Refundable' => $product->refundable,
                'Rating' => $product->rating,
                'Barcode' => $product->barcode,
                'Digital' => $product->digital,
                'File Name' => $product->file_name,
                'File Path' => $product->file_path,
                'External Link' => $product->external_link,
                'External Link Btn' => $product->external_link_btn,
                'Purchase Note' => $product->purchase_note,
                'Shipping Class' => $product->shipping_class,
                'Download Limit' => $product->download_limit,
                'Download Expiry Day' => $product->download_expiry_day,
                'Upsell' => $product->upsell,
                'Cross Sell' => $product->cross_sell,
                'Parent' => $product->parent,
                'Sold Individually' => $product->sold_individually,
                'Visibility In Catalog' => $product->visibility_in_catalog,
                'Grouped Product' => $product->grouped_product,
                'Backorders Allowed' => $product->backorders_allowed,
                'SKU' => $product->stocks[0]->sku,
                'UPC' => $product->stocks[0]->upc,
            ];
            $data[] = $productData;
        }
        return collect($data);
    }

    public function headings(): array
    {
        return [
            'ID',
            'Name',
            'Added By',
            'Supplier ID',
            'User ID',
            'Category ID',
            'Brand ID',
            // 'Auction Product',
            // 'Wholesale Product',
            'Photos',
            'Thumbnail Img',
            'Video Provider',
            'Video Link',
            'Tags',
            'Description',
            'Short Description',
            'Unit Price',
            'dropshipper Price',
            'Purchase Price',
            'Variant Product',
            "Today's Deal",
            'Published',
            'Approved',
            'Stock Visibility State',
            'Cash On Delivery',
            'Featured',
            'Seller Featured',
            'Current Stock',
            'Unit',
            'Weight',
            'Length',
            'Width',
            'Height',
            'Allow Customer Review',
            'Position',
            'Min Qty',
            'Low Stock Quantity',
            'Discount',
            'Discount Type',
            'Discount Start Date',
            'Discount End Date',
            'Tax',
            'Tax Type',
            'Shipping Type',
            'Shipping Cost',
            'Is Quantity Multiplied',
            'Est Shipping Days',
            'Num Of Sale',
            'Meta Title',
            'Meta Description',
            'Meta Img',
            'PDF',
            'Slug',
            'Refundable',
            'Rating',
            'Barcode',
            'Digital',
            'File Name',
            'File Path',
            'External Link',
            'External Link Btn',
            'Purchase Note',
            'Shipping Class',
            'Download Limit',
            'Download Expiry Day',
            'Upsell',
            'Cross Sell',
            'Parent',
            'Sold Individually',
            'Visibility In Catalog',
            'Grouped Product',
            'Backorders Allowed',
            'SKU',
            'UPC',
        ];
    }

    private function convertPhotoUrls($photoIds)
    {
        $photoUrls = [];
        $ids = explode(',', $photoIds);

        foreach ($ids as $id) {
            $url = $this->getPhotoUrl($id);
            if ($url) {
                $photoUrls[] = $url;
            }
        }

        return $photoUrls;
    }

    // Get photo URL by ID
    private function getPhotoUrl($id)
    {
        $upload = Upload::find($id);
        if ($upload) {
            if($upload->external_link) {
                return $upload->external_link;
            }else{
                return 'https://buzfi.com/public/'.$upload->file_name;
            }
        }
        return null;
    }
}
