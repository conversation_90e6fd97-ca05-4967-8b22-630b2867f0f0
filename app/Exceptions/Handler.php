<?php

namespace App\Exceptions;

use App\Utility\NgeniusUtility;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Http\JsonResponse;
class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    public function render($request, Throwable $e)
    {
        if ($e instanceof ModelNotFoundException) {
            return response([
                'success' => false,
                'code' => 'VALIDATION_ERROR',
                'message' => class_basename($e->getModel())." not found!",
            ], 404);
        }
        if($this->isHttpException($e))
        {
            if ($request->is('customer-products/admin')) {
                return NgeniusUtility::initPayment();
            }

            return parent::render($request, $e);
        }else if ($request->expectsJson()) {
            if ($e instanceof AuthenticationException) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication failed. Please log in.',
                    'errors' => ['auth' => ['You must be logged in to access this resource.']]
                ], JsonResponse::HTTP_UNAUTHORIZED);
            }
        }
        else
        {
            return parent::render($request, $e);
        }
    }
}
