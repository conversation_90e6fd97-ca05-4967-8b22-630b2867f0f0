<?php

namespace App\Http\Controllers;

use Auth;
use Hash;
use Mail;
use Cache;
use Cookie;
use App\Models\Cart;
use App\Models\Page;
use App\Models\Shop;
use App\Models\User;
use App\Models\Brand;
use App\Models\Order;
use App\Models\Coupon;
use App\Models\Product;
use App\Models\Category;
use App\Models\FlashDeal;
use App\Models\B2BProfile;
use App\Models\OrderDetail;
use App\Models\PickupPoint;
use Illuminate\Support\Str;
use App\Models\ProductQuery;
use App\Models\ProductStock;
use Illuminate\Http\Request;
use App\Models\AffiliateConfig;
use App\Models\CustomerPackage;
use App\Models\FlashDealProduct;
use App\Utility\CategoryUtility;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Response;

// use App\Notifications\OTPEmailVerificationNotification;
use Illuminate\Auth\Events\PasswordReset;
use App\Mail\SecondEmailVerifyMailManager;
use App\Models\HomePageBanner;
use App\Notifications\OTPRestPasswordNotification;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;

class HomeController extends Controller
{
    protected $purifier;
    /**
     * Show the application frontend home.
     *
     * @return \Illuminate\Http\Response
     */
    // public function index()
    // {
    //     $featured_categories = Cache::rememberForever('featured_categories', function () {
    //         return Category::where('featured', 1)->with('iconUpload')->get();
    //     });

    //     // Debugging line
    //    // dd($featured_categories->toArray());

    //     return view('frontend.index', compact('featured_categories'));
    // }

    //devkamal
    public function __construct()
    {
        //$this->middleware('auth');
    }
    public function index()
    {
        // $featured_categories = Cache::rememberForever('featured_categories', function () {
        //     return Category::select('categories.*', 'uploads.file_name as icon_path')
        //         ->leftJoin('uploads', 'categories.icon', '=', 'uploads.id')
        //         ->where('featured', 1)
        //         ->get();
        // });
        /*
        $featured_categories = Cache::rememberForever('featured_categories', function () {
            return Category::select('categories.*', 'uploads.file_name as icon_path')
                ->leftJoin('uploads', 'categories.icon', '=', 'uploads.id')
                ->where('featured', 1)
                ->where('featured', 0)
                ->get();
        });
        */
        $featured_categories = Category::all();

        //$featured_categories = Category::all();

        return view('frontend.index-new', compact('featured_categories'));
    }



    public function load_todays_deal_section()
    {
        $todays_deal_products = filter_products(Product::where('todays_deal', '1'))->get();
        // dd($todays_deal_products);
        return view('frontend.partials.todays_deal', compact('todays_deal_products'));
    }


    public function load_newest_product_section()
    {
        //return filter_products(Product::where('featured', 1)->latest())->limit(12)->get();
        $newest_products = filter_products(Product::where('featured', 1)->latest())->limit(100)->get();
        $flash_deal = FlashDeal::where('status', 1)->latest()->first();

        return view('frontend.partials.newest_products_section', compact('newest_products', 'flash_deal'));
    }
    public function load_newest_product_section01()
    {
        $newest_products = Cache::remember('newest_products', 3600, function () {
            return filter_products(Product::latest())->limit(12)->get();
        });

        $flash_deal = FlashDeal::where('status', 1)->latest()->first();

        return view('frontend.partials.newest_products_section', compact('newest_products', 'flash_deal'));
    }


    public function load_featured_section()
    {
        return view('frontend.partials.featured_products_section');
    }

    public function load_best_selling_section()
    {
        return view('frontend.partials.best_selling_section');
    }

    public function load_auction_products_section()
    {
        if (!addon_is_activated('auction')) {
            return;
        }
        return view('auction.frontend.auction_products_section');
    }

    public function load_home_categories_section()
    {
        return view('frontend.partials.home_categories_section');
    }

    public function load_best_sellers_section()
    {
        return view('frontend.partials.best_sellers_section');
    }

    public function login()
    {
        if (Auth::check()) {
            return redirect()->route('home');
        }

        if (Route::currentRouteName() == 'seller.login' && get_setting('vendor_system_activation') == 1) {
            return view('frontend.seller_login');
        } else if (Route::currentRouteName() == 'dropshipper.login' && get_setting('vendor_system_activation') == 1) {
            return view('frontend.dropshipper_login');
        } else if (Route::currentRouteName() == 'deliveryboy.login' && addon_is_activated('delivery_boy')) {
            return view('frontend.deliveryboy_login');
        }

        return view('frontend.user_login');
    }
    public function dropshipperLanding()
    {
        //dd('dropshipperlandgin');
        return view('frontend.dropshipper_landing');
    }
    public function registration(Request $request)
    {
        if (Auth::check()) {
            return redirect()->route('home');
        }
        if ($request->has('referral_code') && addon_is_activated('affiliate_system')) {
            try {
                $affiliate_validation_time = AffiliateConfig::where('type', 'validation_time')->first();
                $cookie_minute = 30 * 24;
                if ($affiliate_validation_time) {
                    $cookie_minute = $affiliate_validation_time->value * 60;
                }

                Cookie::queue('referral_code', $request->referral_code, $cookie_minute);
                $referred_by_user = User::where('referral_code', $request->referral_code)->first();

                $affiliateController = new AffiliateController;
                $affiliateController->processAffiliateStats($referred_by_user->id, 1, 0, 0, 0);
            } catch (\Exception $e) {
            }
        }
        return view('frontend.user_registration');
    }

    public function guestRegistration(Request $request)
    {
        //dd('dd');
        if (Auth::check()) {
            return redirect()->route('home');
        }
        if ($request->has('referral_code') && addon_is_activated('affiliate_system')) {
            try {
                $affiliate_validation_time = AffiliateConfig::where('type', 'validation_time')->first();
                $cookie_minute = 30 * 24;
                if ($affiliate_validation_time) {
                    $cookie_minute = $affiliate_validation_time->value * 60;
                }

                Cookie::queue('referral_code', $request->referral_code, $cookie_minute);
                $referred_by_user = User::where('referral_code', $request->referral_code)->first();

                $affiliateController = new AffiliateController;
                $affiliateController->processAffiliateStats($referred_by_user->id, 1, 0, 0, 0);
            } catch (\Exception $e) {
            }
        }
        // return redirect()->route('storeGuest');
        return view('frontend.guest_registration');
    }

    public function cart_login(Request $request)
    {
        $user = null;
        if ($request->get('phone') != null) {
            $user = User::whereIn('user_type', ['customer', 'seller'])->where('phone', "+{$request['country_code']}{$request['phone']}")->first();
        } elseif ($request->get('email') != null) {
            $user = User::whereIn('user_type', ['customer', 'seller'])->where('email', $request->email)->first();
        }

        if ($user != null) {
            if (Hash::check($request->password, $user->password)) {
                if ($request->has('remember')) {
                    auth()->login($user, true);
                } else {
                    auth()->login($user, false);
                }
            } else {
                flash(translate('Invalid email or password!'))->warning();
            }
        } else {
            flash(translate('Invalid email or password!'))->warning();
        }
        return back();
    }

    /**
     * Create a new controller instance.
     *
     * @return void
     */


    /**
     * Show the customer/seller dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function dashboard()
    {
        if (Auth::user()->user_type == 'seller') {
            return redirect()->route('seller.dashboard');
        } elseif (Auth::user()->user_type == 'customer') {
            $users_cart = Cart::where('user_id', auth()->user()->id)->first();
            if ($users_cart) {
                flash(translate('You had placed your items in the shopping cart. Try to order before the product quantity runs out.'))->warning();
            }
            $cID = auth()->user()->id;
            $user = User::where('id', $cID)->first();
            if ($user->email_verified_at == NULL) {
                return view('frontend.user.customer.waiting_for_verification');
            } else {
                return view('frontend.user.customer.dashboard');
            }
        } elseif (Auth::user()->user_type == 'delivery_boy') {
            return view('delivery_boys.dashboard');
        } else {
            abort(404);
        }
    }


    public function profile(Request $request)
    {
        if (Auth::user()->user_type == 'seller') {
            return redirect()->route('seller.profile.index');
        } elseif (Auth::user()->user_type == 'delivery_boy') {
            return view('delivery_boys.profile');
        } else {
            return view('frontend.user.profile');
        }
    }

    public function userProfileUpdate(Request $request)
    {
        if (env('DEMO_MODE') == 'On') {
            flash(translate('Sorry! the action is not permitted in demo '))->error();
            return back();
        }

        $user = Auth::user();
        $user->name = $request->name;
        $user->address = $request->address;
        $user->country = $request->country;
        $user->city = $request->city;
        $user->postal_code = $request->postal_code;
        $user->phone = $request->phone;

        if ($request->new_password != null && ($request->new_password == $request->confirm_password)) {
            $user->password = Hash::make($request->new_password);
        }

        $user->avatar_original = $request->photo;
        $user->save();

        flash(translate('Your Profile has been updated successfully!'))->success();
        return back();
    }

    public function flash_deal_details($slug)
    {
        $flash_deal = FlashDeal::where('slug', $slug)->first();
        if ($flash_deal != null)
            return view('frontend.flash_deal_details', compact('flash_deal'));
        else {
            abort(404);
        }
    }

    public function trackOrder(Request $request)
    {
        if ($request->has('order_code')) {
            $order = Order::where('code', $request->order_code)->first();
            if ($order != null) {
                return view('frontend.track_order', compact('order'));
            }
        }
        return view('frontend.track_order');
    }

    public function product(Request $request, $slug)
    {

        if (!Auth::check()) {
            session(['link' => url()->current()]);
        }

        $detailedProduct  = Product::with('reviews', 'brand', 'stocks', 'user', 'user.shop', 'category.parentCategory')->where('auction_product', 0)->where('slug', $slug)->where('approved', 1)->first();

        if ($detailedProduct != null && $detailedProduct->published) {

            if (!addon_is_activated('wholesale') && $detailedProduct->wholesale_product == 1) {
                abort(404);
            }

            $product_queries = ProductQuery::where('product_id', $detailedProduct->id)->where('customer_id', '!=', Auth::id())->latest('id')->paginate(3);
            $total_query = ProductQuery::where('product_id', $detailedProduct->id)->count();
            $reviews = $detailedProduct->reviews()->paginate(4);

            // Pagination using Ajax
            if (request()->ajax()) {
                if ($request->type == 'query') {
                    return Response::json(View::make('frontend.partials.product_query_pagination', array('product_queries' => $product_queries))->render());
                }
                if ($request->type == 'review') {
                    return Response::json(View::make('frontend.product_details.reviews', array('reviews' => $reviews))->render());
                }
            }

            // review status
            $review_status = 0;
            $customer_shipping_address = null;
            if (Auth::check()) {
                // $OrderDetail = OrderDetail::with(['order' => function ($q) {
                //     $q->where('user_id', Auth::id());
                // }])->where('product_id', $detailedProduct->id)->where('delivery_status', 'delivered')->first();
                $OrderDetail = OrderDetail::where('product_id', $detailedProduct->id)
                    ->whereHas('order', function ($q) {
                        $q->where('user_id', Auth::id());
                    })
                    ->with('order') // This loads the related 'order' data after filtering with whereHas
                    ->first();
                // dd($OrderDetail);
                $review_status = $OrderDetail ? 1 : 0;
                $customer_shipping_address = ($OrderDetail && $OrderDetail->order) ? json_decode($OrderDetail->order->shipping_address, true) : null;
            }
            if ($request->has('product_referral_code') && addon_is_activated('affiliate_system')) {
                $affiliate_validation_time = AffiliateConfig::where('type', 'validation_time')->first();
                $cookie_minute = 30 * 24;
                if ($affiliate_validation_time) {
                    $cookie_minute = $affiliate_validation_time->value * 60;
                }
                Cookie::queue('product_referral_code', $request->product_referral_code, $cookie_minute);
                Cookie::queue('referred_product_id', $detailedProduct->id, $cookie_minute);

                $referred_by_user = User::where('referral_code', $request->product_referral_code)->first();

                $affiliateController = new AffiliateController;
                $affiliateController->processAffiliateStats($referred_by_user->id, 1, 0, 0, 0);
            }
            // Initialize an array with all possible ratings and count as 0
            $ratings = [
                5 => 0,
                4 => 0,
                3 => 0,
                2 => 0,
                1 => 0,
            ];

            // Process the reviews to count the ratings
            $allReviews = $detailedProduct->reviews()->get();
            foreach ($allReviews as $review) {
                $ratings[$review->rating]++;
            }
            // Calculate the total number of reviews
            $totalReviews = array_sum($ratings);
            // Fetch ProductStock for the product
            $productStock = ProductStock::where('product_id', $detailedProduct->id)->first();
            $maxPages = ceil(Product::where('id', $detailedProduct->id)->where('category_id', $detailedProduct->category_id)->count() / 4);
            $home_page_banners = HomePageBanner::where('status', 1)->orderBy('position', 'asc')->get();
            $banner = $home_page_banners->firstWhere('position', 1);
            return view('frontend.new_product_details', compact('maxPages', 'customer_shipping_address', 'detailedProduct', 'product_queries', 'total_query', 'reviews', 'review_status', 'ratings', 'totalReviews', 'banner'));
        }
        abort(404);
    }

    public function shop($slug)
    {
        $shop  = Shop::where('slug', $slug)->first();
        if ($shop != null) {
            if ($shop->verification_status != 0) {
                return view('frontend.seller_shop', compact('shop'));
            } else {
                return view('frontend.seller_shop_without_verification', compact('shop'));
            }
        }
        abort(404);
    }

    public function filter_shop(Request $request, $slug, $type)
    {
        $shop  = Shop::where('slug', $slug)->first();
        if ($shop != null && $type != null) {

            if ($type == 'all-products') {
                $sort_by = $request->sort_by;
                $min_price = $request->min_price;
                $max_price = $request->max_price;
                $selected_categories = array();
                $brand_id = null;
                $rating = null;

                $conditions = ['user_id' => $shop->user->id, 'published' => 1, 'approved' => 1];

                if ($request->brand != null) {
                    $brand_id = (Brand::where('slug', $request->brand)->first() != null) ? Brand::where('slug', $request->brand)->first()->id : null;
                    $conditions = array_merge($conditions, ['brand_id' => $brand_id]);
                }

                $products = Product::where($conditions);

                if ($request->has('selected_categories')) {
                    $selected_categories = $request->selected_categories;
                    $products->whereIn('category_id', $selected_categories);
                }

                if ($min_price != null && $max_price != null) {
                    $products->where('unit_price', '>=', $min_price)->where('unit_price', '<=', $max_price);
                }

                if ($request->has('rating')) {
                    $rating = $request->rating;
                    $products->where('rating', '>=', $rating);
                }

                switch ($sort_by) {
                    case 'newest':
                        $products->orderBy('created_at', 'desc');
                        break;
                    case 'oldest':
                        $products->orderBy('created_at', 'asc');
                        break;
                    case 'price-asc':
                        $products->orderBy('unit_price', 'asc');
                        break;
                    case 'price-desc':
                        $products->orderBy('unit_price', 'desc');
                        break;
                    default:
                        $products->orderBy('id', 'desc');
                        break;
                }

                $products = $products->paginate(24)->appends(request()->query());

                return view('frontend.seller_shop', compact('shop', 'type', 'products', 'selected_categories', 'min_price', 'max_price', 'brand_id', 'sort_by', 'rating'));
            }

            return view('frontend.seller_shop', compact('shop', 'type'));
        }
        abort(404);
    }

    public function all_categories(Request $request)
    {
        // $categories = Category::where('level', 0)->orderBy('order_level', 'desc')->get();
        $categories = Category::where('parent_id', 0)
            ->where('is_visible', 1)
            ->with(['children_Categories' => function ($query) {
                $query->where('is_visible', 1)
                    ->with(['children_Categories' => function ($query) {
                        $query->where('is_visible', 1);
                    }]);
            }])
            ->get();
        return view('frontend.all_category', compact('categories'));
    }

    public function all_brands(Request $request)
    {
        $brands = Brand::all();
        return view('frontend.all_brand', compact('brands'));
    }

    public function home_settings(Request $request)
    {
        return view('home_settings.index');
    }

    public function top_10_settings(Request $request)
    {
        foreach (Category::all() as $key => $category) {
            if (is_array($request->top_categories) && in_array($category->id, $request->top_categories)) {
                $category->top = 1;
                $category->save();
            } else {
                $category->top = 0;
                $category->save();
            }
        }

        foreach (Brand::all() as $key => $brand) {
            if (is_array($request->top_brands) && in_array($brand->id, $request->top_brands)) {
                $brand->top = 1;
                $brand->save();
            } else {
                $brand->top = 0;
                $brand->save();
            }
        }

        flash(translate('Top 10 categories and brands have been updated successfully'))->success();
        return redirect()->route('home_settings.index');
    }

    public function variant_price(Request $request)
    {
        $product = Product::find($request->id);
        $str = '';
        $quantity = 0;
        $tax = 0;
        $max_limit = 0;

        if ($request->has('color')) {
            $str = $request['color'];
        }
        // added by arif on 4 june 2024:: start
        $choice_options = "";

        if (is_array($product->choice_options)) {
            $choice_options = json_encode($product->choice_options);
        } else {
            $choice_options = $product->choice_options;
        }

        if (json_decode($choice_options) != null) {
            // dd($choice_options);
            foreach (json_decode($choice_options) as $key => $choice) {
                if ($str != null) {
                    $str .= '-' . str_replace(' ', '', $request['attribute_id_' . $choice->attribute_id]);
                } else {
                    $str .= str_replace(' ', '', $request['attribute_id_' . $choice->attribute_id]);
                }
            }
        }
        // added by arif on 4 june 2024:: start
        /*
        if (json_decode($product->choice_options) != null) {
            foreach (json_decode($product->choice_options) as $key => $choice) {
                if ($str != null) {
                    $str .= '-' . str_replace(' ', '', $request['attribute_id_' . $choice->attribute_id]);
                } else {
                    $str .= str_replace(' ', '', $request['attribute_id_' . $choice->attribute_id]);
                }
            }
        }

        */


        $product_stock = $product->stocks->where('variant', $str)->first();

        // $price = $product_stock->price;
        if (auth()->user() == null) {
            $price = $product_stock->price;
        } else {
            $dropshipper_Profile = B2BProfile::where('user_id', auth()->user()->id)->first();
            if ((auth()->user()->user_type == 'b2b') && ($dropshipper_Profile && $dropshipper_Profile->verification_status == 1)) {
                $price = $product_stock->dropshipper_price;
                if ($product_stock->dropshipper_price != 0) {
                    $dropshipper_price = $product_stock->dropshipper_price;
                } else {
                    // $dropshipper_price = $product_stock->unit_price;
                    $dropshipper_price = $product_stock->price;
                }
                $price = $dropshipper_price;
            } else {
                $price = $product_stock->price;
            }
        }


        if ($product->wholesale_product) {
            $wholesalePrice = $product_stock->wholesalePrices->where('min_qty', '<=', $request->quantity)->where('max_qty', '>=', $request->quantity)->first();
            if ($wholesalePrice) {
                // $price = $wholesalePrice->price;
                if (auth()->user() == null) {
                    $price = $wholesalePrice->price;
                } else {
                    $dropshipper_Profile = B2BProfile::where('user_id', auth()->user()->id)->first();
                    if ((auth()->user()->user_type == 'b2b') && ($dropshipper_Profile && $dropshipper_Profile->verification_status == 1)) {
                        if ($wholesalePrice->dropshipper_price != 0) {
                            $dropshipper_price = $wholesalePrice->dropshipper_price;
                        } else {
                            $dropshipper_price = $wholesalePrice->unit_price;
                        }
                        $price = $dropshipper_price;
                    } else {
                        $price = $wholesalePrice->price;
                    }
                }
            }
        }

        $quantity = $product_stock->qty;
        $max_limit = $product_stock->qty;

        if ($quantity >= 1 && $product->min_qty <= $quantity) {
            $in_stock = 1;
        } else {
            $in_stock = 0;
        }

        //Product Stock Visibility
        if ($product->stock_visibility_state == 'text') {
            if ($quantity >= 1 && $product->min_qty < $quantity) {
                $quantity = translate('In Stock');
            } else {
                $quantity = translate('Out Of Stock');
            }
        }

        //discount calculation
        $discount_applicable = false;

        if ($product->discount_start_date == null) {
            $discount_applicable = true;
        } elseif (
            strtotime(date('d-m-Y H:i:s')) >= $product->discount_start_date &&
            strtotime(date('d-m-Y H:i:s')) <= $product->discount_end_date
        ) {
            $discount_applicable = true;
        }

        if ($discount_applicable) {
            if ($product->discount_type == 'percent') {
                $price -= ($price * $product->discount) / 100;
            } elseif ($product->discount_type == 'amount') {
                $price -= $product->discount;
            }
        }

        // taxes
        foreach ($product->taxes as $product_tax) {
            if ($product_tax->tax_type == 'percent') {
                $tax += ($price * $product_tax->tax) / 100;
            } elseif ($product_tax->tax_type == 'amount') {
                $tax += $product_tax->tax;
            }
        }

        $price += $tax;

        return array(
            'price' => single_price($price * $request->quantity),
            'quantity' => $quantity,
            'digital' => $product->digital,
            'variation' => $str,
            'max_limit' => $max_limit,
            'in_stock' => $in_stock
        );
    }

    public function sellerpolicy()
    {
        $page =  Page::where('type', 'seller_policy_page')->first();
        return view("frontend.policies.sellerpolicy", compact('page'));
    }

    public function returnpolicy()
    {
        $page =  Page::where('type', 'return_policy_page')->first();
        return view("frontend.policies.returnpolicy", compact('page'));
    }

    public function supportpolicy()
    {
        $page =  Page::where('type', 'support_policy_page')->first();
        return view("frontend.policies.supportpolicy", compact('page'));
    }

    public function terms()
    {
        $page =  Page::where('type', 'terms_conditions_page')->first();
        return view("frontend.policies.terms", compact('page'));
    }

    public function privacypolicy()
    {
        $page =  Page::where('type', 'privacy_policy_page')->first();
        return view("frontend.policies.privacypolicy", compact('page'));
    }

    public function get_pick_up_points(Request $request)
    {
        $pick_up_points = PickupPoint::all();
        return view('frontend.partials.pick_up_points', compact('pick_up_points'));
    }

    public function get_category_items(Request $request)
    {
        $category = Category::findOrFail($request->id);
        return view('frontend.partials.category_elements', compact('category'));
    }

    public function premium_package_index()
    {
        $customer_packages = CustomerPackage::all();
        return view('frontend.user.customer_packages_lists', compact('customer_packages'));
    }

    // public function new_page()
    // {
    //     $user = User::where('user_type', 'admin')->first();
    //     auth()->login($user);
    //     return redirect()->route('admin.dashboard');

    // }


    // Ajax call
    public function new_verify(Request $request)
    {
        $email = $request->email;
        if (isUnique($email) == '0') {
            $response['status'] = 2;
            $response['message'] = translate('Email already exists!');
            return json_encode($response);
        }

        $response = $this->send_email_change_verification_mail($request, $email);
        return json_encode($response);
    }


    // Form request
    public function update_email(Request $request)
    {
        $email = $request->email;
        if (isUnique($email)) {
            $this->send_email_change_verification_mail($request, $email);
            flash(translate('A verification mail has been sent to the mail you provided us with.'))->success();
            return back();
        }

        flash(translate('Email already exists!'))->warning();
        return back();
    }

    public function send_email_change_verification_mail($request, $email)
    {
        $response['status'] = 0;
        $response['message'] = 'Unknown';

        $verification_code = Str::random(32);

        $array['subject'] = translate('Email Verification');
        $array['from'] = env('MAIL_FROM_ADDRESS');
        $array['content'] = translate('Verify your account');
        $array['link'] = route('email_change.callback') . '?new_email_verificiation_code=' . $verification_code . '&email=' . $email;
        $array['sender'] = Auth::user()->name;
        $array['details'] = translate("Email Second");

        $user = Auth::user();
        $user->new_email_verificiation_code = $verification_code;
        $user->save();

        try {
            Mail::to($email)->queue(new SecondEmailVerifyMailManager($array));

            $response['status'] = 1;
            $response['message'] = translate("Your verification mail has been Sent to your email.");
        } catch (\Exception $e) {
            // return $e->getMessage();
            $response['status'] = 0;
            $response['message'] = $e->getMessage();
        }

        return $response;
    }

    public function email_change_callback(Request $request)
    {
        if ($request->has('new_email_verificiation_code') && $request->has('email')) {
            $verification_code_of_url_param =  $request->input('new_email_verificiation_code');
            $user = User::where('new_email_verificiation_code', $verification_code_of_url_param)->first();

            if ($user != null) {

                $user->email = $request->input('email');
                $user->new_email_verificiation_code = null;
                $user->save();

                auth()->login($user, true);

                flash(translate('Email Changed successfully'))->success();
                if ($user->user_type == 'seller') {
                    return redirect()->route('seller.dashboard');
                }
                return redirect()->route('dashboard');
            }
        }

        flash(translate('Email was not verified. Please resend your mail!'))->error();
        return redirect()->route('dashboard');
    }

    public function otp_password_request(Request $request)
    {
        return view('emails.otp_email');
    }

    public function otp_password_email(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'email' => 'required|email|exists:users,email',
        ], [
            'email.required' => 'Please enter your email address.',
            'email.email' => 'The email address must be in a valid format.',
            'email.exists' => 'We couldn’t find an account with that email address.',
        ]);

        // Check if validation fails
        if ($validator->fails()) {
            flash(translate('The mail is not available or Invalid Email'))->error();
            return redirect()->back();
        }
        $user = User::where('email', $request->email)->first();

        /*if (!$user) {
            flash(translate('Email was not verified. Please resend your mail!'))->error();
            return redirect()->back();
        }*/

        DB::table('password_resets')->where('email', $request->email)->delete();

        $token = Str::random(64);

        // Store the token in the password_resets table
        DB::table('password_resets')->insert([
            'email' => $request->email,
            'token' => $token, // Hash the token for security
            'created_at' => Carbon::now(),
        ]);

        $user->notify(new OTPRestPasswordNotification());
        flash(translate('OTP send successful. Please verify your email.'))->success();
        // Redirect to the reset password form with the token
        return redirect()->route('password.reset', ['token' => $token, 'email' => $request->email]);
    }

    public function otp_password_request_form($token, $email)
    {
        $token_exist = \App\Models\PasswordReset::where('token', $token)->where('email', $email)->first();
        if (!$token_exist) {
            flash(translate('Invalid Token!'))->error();
            return redirect()->back();
        }
        return view('emails.waiting_for_verification', compact('token', 'email'));
    }


    public function verify_otp_reset_password(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|exists:users,email',
            'verification_code' => 'required|exists:users,verification_code',
            'password' => 'required|string|min:6|confirmed',
            'password_confirmation' => 'required'
        ]);

        // Check if validation fails
        if ($validator->fails()) {
            flash(translate('Please fill all the required fields'))->error();
            foreach ($validator->errors()->all() as $key => $value) {
                flash(translate($value))->error();
            }
            return redirect()->back()->withInput() // This retains the input data, such as form fields
                ->with(['email' => $request->email, 'token' => $request->token]);;
        }
        $token_exist = \App\Models\PasswordReset::where('token', $request->token)->where('email', $request->email)->first();
        if (!$token_exist) {
            flash(translate('Invalid Token!'))->error();
            return redirect()->back()->withInput() // This retains the input data, such as form fields
                ->with(['email' => $request->email, 'token' => $request->token]);;
        }

        $user = User::where('verification_code', $request->verification_code)->where('email', $request->email)->first();
        if (!$user) {
            flash(translate('Invalid verification code'))->error();
            return redirect()->back()->withInput() // This retains the input data, such as form fields
                ->with(['email' => $request->email, 'token' => $request->token]);;
        }

        $userTokenExpiry = Carbon::parse($user->verification_token_expire_at);
        if ($userTokenExpiry->isPast()) {
            flash(translate('Verification Code Expired.Please Click on Resent OTP Email !'))->error();
            return redirect()->back()->withInput() // This retains the input data, such as form fields
                ->with(['email' => $request->email, 'token' => $request->token]);;
        }
        DB::table('password_resets')->where(['email' => $request->email])->delete();


        if ($user->user_type == 'guest') {
            $user->user_type = 'customer';
        }
        $user->password = Hash::make($request->password);
        $user->verification_token_expire_at = null;
        $user->email_verified_at = Carbon::now();
        $user->verification_code = null;
        $user->save();
        event(new PasswordReset($user));
        auth()->login($user, true);
        flash(translate('Password updated successfully'))->success();

        if (auth()->user()->user_type == 'admin' || auth()->user()->user_type == 'staff') {
            return redirect()->route('admin.dashboard');
        }
        return redirect()->route('home');
    }

    public function reset_password_with_code(Request $request)
    {

        dd('dd');
        if (($user = User::where('email', $request->email)->where('verification_code', $request->code)->first()) != null) {
            if ($request->password == $request->password_confirmation) {
                $user->password = Hash::make($request->password);
                $user->email_verified_at = date('Y-m-d h:m:s');
                $user->save();
                event(new PasswordReset($user));
                auth()->login($user, true);

                flash(translate('Password updated successfully'))->success();

                if (auth()->user()->user_type == 'admin' || auth()->user()->user_type == 'staff') {
                    return redirect()->route('admin.dashboard');
                }
                return redirect()->route('home');
            } else {
                flash(translate("Password and confirm password didn't match"))->warning();
                return view('auth.passwords.reset');
            }
        } else {
            flash(translate("Verification code mismatch"))->error();
            return view('auth.passwords.reset');
        }
    }


    public function all_flash_deals()
    {
        $today = strtotime(date('Y-m-d H:i:s'));

        $data['all_flash_deals'] = FlashDeal::where('status', 1)
            ->where('start_date', "<=", $today)
            ->where('end_date', ">", $today)
            ->orderBy('created_at', 'desc')
            ->get();

        return view("frontend.flash_deal.all_flash_deal_list", $data);
    }

    public function todays_deal()
    {
        $todays_deal_products = Cache::rememberForever('todays_deal_products', function () {
            return filter_products(Product::with('thumbnail')->where('todays_deal', '1'))->get();
        });

        return view("frontend.todays_deal", compact('todays_deal_products'));
    }

    public function all_seller(Request $request)
    {
        $shops = Shop::whereIn('user_id', verified_sellers_id())
            ->paginate(15);

        return view('frontend.shop_listing', compact('shops'));
    }

    public function all_coupons(Request $request)
    {
        $coupons = Coupon::where('start_date', '<=', strtotime(date('d-m-Y')))->where('end_date', '>=', strtotime(date('d-m-Y')))->paginate(15);
        return view('frontend.coupons', compact('coupons'));
    }

    public function inhouse_products(Request $request)
    {
        $products = filter_products(Product::where('added_by', 'admin'))->with('taxes')->paginate(12)->appends(request()->query());
        return view('frontend.inhouse_products', compact('products'));
    }

    public function loadRelatedProducts(Request $request)
    {
        $page = $request->input('page', 1);
        $productId = $request->input('productId');

        $detailedProduct = Product::find($productId);

        if (!$detailedProduct) {
            return response()->json(['error' => 'Product not found'], 404);
        }

        $relatedProducts = get_new_more_related_products($detailedProduct)->withPath(url()->current() . '?productId=' . $productId);
        $view = '';
        foreach ($relatedProducts as $relatedProduct) {
            $view .= view('frontend.new-product-details.related_products', compact('relatedProduct'))->render();
        }


        return response()->json([
            'html' => $view,
            'maxPages' => $relatedProducts->lastPage(),
        ]);
    }

    public function loadFeaturedProducts(Request $request)
    {
        $page = $request->input('page', 1);
        $productId = $request->input('productId');

        $detailedProduct = Product::find($productId);

        if (!$detailedProduct) {
            return response()->json(['error' => 'Product not found'], 404);
        }

        $relatedProducts = get_new_more_featured_products($detailedProduct)->withPath(url()->current() . '?productId=' . $productId);

        $view = '';
        foreach ($relatedProducts as $popular_product) {
            $view .= view('frontend.new-product-details.top-sale-products', compact('popular_product'))->render();
        }

        return response()->json([
            'html' => $view,
            'maxPages' => $relatedProducts->lastPage(),
        ]);
    }

    public function loadBestSellers(Request $request)
    {
        $page = $request->input('page', 1);
        $productId = $request->input('productId');

        $detailedProduct = Product::find($productId);

        if (!$detailedProduct) {
            return response()->json(['error' => 'Product not found'], 404);
        }

        $relatedProducts = get_new_best_selling_products($detailedProduct, 5)->withPath(url()->current() . '?productId=' . $productId);
        $view = '';
        foreach ($relatedProducts as $relatedProduct) {
            $view .= view('frontend.new-product-details.related_products', compact('relatedProduct'))->render();
        }


        return response()->json([
            'html' => $view,
            'maxPages' => $relatedProducts->lastPage(),
        ]);
    }

    public function loadMoreReviews(Request $request, $id)
    {
        $current_count = $request->input('current_count');
        $detailedProduct = Product::findOrFail($id);

        $reviews = $detailedProduct->reviews()
            ->with('user')
            ->skip($current_count)
            ->take(5)
            ->get();

        return view('partials.reviews', compact('reviews'));
    }
}
