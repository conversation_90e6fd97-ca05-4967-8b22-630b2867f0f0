<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\RefundRequest;
use App\Notifications\order\DeliveredEmailNotification;
use App\Notifications\order\OrderCancelRequestApproved;
use App\Notifications\order\OrderCancelRequestRejected;
use App\Notifications\order\OrderCancelRequestSendNotification;
use App\Services\ActivityLogService;
use Illuminate\Http\Request;
use App\Models\OrderCancel;
use Auth;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class OrderCancelController extends Controller
{
    protected $activityLogService;

    public function __construct(ActivityLogService $activityLogService)
    {
        $this->activityLogService = $activityLogService;
    }
    public function admin_order_cancel_request_all(){
        $order_cancel_requests = OrderCancel::latest()->paginate(10);
        return view('backend.order_cancel.admin_order_cancel_request_all', compact('order_cancel_requests'));
    }
    public function customer_request_index(){
        $order_cancel_requests = OrderCancel::where('user_id', Auth::user()->id)->latest()->paginate(10);
        return view('frontend.order_cancel.customer_request_index', compact('order_cancel_requests'));
    }
    public function dropshipper_request_index(){
        $order_cancel_requests = OrderCancel::where('user_id', Auth::user()->id)->latest()->paginate(10);
        return view('frontend.order_cancel.dropshipper_request_index', compact('order_cancel_requests'));
    }
    public function dropshipper_cancel_order_form($id){
        $order = Order::findOrFail(decrypt($id));
        return view('frontend.order_cancel.dropshipper_cancel_order_form', compact('order'));
    }
    public function customer_cancel_order_form($id)
    {
        $order = Order::findOrFail(decrypt($id));
        return view('frontend.order_cancel.customer_cancel_order_form', compact('order'));
    }
    public function store(Request $request){
        $validator = Validator::make($request->all(), [
            'id' => ['required', function ($attribute, $value, $fail) {
                try {
                    $decryptedId = decrypt($value);
                    if (!\DB::table('orders')->where('id', $decryptedId)->exists()) {
                        $fail('The selected ' . $attribute . ' is invalid.');
                    }
                } catch (\Exception $e) {
                    $fail('The selected ' . $attribute . ' is invalid.');
                }
            }],
            'reason' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        if(Auth::user()->user_type=='customer'){
            $return_route='purchase_history.index';
        }else{
            $return_route='dropshipper.orders.index';
        }


        $decryptedId = decrypt($request->id);

        $orderCancel = OrderCancel::where('order_id', $decryptedId)->first();
        if($orderCancel){
            $message = translate("You have already requested to cancel this order.");
            flash($message)->error();
            return redirect()->route($return_route);
        }


        $order = Order::findOrFail($decryptedId);

        $orderCancel = new OrderCancel();
        $orderCancel->user_id = Auth::user()->id;
        $orderCancel->order_id = $order->id;
        $orderCancel->seller_id = 0;
        $orderCancel->seller_approval = 0;
        $orderCancel->admin_approval = 0;
        $orderCancel->cancel_amount = $request->cancel_amount;
        $orderCancel->reason = $request->reason;
        $orderCancel->admin_seen = 0;
        $orderCancel->status = 0;
        $orderCancel->reject_reason = Null;
        $orderCancel->save();
        try {
            $array = array();
            $array['order_code'] = $order->code;
            $array['user_name'] = $order->user->name;
            $array['subject'] = translate('Confirmation of Your Order Cancellation Request ') . ' - ' . $order->code ;
            $order->user->notify(new OrderCancelRequestSendNotification($array));
            $command = 'php ' . base_path() . '/artisan queue:work --verbose --tries=1 --timeout=220 > /dev/null &';
            exec($command);
        } catch (\Exception $e) {
            Log::channel('email_logs')->error('Error occurred while sending OrderCancelRequestSendNotification email in OrderCancelController : ' . $e->getMessage());
        }
        $message = translate("Order Cancel successfully.");
        flash($message)->success();
        return redirect()->route($return_route);
    }
    function order_cancel_status_update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required',
            'status' => 'required'
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            $orderCancel=OrderCancel::findOrFail($request->id);
            $orderCancel->status = $request->status;
            $orderCancel->admin_approval = $request->status;
            $orderCancel->admin_id = Auth::user()->id;;
            $orderCancel->approve_reason = $request->approve_reason;
            $orderCancel->reject_reason = $request->reject_reason;
            $orderCancel->save();

            $order = Order::findOrFail($orderCancel->order_id);
            $oldStatus = $order->delivery_status;
            $array = array();
            if($request->status==1){

                $order->delivery_status = 'cancelled';
                $order->save();


                $order_detail = OrderDetail::where('id', $order->id)->first();
                $order_detail->cancel_reason = 12;
                $order_detail->save();


                $this->activityLogService->log(
                    'order_status_changed',
                    'Customer requested to cancel the order',
                    $order->id,
                    Order::class,
                    auth()->user()->id,
                    get_class(auth()->user()),
                    $oldStatus,
                    $order->delivery_status,
                    null,
                    email_end_time: null,
                );

                $refund = new RefundRequest;
                $refund->user_id = $orderCancel->user_id;
                $refund->order_id =$order->id;
                $refund->order_detail_id = 0;
                $refund->seller_id =0;
                $refund->seller_approval = 0;
                $refund->reason = 'Customer requested to cancel the order';
                $refund->admin_approval = 0;
                $refund->admin_seen = 0;
                $refund->refund_amount = $order->grand_total;
                $refund->refund_status = 0;
                $refund->save();

                $array['order_code'] = $order->code;
                $array['user_name'] = $order->user->name;
                $array['subject'] = translate('Your Order Cancellation Request Has Been Approved  ') . ' - ' . $order->code ;
                $order->user->notify(new OrderCancelRequestApproved($array));
            }else{
                $array['order_code'] = $order->code;
                $array['user_name'] = $order->user->name;
                $array['reject_reason'] = $request->reject_reason;;
                $array['subject'] = translate(' Update on Your Order Cancellation Request ') . ' - ' . $order->code ;
                $order->user->notify(new OrderCancelRequestRejected($array));
            }

            $command = 'php ' . base_path() . '/artisan queue:work --verbose --tries=1 --timeout=220 > /dev/null &';
            exec($command);
        } catch (\Exception $e) {
            Log::channel('email_logs')->error('Error occurred while sending OrderCancelRequestSendNotification email in OrderCancelController : ' . $e->getMessage());
        }
        $message = translate("Order Cancel successfully.");
        flash($message)->success();
        return redirect()->back();
    }
}
