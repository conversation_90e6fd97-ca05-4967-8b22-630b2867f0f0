<?php

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\CombinedOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Http;

class PayDunyaController extends Controller
{
    /**
     * PayDunya configuration
     */
    protected function getPayDunyaConfig()
    {
        return [
            'master_key' => env('PAYDUNYA_MASTER_KEY'),
            'private_key' => env('PAYDUNYA_PRIVATE_KEY'),
            'public_key' => env('PAYDUNYA_PUBLIC_KEY'),
            'token' => env('PAYDUNYA_TOKEN'),
            'sandbox' => env('PAYDUNYA_SANDBOX', true),
            'api_url' => env('PAYDUNYA_SANDBOX', true) 
                ? 'https://app.paydunya.com/sandbox-api/v1/' 
                : 'https://app.paydunya.com/api/v1/',
        ];
    }

    /**
     * Initiate PayDunya payment
     */
    public function pay(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'payment_type' => 'required|string|in:cart_payment,wallet_payment,seller_package_payment',
                'combined_order_id' => 'required|integer',
                'amount' => 'required|numeric|min:0.01',
                'user_id' => 'required|integer|exists:users,id',
                'package_id' => 'nullable|integer'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'result' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $payment_type = $request->payment_type;
            $combined_order_id = $request->combined_order_id;
            $amount = $request->amount;
            $user_id = $request->user_id;
            $package_id = $request->package_id ?? 0;

            $user = User::find($user_id);
            if (!$user) {
                return response()->json([
                    'result' => false,
                    'message' => 'User not found'
                ], 404);
            }

            // Calculate amount based on payment type
            if ($payment_type == 'cart_payment') {
                $combined_order = CombinedOrder::find($combined_order_id);
                if (!$combined_order) {
                    return response()->json([
                        'result' => false,
                        'message' => 'Order not found'
                    ], 404);
                }
                $amount = floatval($combined_order->grand_total);
            }

            $config = $this->getPayDunyaConfig();
            $invoice_token = 'PAYDUNYA_' . $payment_type . '_' . time() . '_' . rand(1000, 9999);

            // Prepare PayDunya payment request
            $payment_data = [
                'invoice' => [
                    'total_amount' => $amount,
                    'description' => 'Payment for ' . $payment_type . ' #' . $combined_order_id,
                ],
                'store' => [
                    'name' => get_setting('site_name') ?? 'Buzfi',
                    'tagline' => 'Online Payment',
                    'phone' => get_setting('contact_phone') ?? '',
                    'postal_address' => get_setting('contact_address') ?? '',
                    'logo_url' => uploaded_asset(get_setting('site_logo')),
                ],
                'custom_data' => [
                    'payment_type' => $payment_type,
                    'combined_order_id' => $combined_order_id,
                    'user_id' => $user_id,
                    'package_id' => $package_id,
                ],
                'actions' => [
                    'cancel_url' => route('paydunya.cancel'),
                    'return_url' => route('paydunya.success', [
                        'payment_type' => $payment_type,
                        'combined_order_id' => $combined_order_id,
                        'user_id' => $user_id,
                        'package_id' => $package_id,
                    ]),
                    'callback_url' => route('paydunya.callback'),
                ]
            ];

            // Make API call to PayDunya
            $response = Http::withHeaders([
                'PAYDUNYA-MASTER-KEY' => $config['master_key'],
                'PAYDUNYA-PRIVATE-KEY' => $config['private_key'],
                'PAYDUNYA-TOKEN' => $config['token'],
                'Content-Type' => 'application/json',
            ])->post($config['api_url'] . 'checkout-invoice/create', $payment_data);

            if ($response->successful()) {
                $result = $response->json();
                
                if (isset($result['response_code']) && $result['response_code'] == '00') {
                    $payment_url = $result['response_text'];
                    $invoice_token = $result['token'];

                    Log::info('PayDunya payment initiated', [
                        'user_id' => $user_id,
                        'payment_type' => $payment_type,
                        'amount' => $amount,
                        'invoice_token' => $invoice_token
                    ]);

                    return response()->json([
                        'result' => true,
                        'payment_url' => $payment_url,
                        'invoice_token' => $invoice_token,
                        'message' => 'Payment initiated successfully'
                    ]);
                } else {
                    throw new \Exception('PayDunya API error: ' . ($result['response_text'] ?? 'Unknown error'));
                }
            } else {
                throw new \Exception('PayDunya API request failed: ' . $response->body());
            }

        } catch (\Exception $e) {
            Log::error('PayDunya payment initiation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'result' => false,
                'message' => 'Payment initiation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle PayDunya payment success
     */
    public function success(Request $request)
    {
        try {
            Log::info('PayDunya payment success callback', $request->all());

            $token = $request->token;
            if (!$token) {
                return response()->json([
                    'result' => false,
                    'message' => 'Invalid payment token'
                ], 400);
            }

            // Verify payment status with PayDunya
            $config = $this->getPayDunyaConfig();
            $response = Http::withHeaders([
                'PAYDUNYA-MASTER-KEY' => $config['master_key'],
                'PAYDUNYA-PRIVATE-KEY' => $config['private_key'],
                'PAYDUNYA-TOKEN' => $config['token'],
                'Content-Type' => 'application/json',
            ])->get($config['api_url'] . 'checkout-invoice/confirm/' . $token);

            if ($response->successful()) {
                $result = $response->json();
                
                if (isset($result['response_code']) && $result['response_code'] == '00') {
                    $payment_type = $request->payment_type;
                    $user_id = $request->user_id;
                    $combined_order_id = $request->combined_order_id;
                    $package_id = $request->package_id;

                    Log::info('PayDunya payment verified successfully', [
                        'payment_type' => $payment_type,
                        'user_id' => $user_id,
                        'token' => $token,
                        'amount' => $result['invoice']['total_amount'] ?? 0
                    ]);

                    // Process payment based on type
                    switch ($payment_type) {
                        case 'cart_payment':
                            if (function_exists('checkout_done') && $combined_order_id) {
                                checkout_done($combined_order_id, json_encode($result));
                            }
                            break;

                        case 'wallet_payment':
                            if (function_exists('wallet_payment_done') && $user_id) {
                                $amount = $result['invoice']['total_amount'] ?? 0;
                                wallet_payment_done($user_id, $amount, 'PayDunya', json_encode($result));
                            }
                            break;

                        case 'seller_package_payment':
                            if (function_exists('seller_purchase_payment_done') && $user_id && $package_id) {
                                $amount = $result['invoice']['total_amount'] ?? 0;
                                seller_purchase_payment_done($user_id, $package_id, $amount, 'PayDunya', json_encode($result));
                            }
                            break;
                    }

                    return response()->json([
                        'result' => true,
                        'message' => translate('Payment is successful'),
                        'transaction_id' => $result['invoice']['receipt_url'] ?? '',
                        'payment_type' => $payment_type
                    ]);

                } else {
                    Log::warning('PayDunya payment verification failed', [
                        'token' => $token,
                        'response' => $result
                    ]);

                    return response()->json([
                        'result' => false,
                        'message' => translate('Payment verification failed'),
                        'error' => $result['response_text'] ?? 'Unknown error'
                    ], 400);
                }
            } else {
                throw new \Exception('PayDunya verification API request failed: ' . $response->body());
            }

        } catch (\Exception $e) {
            Log::error('PayDunya payment success handling failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'result' => false,
                'message' => 'Payment verification failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle PayDunya payment cancellation
     */
    public function cancel(Request $request)
    {
        try {
            Log::info('PayDunya payment cancelled', $request->all());

            return response()->json([
                'result' => false,
                'message' => translate('Payment was cancelled'),
                'token' => $request->token ?? ''
            ], 400);

        } catch (\Exception $e) {
            Log::error('PayDunya payment cancellation handling failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'result' => false,
                'message' => 'Payment cancellation handling failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle PayDunya webhook callback
     */
    public function callback(Request $request)
    {
        try {
            Log::info('PayDunya webhook callback received', $request->all());

            $data = $request->all();
            $token = $data['data']['token'] ?? null;

            if (!$token) {
                return response()->json([
                    'result' => false,
                    'message' => 'Invalid callback data'
                ], 400);
            }

            // Verify webhook signature if available
            // PayDunya webhook verification implementation would go here

            Log::info('PayDunya webhook processed', [
                'token' => $token,
                'status' => $data['data']['status'] ?? 'unknown'
            ]);

            return response()->json([
                'result' => true,
                'message' => 'Webhook processed successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('PayDunya webhook processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'result' => false,
                'message' => 'Webhook processing failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment status by token
     */
    public function getPaymentStatus(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'token' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'result' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $config = $this->getPayDunyaConfig();
            $response = Http::withHeaders([
                'PAYDUNYA-MASTER-KEY' => $config['master_key'],
                'PAYDUNYA-PRIVATE-KEY' => $config['private_key'],
                'PAYDUNYA-TOKEN' => $config['token'],
                'Content-Type' => 'application/json',
            ])->get($config['api_url'] . 'checkout-invoice/confirm/' . $request->token);

            if ($response->successful()) {
                return response()->json([
                    'result' => true,
                    'data' => $response->json()
                ]);
            } else {
                throw new \Exception('PayDunya status check API request failed: ' . $response->body());
            }

        } catch (\Exception $e) {
            Log::error('PayDunya payment status check failed', [
                'error' => $e->getMessage(),
                'token' => $request->token ?? 'N/A'
            ]);

            return response()->json([
                'result' => false,
                'message' => 'Status check failed: ' . $e->getMessage()
            ], 500);
        }
    }
} 