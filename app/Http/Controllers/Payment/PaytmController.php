<?php

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\CombinedOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use PaytmWallet;

class PaytmController extends Controller
{
    /**
     * Initiate Paytm payment
     */
    public function pay(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'payment_type' => 'required|string|in:cart_payment,wallet_payment,seller_package_payment',
                'combined_order_id' => 'required|integer',
                'amount' => 'required|numeric|min:0.01',
                'user_id' => 'required|integer|exists:users,id',
                'package_id' => 'nullable|integer'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'result' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $payment_type = $request->payment_type;
            $combined_order_id = $request->combined_order_id;
            $amount = $request->amount;
            $user_id = $request->user_id;
            $package_id = $request->package_id ?? 0;

            $user = User::find($user_id);
            if (!$user) {
                return response()->json([
                    'result' => false,
                    'message' => 'User not found'
                ], 404);
            }

            // Calculate amount based on payment type
            if ($payment_type == 'cart_payment') {
                $combined_order = CombinedOrder::find($combined_order_id);
                if (!$combined_order) {
                    return response()->json([
                        'result' => false,
                        'message' => 'Order not found'
                    ], 404);
                }
                $amount = floatval($combined_order->grand_total);
            }

            // Prepare Paytm payment
            $payment = PaytmWallet::with('receive');
            $payment_data = [
                'order' => 'PAYTM_' . $payment_type . '_' . time() . '_' . rand(1000, 9999),
                'user' => $user->id,
                'mobile_number' => $user->phone ?? '',
                'email' => $user->email,
                'amount' => $amount,
                'callback_url' => route('paytm.callback', [
                    "payment_type" => $payment_type,
                    "combined_order_id" => $combined_order_id,
                    "amount" => $amount,
                    "user_id" => $user_id,
                    "package_id" => $package_id,
                ])
            ];

            $payment->prepare($payment_data);

            Log::info('Paytm payment initiated', [
                'user_id' => $user_id,
                'payment_type' => $payment_type,
                'amount' => $amount,
                'order_id' => $payment_data['order']
            ]);

            return $payment->receive();

        } catch (\Exception $e) {
            Log::error('Paytm payment initiation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'result' => false,
                'message' => 'Payment initiation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle Paytm payment callback
     */
    public function callback(Request $request)
    {
        try {
            Log::info('Paytm callback received', $request->all());

            $transaction = PaytmWallet::with('receive');
            $response = $transaction->response();

            if ($transaction->isSuccessful()) {
                $payment_type = $request->payment_type;
                $user_id = $request->user_id;
                $amount = $request->amount;
                $combined_order_id = $request->combined_order_id;
                $package_id = $request->package_id;

                Log::info('Paytm payment successful', [
                    'payment_type' => $payment_type,
                    'user_id' => $user_id,
                    'amount' => $amount,
                    'response' => $response
                ]);

                // Process payment based on type
                switch ($payment_type) {
                    case 'cart_payment':
                        if (function_exists('checkout_done')) {
                            checkout_done($combined_order_id, json_encode($response));
                        }
                        break;

                    case 'wallet_payment':
                        if (function_exists('wallet_payment_done')) {
                            wallet_payment_done($user_id, $amount, 'Paytm', json_encode($response));
                        }
                        break;

                    case 'seller_package_payment':
                        if (function_exists('seller_purchase_payment_done')) {
                            seller_purchase_payment_done($user_id, $package_id, $amount, 'Paytm', json_encode($response));
                        }
                        break;
                }

                return response()->json([
                    'result' => true,
                    'message' => translate("Payment is successful"),
                    'transaction_id' => $response['TXNID'] ?? '',
                    'payment_type' => $payment_type
                ]);

            } else {
                Log::warning('Paytm payment failed', [
                    'response' => $response,
                    'request_data' => $request->all()
                ]);

                return response()->json([
                    'result' => false,
                    'message' => translate("Payment failed"),
                    'response' => $response
                ], 400);
            }

        } catch (\Exception $e) {
            Log::error('Paytm callback processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'result' => false,
                'message' => 'Payment processing failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get Paytm payment status
     */
    public function getPaymentStatus(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'order_id' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'result' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // This would typically query Paytm's API for transaction status
            // Implementation depends on Paytm SDK capabilities
            
            return response()->json([
                'result' => true,
                'message' => 'Status check not implemented yet',
                'order_id' => $request->order_id
            ]);

        } catch (\Exception $e) {
            Log::error('Paytm status check failed', [
                'error' => $e->getMessage(),
                'order_id' => $request->order_id ?? 'N/A'
            ]);

            return response()->json([
                'result' => false,
                'message' => 'Status check failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle Paytm refund (if supported)
     */
    public function refund(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'transaction_id' => 'required|string',
                'refund_amount' => 'required|numeric|min:0.01',
                'reason' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'result' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Paytm refund implementation would go here
            // This depends on Paytm SDK refund capabilities
            
            Log::info('Paytm refund requested', [
                'transaction_id' => $request->transaction_id,
                'amount' => $request->refund_amount,
                'reason' => $request->reason
            ]);

            return response()->json([
                'result' => true,
                'message' => 'Refund processing initiated',
                'transaction_id' => $request->transaction_id
            ]);

        } catch (\Exception $e) {
            Log::error('Paytm refund failed', [
                'error' => $e->getMessage(),
                'transaction_id' => $request->transaction_id ?? 'N/A'
            ]);

            return response()->json([
                'result' => false,
                'message' => 'Refund failed: ' . $e->getMessage()
            ], 500);
        }
    }
} 