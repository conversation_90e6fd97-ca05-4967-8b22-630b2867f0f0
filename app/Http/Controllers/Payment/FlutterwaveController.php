<?php

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use App\Models\CombinedOrder;
use App\Models\Order;
use App\Models\User;
use App\Models\CustomerPackagePayment;
use App\Models\SellerPackagePayment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Http;

class FlutterwaveController extends Controller
{
    private $public_key;
    private $secret_key;
    private $encryption_key;
    private $environment;

    public function __construct()
    {
        $this->public_key = get_setting('flutterwave_public_key');
        $this->secret_key = get_setting('flutterwave_secret_key');
        $this->encryption_key = get_setting('flutterwave_encryption_key');
        $this->environment = get_setting('flutterwave_sandbox') == 1 ? 'sandbox' : 'live';
    }

    /**
     * Handle Flutterwave payment callback
     */
    public function callback(Request $request)
    {
        try {
            $transaction_id = $request->transaction_id;
            $tx_ref = $request->tx_ref;

            if (!$transaction_id || !$tx_ref) {
                return $this->handleFailedPayment('Invalid callback parameters');
            }

            // Verify the transaction
            $verification_result = $this->verifyTransaction($transaction_id);

            if ($verification_result['success']) {
                $transaction_data = $verification_result['data'];
                
                // Check if payment was successful
                if ($transaction_data['status'] === 'successful' && $transaction_data['currency'] === 'NGN') {
                    return $this->processSuccessfulPayment($transaction_data, $tx_ref);
                } else {
                    return $this->handleFailedPayment('Payment was not successful');
                }
            } else {
                return $this->handleFailedPayment('Transaction verification failed');
            }

        } catch (\Exception $e) {
            Log::error('Flutterwave callback error: ' . $e->getMessage());
            return $this->handleFailedPayment('Payment processing failed');
        }
    }

    /**
     * Verify transaction with Flutterwave
     */
    private function verifyTransaction($transaction_id)
    {
        try {
            $url = $this->getApiUrl() . "/v3/transactions/{$transaction_id}/verify";

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secret_key,
                'Content-Type' => 'application/json',
            ])->get($url);

            if ($response->successful()) {
                $data = $response->json();
                
                if ($data['status'] === 'success') {
                    return [
                        'success' => true,
                        'data' => $data['data']
                    ];
                }
            }

            return ['success' => false, 'message' => 'Verification failed'];

        } catch (\Exception $e) {
            Log::error('Flutterwave verification error: ' . $e->getMessage());
            return ['success' => false, 'message' => 'Verification request failed'];
        }
    }

    /**
     * Process successful payment
     */
    private function processSuccessfulPayment($transaction_data, $tx_ref)
    {
        try {
            $payment_type = Session::get('payment_type');
            $payment_data = Session::get('payment_data');
            $amount = $transaction_data['amount'];
            
            Log::info('Processing Flutterwave payment: ', [
                'tx_ref' => $tx_ref,
                'amount' => $amount,
                'payment_type' => $payment_type
            ]);

            switch ($payment_type) {
                case 'order_payment':
                    return $this->processOrderPayment($payment_data, $transaction_data);

                case 'wallet_payment':
                    return $this->processWalletPayment($payment_data, $transaction_data);

                case 'seller_package_payment':
                    return $this->processSellerPackagePayment($payment_data, $transaction_data);

                case 'customer_package_payment':
                    return $this->processCustomerPackagePayment($payment_data, $transaction_data);

                default:
                    return $this->handleFailedPayment('Unknown payment type');
            }

        } catch (\Exception $e) {
            Log::error('Flutterwave payment processing error: ' . $e->getMessage());
            return $this->handleFailedPayment('Payment processing failed');
        }
    }

    /**
     * Process order payment
     */
    private function processOrderPayment($payment_data, $transaction_data)
    {
        try {
            $combined_order_id = $payment_data['combined_order_id'] ?? null;
            $combined_order = CombinedOrder::find($combined_order_id);

            if ($combined_order) {
                $combined_order->payment_status = 'paid';
                $combined_order->payment_details = json_encode($transaction_data);
                $combined_order->save();

                // Update individual orders
                foreach ($combined_order->orders as $order) {
                    $order->payment_status = 'paid';
                    $order->payment_details = json_encode($transaction_data);
                    $order->save();
                }

                flash(translate('Payment completed successfully'))->success();
                return redirect()->route('order_confirmed');
            }

            return $this->handleFailedPayment('Order not found');

        } catch (\Exception $e) {
            Log::error('Order payment processing error: ' . $e->getMessage());
            return $this->handleFailedPayment('Order payment processing failed');
        }
    }

    /**
     * Process wallet recharge
     */
    private function processWalletPayment($payment_data, $transaction_data)
    {
        try {
            $user_id = $payment_data['user_id'] ?? null;
            $amount = $transaction_data['amount'];

            if ($user_id) {
                $user = User::find($user_id);
                if ($user) {
                    $user->balance += $amount;
                    $user->save();

                    // Create wallet transaction record
                    \App\Models\Wallet::create([
                        'user_id' => $user_id,
                        'amount' => $amount,
                        'payment_method' => 'Flutterwave',
                        'payment_details' => json_encode($transaction_data)
                    ]);

                    flash(translate('Wallet recharged successfully'))->success();
                    return redirect()->route('wallet.index');
                }
            }

            return $this->handleFailedPayment('User not found');

        } catch (\Exception $e) {
            Log::error('Wallet payment processing error: ' . $e->getMessage());
            return $this->handleFailedPayment('Wallet recharge failed');
        }
    }

    /**
     * Process seller package payment
     */
    private function processSellerPackagePayment($payment_data, $transaction_data)
    {
        try {
            $user_id = $payment_data['user_id'] ?? null;
            $package_id = $payment_data['seller_package_id'] ?? null;
            $amount = $transaction_data['amount'];

            if ($user_id && $package_id) {
                seller_purchase_payment_done($user_id, $package_id, $amount, 'Flutterwave', json_encode($transaction_data));

                flash(translate('Seller package purchased successfully'))->success();
                return redirect()->route('seller.seller_packages_list');
            }

            return $this->handleFailedPayment('Invalid package payment data');

        } catch (\Exception $e) {
            Log::error('Seller package payment processing error: ' . $e->getMessage());
            return $this->handleFailedPayment('Package purchase failed');
        }
    }

    /**
     * Process customer package payment
     */
    private function processCustomerPackagePayment($payment_data, $transaction_data)
    {
        try {
            $user_id = $payment_data['user_id'] ?? null;
            $package_id = $payment_data['customer_package_id'] ?? null;

            if ($user_id && $package_id) {
                customer_purchase_payment_done($user_id, $package_id);

                flash(translate('Customer package purchased successfully'))->success();
                return redirect()->route('customer_packages.index');
            }

            return $this->handleFailedPayment('Invalid package payment data');

        } catch (\Exception $e) {
            Log::error('Customer package payment processing error: ' . $e->getMessage());
            return $this->handleFailedPayment('Package purchase failed');
        }
    }

    /**
     * Handle failed payment
     */
    private function handleFailedPayment($message)
    {
        Log::warning('Flutterwave payment failed: ' . $message);
        flash(translate('Payment failed: ') . $message)->error();
        return redirect()->route('home');
    }

    /**
     * Get API URL based on environment
     */
    private function getApiUrl()
    {
        return $this->environment === 'sandbox' 
            ? 'https://api.flutterwave.com' 
            : 'https://api.flutterwave.com';
    }

    /**
     * Initialize payment (for frontend use)
     */
    public function initializePayment(Request $request)
    {
        try {
            $amount = $request->amount;
            $currency = $request->currency ?? 'NGN';
            $payment_type = $request->payment_type;
            $user = auth()->user();

            if (!$user) {
                return response()->json(['success' => false, 'message' => 'User not authenticated']);
            }

            // Generate transaction reference
            $tx_ref = 'FLW_' . time() . '_' . $user->id;

            // Store payment data in session
            Session::put('payment_type', $payment_type);
            Session::put('payment_data', $request->all());

            $payload = [
                'tx_ref' => $tx_ref,
                'amount' => $amount,
                'currency' => $currency,
                'redirect_url' => route('flutterwave.callback'),
                'customer' => [
                    'email' => $user->email,
                    'phonenumber' => $user->phone ?? '',
                    'name' => $user->name
                ],
                'customizations' => [
                    'title' => get_setting('site_name') ?? 'Payment',
                    'description' => 'Payment for ' . $payment_type,
                    'logo' => asset('assets/img/logo.png')
                ]
            ];

            $url = $this->getApiUrl() . '/v3/payments';

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->secret_key,
                'Content-Type' => 'application/json',
            ])->post($url, $payload);

            if ($response->successful()) {
                $data = $response->json();
                
                if ($data['status'] === 'success') {
                    return response()->json([
                        'success' => true,
                        'payment_url' => $data['data']['link']
                    ]);
                }
            }

            return response()->json(['success' => false, 'message' => 'Payment initialization failed']);

        } catch (\Exception $e) {
            Log::error('Flutterwave initialization error: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Payment initialization failed']);
        }
    }

    /**
     * Get transaction status
     */
    public function getTransactionStatus(Request $request)
    {
        try {
            $transaction_id = $request->transaction_id;
            
            if (!$transaction_id) {
                return response()->json(['success' => false, 'message' => 'Transaction ID required']);
            }

            $verification_result = $this->verifyTransaction($transaction_id);
            
            return response()->json($verification_result);

        } catch (\Exception $e) {
            Log::error('Transaction status check error: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Status check failed']);
        }
    }
} 