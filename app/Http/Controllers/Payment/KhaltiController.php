<?php

namespace App\Http\Controllers\Payment;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\CombinedOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Http;
use Redirect;

class KhaltiController extends Controller
{
    /**
     * Get Khalti API URL based on sandbox setting
     */
    protected function getKhaltiApiUrl($endpoint = 'initiate')
    {
        $base_url = get_setting('khalti_sandbox') == 1 
            ? 'https://a.khalti.com/api/v2/epayment/' 
            : 'https://khalti.com/api/v2/epayment/';
        
        return $base_url . $endpoint . '/';
    }

    /**
     * Initiate Khalti payment
     */
    public function pay(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'payment_type' => 'required|string|in:cart_payment,wallet_payment,seller_package_payment,customer_package_payment',
                'combined_order_id' => 'required_if:payment_type,cart_payment|integer',
                'amount' => 'required|numeric|min:0.01',
                'user_id' => 'required|integer|exists:users,id',
                'package_id' => 'required_if:payment_type,seller_package_payment,customer_package_payment|nullable|integer'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'result' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $payment_type = $request->payment_type;
            $user_id = $request->user_id;
            $amount = $request->amount;
            $package_id = $request->package_id ?? 0;

            // Determine purchase order details based on payment type
            if ($payment_type == 'cart_payment') {
                $combined_order = CombinedOrder::find($request->combined_order_id);
                if (!$combined_order) {
                    return response()->json([
                        'result' => false,
                        'message' => 'Order not found'
                    ], 404);
                }
                $purchase_order_id = $combined_order->id;
                $amount = floatval($combined_order->grand_total);
            } elseif ($payment_type == 'wallet_payment') {
                $purchase_order_id = $user_id;
            } elseif (in_array($payment_type, ['seller_package_payment', 'customer_package_payment'])) {
                $purchase_order_id = $package_id;
            }

            // Verify user exists
            $user = User::find($user_id);
            if (!$user) {
                return response()->json([
                    'result' => false,
                    'message' => 'User not found'
                ], 404);
            }

            // Prepare Khalti payment data
            $payment_data = [
                'return_url' => route('khalti.success', [
                    'payment_type' => $payment_type,
                    'combined_order_id' => $request->combined_order_id ?? 0,
                    'user_id' => $user_id,
                    'package_id' => $package_id,
                    'amount' => $amount
                ]),
                'website_url' => route('home'),
                'amount' => intval($amount * 100), // Convert to paisa (Khalti uses smallest currency unit)
                "modes" => [
                    "KHALTI",
                    "EBANKING",
                    "MOBILE_BANKING",
                    "CONNECT_IPS",
                    "SCT"
                ],
                'purchase_order_id' => $purchase_order_id,
                'purchase_order_name' => $payment_type . '_' . time(),
                'customer_info' => [
                    'name' => $user->name,
                    'email' => $user->email,
                    'phone' => $user->phone ?? ''
                ]
            ];

            // Make API call to Khalti
            $response = Http::withHeaders([
                'Authorization' => 'Key ' . env('KHALTI_SECRET_KEY'),
                'Content-Type' => 'application/json',
            ])->post($this->getKhaltiApiUrl('initiate'), $payment_data);

            if ($response->successful()) {
                $result = $response->json();
                
                if (isset($result['payment_url'])) {
                    Log::info('Khalti payment initiated', [
                        'user_id' => $user_id,
                        'payment_type' => $payment_type,
                        'amount' => $amount,
                        'purchase_order_id' => $purchase_order_id,
                        'pidx' => $result['pidx'] ?? 'N/A'
                    ]);

                    // Return JSON response for API calls or redirect for web
                    if ($request->expectsJson()) {
                        return response()->json([
                            'result' => true,
                            'payment_url' => $result['payment_url'],
                            'pidx' => $result['pidx'] ?? null,
                            'message' => 'Payment initiated successfully'
                        ]);
                    } else {
                        return Redirect::to($result['payment_url']);
                    }
                } else {
                    throw new \Exception('Invalid response from Khalti API: ' . json_encode($result));
                }
            } else {
                throw new \Exception('Khalti API request failed: ' . $response->body());
            }

        } catch (\Exception $e) {
            Log::error('Khalti payment initiation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'result' => false,
                'message' => 'Payment initiation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle Khalti payment completion
     */
    public function paymentDone(Request $request)
    {
        try {
            Log::info('Khalti payment completion check', $request->all());

            $validator = Validator::make($request->all(), [
                'pidx' => 'required|string',
                'payment_type' => 'nullable|string',
                'user_id' => 'nullable|integer',
                'combined_order_id' => 'nullable|integer',
                'package_id' => 'nullable|integer',
                'amount' => 'nullable|numeric'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'result' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $pidx = $request->pidx;

            // Lookup payment status
            $response = Http::withHeaders([
                'Authorization' => 'Key ' . env('KHALTI_SECRET_KEY'),
                'Content-Type' => 'application/json',
            ])->post($this->getKhaltiApiUrl('lookup'), [
                'pidx' => $pidx
            ]);

            if ($response->successful()) {
                $result = $response->json();
                
                if (isset($result['status']) && $result['status'] == 'Completed') {
                    $payment_type = $request->payment_type;
                    $user_id = $request->user_id;
                    $amount = $request->amount;
                    $combined_order_id = $request->combined_order_id;
                    $package_id = $request->package_id;

                    Log::info('Khalti payment successful', [
                        'payment_type' => $payment_type,
                        'user_id' => $user_id,
                        'amount' => $amount,
                        'pidx' => $pidx,
                        'transaction_id' => $result['transaction_id'] ?? 'N/A'
                    ]);

                    // Process payment based on type
                    switch ($payment_type) {
                        case 'cart_payment':
                            if (function_exists('checkout_done') && $combined_order_id) {
                                checkout_done($combined_order_id, json_encode($result));
                            }
                            break;

                        case 'wallet_payment':
                            if (function_exists('wallet_payment_done') && $user_id && $amount) {
                                wallet_payment_done($user_id, $amount, 'Khalti', json_encode($result));
                            }
                            break;

                        case 'seller_package_payment':
                            if (function_exists('seller_purchase_payment_done') && $user_id && $package_id && $amount) {
                                seller_purchase_payment_done($user_id, $package_id, $amount, 'Khalti', json_encode($result));
                            }
                            break;

                        case 'customer_package_payment':
                            if (function_exists('customer_purchase_payment_done') && $user_id && $package_id) {
                                customer_purchase_payment_done($user_id, $package_id);
                            }
                            break;
                    }

                    return response()->json([
                        'result' => true,
                        'message' => translate('Payment is successful'),
                        'transaction_id' => $result['transaction_id'] ?? '',
                        'amount' => $result['total_amount'] ?? 0,
                        'payment_type' => $payment_type
                    ]);

                } else {
                    Log::warning('Khalti payment not completed', [
                        'pidx' => $pidx,
                        'status' => $result['status'] ?? 'Unknown',
                        'response' => $result
                    ]);

                    return response()->json([
                        'result' => false,
                        'message' => translate('Payment is failed'),
                        'status' => $result['status'] ?? 'Unknown'
                    ], 400);
                }
            } else {
                throw new \Exception('Khalti lookup API request failed: ' . $response->body());
            }

        } catch (\Exception $e) {
            Log::error('Khalti payment completion check failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'result' => false,
                'message' => 'Payment verification failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle Khalti payment cancellation
     */
    public function getCancel(Request $request)
    {
        try {
            Log::info('Khalti payment cancelled', $request->all());

            return response()->json([
                'result' => false,
                'message' => translate('Payment was cancelled'),
                'pidx' => $request->pidx ?? ''
            ], 400);

        } catch (\Exception $e) {
            Log::error('Khalti payment cancellation handling failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'result' => false,
                'message' => 'Payment cancellation handling failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get payment status by pidx
     */
    public function getPaymentStatus(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'pidx' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'result' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $response = Http::withHeaders([
                'Authorization' => 'Key ' . env('KHALTI_SECRET_KEY'),
                'Content-Type' => 'application/json',
            ])->post($this->getKhaltiApiUrl('lookup'), [
                'pidx' => $request->pidx
            ]);

            if ($response->successful()) {
                return response()->json([
                    'result' => true,
                    'data' => $response->json()
                ]);
            } else {
                throw new \Exception('Khalti status check API request failed: ' . $response->body());
            }

        } catch (\Exception $e) {
            Log::error('Khalti payment status check failed', [
                'error' => $e->getMessage(),
                'pidx' => $request->pidx ?? 'N/A'
            ]);

            return response()->json([
                'result' => false,
                'message' => 'Status check failed: ' . $e->getMessage()
            ], 500);
        }
    }
} 