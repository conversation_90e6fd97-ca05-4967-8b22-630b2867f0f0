<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use App\Models\Product;
use App\Models\ProductStock;
use App\Models\Category;
use App\Models\Brand;
use App\Models\User;
use App\Models\Address;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\CombinedOrder;
use App\Models\Cart;
use App\Models\Coupon;
use App\Models\CouponUsage;
use App\Models\BusinessSetting;
use App\Enums\OrderStatus;
use App\Services\OrderProcessingService;
use App\Services\ActivityLogService;
use Carbon\Carbon;

class PosController extends Controller
{
    protected $orderProcessingService;
    protected $activityLogService;

    public function __construct(
        OrderProcessingService $orderProcessingService = null,
        ActivityLogService $activityLogService = null
    ) {
        $this->orderProcessingService = $orderProcessingService;
        $this->activityLogService = $activityLogService;
    }

    /**
     * Admin POS Interface
     */
    public function index()
    {
        try {
            $categories = Category::where('level', 0)->get();
            $brands = Brand::all();
            
            return view('backend.pos.index', compact('categories', 'brands'));
        } catch (\Exception $e) {
            Log::error('POS index failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            flash(translate('Error loading POS interface'))->error();
            return redirect()->back();
        }
    }

    /**
     * POS Configuration/Activation
     */
    public function configuration()
    {
        try {
            return view('backend.pos.configuration');
        } catch (\Exception $e) {
            Log::error('POS configuration failed', [
                'error' => $e->getMessage()
            ]);
            
            flash(translate('Error loading POS configuration'))->error();
            return redirect()->back();
        }
    }

    /**
     * Search products for POS
     */
    public function search(Request $request)
    {
        try {
            $keyword = $request->keyword;
            $category = $request->category;
            $brand = $request->brand;

            $products = Product::where('published', 1)
                ->where('approved', 1)
                ->where('auction_product', 0);

            if (!empty($keyword)) {
                $products = $products->where(function($q) use ($keyword) {
                    $q->where('name', 'like', '%' . $keyword . '%')
                      ->orWhere('slug', 'like', '%' . $keyword . '%')
                      ->orWhere('barcode', 'like', '%' . $keyword . '%')
                      ->orWhereHas('product_translations', function($query) use ($keyword) {
                          $query->where('name', 'like', '%' . $keyword . '%');
                      });
                });
            }

            if (!empty($category) && $category != 'all') {
                if (strpos($category, 'category-') === 0) {
                    $category_id = str_replace('category-', '', $category);
                    $products = $products->where('category_id', $category_id);
                }
            }

            if (!empty($brand) && $brand != 'all') {
                $products = $products->where('brand_id', $brand);
            }

            $products = $products->with(['thumbnail', 'product_translations', 'stocks', 'taxes'])
                ->limit(20)
                ->get();

            $html = '';
            foreach ($products as $product) {
                $stock_qty = $product->stocks->sum('qty');
                $price = home_base_price($product);
                
                $html .= '<div class="col-sm-6 col-md-4 col-xl-3 mb-3">
                    <div class="card card-product" onclick="addToCart(' . $product->id . ')">
                        <div class="card-body p-3 text-center">
                            <img src="' . uploaded_asset($product->thumbnail_img) . '" 
                                 class="img-fluid mb-2" style="height: 100px; object-fit: cover;">
                            <h6 class="fw-600 text-truncate">' . $product->getTranslation('name') . '</h6>
                            <p class="opacity-60 text-truncate">' . format_price($price) . '</p>
                            <p class="opacity-50 fs-12">Stock: ' . $stock_qty . '</p>
                        </div>
                    </div>
                </div>';
            }

            return $html;

        } catch (\Exception $e) {
            Log::error('POS product search failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Product search failed'
            ], 500);
        }
    }

    /**
     * Add product to POS cart
     */
    public function addToCart(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'product_id' => 'required|integer|exists:products,id',
                'quantity' => 'integer|min:1',
                'variant' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $product = Product::findOrFail($request->product_id);
            $quantity = $request->quantity ?? 1;
            $variant = $request->variant ?? '';

            // Check stock
            $stock = $this->getProductStock($product, $variant);
            if ($stock < $quantity) {
                return response()->json([
                    'success' => false,
                    'message' => translate('Insufficient stock')
                ], 400);
            }

            // Get or create POS cart session
            $cart = Session::get('pos_cart', []);
            $cart_key = $product->id . '_' . $variant;

            if (isset($cart[$cart_key])) {
                $cart[$cart_key]['quantity'] += $quantity;
            } else {
                $cart[$cart_key] = [
                    'product_id' => $product->id,
                    'quantity' => $quantity,
                    'variant' => $variant,
                    'price' => $this->getProductPrice($product, $variant),
                    'name' => $product->getTranslation('name'),
                    'image' => uploaded_asset($product->thumbnail_img)
                ];
            }

            Session::put('pos_cart', $cart);

            Log::info('POS item added to cart', [
                'product_id' => $product->id,
                'quantity' => $quantity,
                'variant' => $variant
            ]);

            return response()->json([
                'success' => true,
                'message' => translate('Item added to cart'),
                'cart' => $cart,
                'cart_count' => count($cart)
            ]);

        } catch (\Exception $e) {
            Log::error('POS add to cart failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to add item to cart'
            ], 500);
        }
    }

    /**
     * Update quantity in POS cart
     */
    public function updateQuantity(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'cart_key' => 'required|string',
                'quantity' => 'required|integer|min:1'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $cart = Session::get('pos_cart', []);
            $cart_key = $request->cart_key;
            $quantity = $request->quantity;

            if (!isset($cart[$cart_key])) {
                return response()->json([
                    'success' => false,
                    'message' => translate('Item not found in cart')
                ], 404);
            }

            $product = Product::find($cart[$cart_key]['product_id']);
            $variant = $cart[$cart_key]['variant'] ?? '';

            // Check stock
            $stock = $this->getProductStock($product, $variant);
            if ($stock < $quantity) {
                return response()->json([
                    'success' => false,
                    'message' => translate('Insufficient stock')
                ], 400);
            }

            $cart[$cart_key]['quantity'] = $quantity;
            Session::put('pos_cart', $cart);

            return response()->json([
                'success' => true,
                'message' => translate('Cart updated'),
                'cart' => $cart
            ]);

        } catch (\Exception $e) {
            Log::error('POS update quantity failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update cart'
            ], 500);
        }
    }

    /**
     * Remove item from POS cart
     */
    public function removeFromCart(Request $request)
    {
        try {
            $cart = Session::get('pos_cart', []);
            $cart_key = $request->cart_key;

            if (isset($cart[$cart_key])) {
                unset($cart[$cart_key]);
                Session::put('pos_cart', $cart);
            }

            return response()->json([
                'success' => true,
                'message' => translate('Item removed from cart'),
                'cart' => $cart,
                'cart_count' => count($cart)
            ]);

        } catch (\Exception $e) {
            Log::error('POS remove from cart failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to remove item'
            ], 500);
        }
    }

    /**
     * Get shipping address for customer
     */
    public function getShippingAddress(Request $request)
    {
        try {
            $user_id = $request->user_id;
            
            if ($user_id) {
                $user = User::find($user_id);
                if ($user) {
                    $addresses = $user->addresses()->get();
                    return response()->json([
                        'success' => true,
                        'addresses' => $addresses
                    ]);
                }
            }

            return response()->json([
                'success' => false,
                'message' => translate('User not found')
            ], 404);

        } catch (\Exception $e) {
            Log::error('POS get shipping address failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get addresses'
            ], 500);
        }
    }

    /**
     * Get shipping address for seller
     */
    public function getShippingAddressForSeller(Request $request)
    {
        try {
            $user_id = $request->user_id;
            
            if ($user_id) {
                $user = User::find($user_id);
                if ($user && $user->seller) {
                    $shop_address = [
                        'name' => $user->shop->name ?? $user->name,
                        'address' => $user->shop->address ?? $user->address,
                        'city' => $user->city,
                        'phone' => $user->phone,
                        'postal_code' => $user->postal_code
                    ];
                    
                    return response()->json([
                        'success' => true,
                        'address' => $shop_address
                    ]);
                }
            }

            return response()->json([
                'success' => false,
                'message' => translate('Seller not found')
            ], 404);

        } catch (\Exception $e) {
            Log::error('POS get seller shipping address failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get seller address'
            ], 500);
        }
    }

    /**
     * Set discount for POS order
     */
    public function setDiscount(Request $request)
    {
        try {
            $discount_type = $request->discount_type; // 'percent' or 'flat'
            $discount_value = $request->discount_value;

            Session::put('pos_discount', [
                'type' => $discount_type,
                'value' => $discount_value
            ]);

            return response()->json([
                'success' => true,
                'message' => translate('Discount applied'),
                'discount' => Session::get('pos_discount')
            ]);

        } catch (\Exception $e) {
            Log::error('POS set discount failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to set discount'
            ], 500);
        }
    }

    /**
     * Set shipping for POS order
     */
    public function setShipping(Request $request)
    {
        try {
            $shipping_cost = $request->shipping_cost ?? 0;

            Session::put('pos_shipping', $shipping_cost);

            return response()->json([
                'success' => true,
                'message' => translate('Shipping cost updated'),
                'shipping' => $shipping_cost
            ]);

        } catch (\Exception $e) {
            Log::error('POS set shipping failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to set shipping'
            ], 500);
        }
    }

    /**
     * Set shipping address
     */
    public function set_shipping_address(Request $request)
    {
        try {
            $address = [
                'name' => $request->name,
                'email' => $request->email,
                'address' => $request->address,
                'city' => $request->city,
                'postal_code' => $request->postal_code,
                'phone' => $request->phone,
                'country' => $request->country
            ];

            Session::put('pos_shipping_address', $address);

            return response()->json([
                'success' => true,
                'message' => translate('Shipping address updated'),
                'address' => $address
            ]);

        } catch (\Exception $e) {
            Log::error('POS set shipping address failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to set address'
            ], 500);
        }
    }

    /**
     * Get order summary
     */
    public function get_order_summary(Request $request)
    {
        try {
            $cart = Session::get('pos_cart', []);
            $discount = Session::get('pos_discount', ['type' => 'flat', 'value' => 0]);
            $shipping = Session::get('pos_shipping', 0);

            if (empty($cart)) {
                return response()->json([
                    'success' => false,
                    'message' => translate('Cart is empty')
                ], 400);
            }

            $subtotal = 0;
            $total_tax = 0;

            foreach ($cart as $item) {
                $item_total = $item['price'] * $item['quantity'];
                $subtotal += $item_total;

                // Calculate tax if applicable
                $product = Product::find($item['product_id']);
                if ($product && $product->taxes) {
                    foreach ($product->taxes as $tax) {
                        if ($tax->tax_type == 'percent') {
                            $total_tax += ($item_total * $tax->tax) / 100;
                        } else {
                            $total_tax += $tax->tax * $item['quantity'];
                        }
                    }
                }
            }

            // Apply discount
            $discount_amount = 0;
            if ($discount['type'] == 'percent') {
                $discount_amount = ($subtotal * $discount['value']) / 100;
            } else {
                $discount_amount = $discount['value'];
            }

            $total = $subtotal + $total_tax + $shipping - $discount_amount;

            return response()->json([
                'success' => true,
                'summary' => [
                    'subtotal' => $subtotal,
                    'tax' => $total_tax,
                    'shipping' => $shipping,
                    'discount' => $discount_amount,
                    'total' => $total,
                    'cart_count' => count($cart)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('POS order summary failed', [
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to calculate order summary'
            ], 500);
        }
    }

    /**
     * Place POS order
     */
    public function order_store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'payment_method' => 'required|string|in:cash,card,digital_wallet',
                'customer_phone' => 'nullable|string',
                'customer_name' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $cart = Session::get('pos_cart', []);
            if (empty($cart)) {
                return response()->json([
                    'success' => false,
                    'message' => translate('Cart is empty')
                ], 400);
            }

            DB::beginTransaction();

            // Create combined order
            $combined_order = new CombinedOrder();
            $combined_order->user_id = Auth::id();
            $combined_order->shipping_address = json_encode(Session::get('pos_shipping_address', []));
            $combined_order->save();

            // Create order
            $order = new Order();
            $order->combined_order_id = $combined_order->id;
            $order->user_id = Auth::id();
            $order->seller_id = Auth::id();
            $order->code = 'POS-' . date('Ymd') . '-' . rand(1000, 9999);
            $order->date = strtotime('now');
            $order->payment_type = $request->payment_method;
            $order->payment_status = 'paid';
            $order->delivery_status = 'delivered';
            $order->shipping_address = json_encode(Session::get('pos_shipping_address', []));
            $order->order_from = 'pos';
            
            // Calculate totals
            $subtotal = 0;
            $total_tax = 0;
            $discount = Session::get('pos_discount', ['type' => 'flat', 'value' => 0]);
            $shipping = Session::get('pos_shipping', 0);

            foreach ($cart as $item) {
                $item_total = $item['price'] * $item['quantity'];
                $subtotal += $item_total;

                $product = Product::find($item['product_id']);
                if ($product && $product->taxes) {
                    foreach ($product->taxes as $tax) {
                        if ($tax->tax_type == 'percent') {
                            $total_tax += ($item_total * $tax->tax) / 100;
                        } else {
                            $total_tax += $tax->tax * $item['quantity'];
                        }
                    }
                }
            }

            $discount_amount = 0;
            if ($discount['type'] == 'percent') {
                $discount_amount = ($subtotal * $discount['value']) / 100;
            } else {
                $discount_amount = $discount['value'];
            }

            $order->grand_total = $subtotal + $total_tax + $shipping - $discount_amount;
            $order->save();

            // Create order details and update stock
            foreach ($cart as $item) {
                $product = Product::find($item['product_id']);
                
                $order_detail = new OrderDetail();
                $order_detail->order_id = $order->id;
                $order_detail->product_id = $product->id;
                $order_detail->variation = $item['variant'] ?? '';
                $order_detail->price = $item['price'];
                $order_detail->quantity = $item['quantity'];
                $order_detail->save();

                // Update product stock
                if ($product->variant_product == 1 && !empty($item['variant'])) {
                    $product_stock = ProductStock::where('product_id', $product->id)
                        ->where('variant', $item['variant'])
                        ->first();
                    if ($product_stock) {
                        $product_stock->qty -= $item['quantity'];
                        $product_stock->save();
                    }
                } else {
                    $product->current_stock -= $item['quantity'];
                    $product->save();
                }

                // Update sales count
                $product->num_of_sale += $item['quantity'];
                $product->save();
            }

            // Clear POS session data
            Session::forget(['pos_cart', 'pos_discount', 'pos_shipping', 'pos_shipping_address']);

            DB::commit();

            Log::info('POS order created successfully', [
                'order_id' => $order->id,
                'order_code' => $order->code,
                'total' => $order->grand_total
            ]);

            return response()->json([
                'success' => true,
                'message' => translate('Order placed successfully'),
                'order' => [
                    'id' => $order->id,
                    'code' => $order->code,
                    'total' => $order->grand_total
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('POS order creation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to place order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate thermal printer invoice
     */
    public function invoice($order_id)
    {
        try {
            $order = Order::findOrFail($order_id);
            $order_details = OrderDetail::where('order_id', $order->id)
                ->with(['product'])
                ->get();

            return view('backend.pos.thermal_invoice', compact('order', 'order_details'));

        } catch (\Exception $e) {
            Log::error('POS thermal invoice failed', [
                'error' => $e->getMessage(),
                'order_id' => $order_id
            ]);

            flash(translate('Error generating invoice'))->error();
            return redirect()->back();
        }
    }

    /**
     * Helper: Get product stock
     */
    protected function getProductStock($product, $variant = '')
    {
        if ($product->variant_product == 1 && !empty($variant)) {
            $stock = ProductStock::where('product_id', $product->id)
                ->where('variant', $variant)
                ->first();
            return $stock ? $stock->qty : 0;
        }
        
        return $product->current_stock ?? 0;
    }

    /**
     * Helper: Get product price
     */
    protected function getProductPrice($product, $variant = '')
    {
        if ($product->variant_product == 1 && !empty($variant)) {
            $stock = ProductStock::where('product_id', $product->id)
                ->where('variant', $variant)
                ->first();
            return $stock ? $stock->price : $product->unit_price;
        }
        
        return home_base_price($product) ?? $product->unit_price;
    }
} 