<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Messaging\Conversation;
use App\Models\Messaging\ConversationMessage;
use App\Models\Messaging\ConversationParticipant;
use App\Models\Messaging\MessageThread;
use App\Models\Messaging\Message;
use App\Models\Messaging\ThreadParticipant;
use App\Models\User;
use App\Events\Messaging\MessageSent;
use App\Events\Messaging\ThreadUpdated;
use App\Events\Messaging\ConversationMessageSent;
use App\Events\Messaging\ConversationUpdated;
use App\Events\Messaging\UnreadCountUpdated;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class MessagingController extends Controller
{
    /**
     * Display a listing of the threads.
     *
     * @return \Illuminate\Http\Response
     */
    public function threads(Request $request)
    {
        $query = MessageThread::with(['participants.user', 'lastMessage.sender'])
            ->withCount(['messages as unread_count' => function ($query) {
                $query->where('is_read', 0);
            }]);

        // Apply search filter if provided
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('subject', 'like', "%$search%")
                  ->orWhereHas('participants.user', function ($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%$search%")
                               ->orWhere('email', 'like', "%$search%");
                  });
            });
        }

        // Apply status filter
        if ($request->has('status') && !empty($request->status)) {
            $status = $request->status;
            if ($status === 'open') {
                $query->where('is_closed', 0);
            } elseif ($status === 'closed') {
                $query->where('is_closed', 1);
            }
        }

        $threads = $query->orderBy('updated_at', 'desc')
                         ->paginate($request->input('perPage', 15));

        return view('backend.messaging.threads.index', compact('threads'));
    }

    /**
     * Display the thread details.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function showThread($id)
    {
        $thread = MessageThread::with(['participants.user', 'messages.sender', 'messages.attachments'])
            ->findOrFail($id);
            
        // Mark all messages as read for admin
        $this->markThreadAsRead($thread);

        return view('backend.messaging.threads.show', compact('thread'));
    }

    /**
     * Send a reply to a thread.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $threadId
     * @return \Illuminate\Http\Response
     */
    public function replyToThread(Request $request, $threadId)
    {
        $request->validate([
            'content' => 'required|string',
            'attachments.*' => 'nullable|file|max:10240',
        ]);

        $thread = MessageThread::findOrFail($threadId);
        $user = Auth::user();

        // Check if admin is already a participant
        $isParticipant = ThreadParticipant::where('thread_id', $thread->id)
            ->where('user_id', $user->id)
            ->exists();

        // If not, add admin as a participant
        if (!$isParticipant) {
            ThreadParticipant::create([
                'thread_id' => $thread->id,
                'user_id' => $user->id,
                'is_admin' => 1
            ]);
        }

        DB::beginTransaction();
        try {
            // Create the message
            $message = new Message();
            $message->thread_id = $thread->id;
            $message->sender_id = $user->id;
            $message->content = $request->content;
            $message->is_read = 0;
            $message->save();

            // Handle attachments if any
            if ($request->hasFile('attachments')) {
                foreach ($request->file('attachments') as $file) {
                    $path = $file->store('attachments/messaging', 'public');
                    $message->attachments()->create([
                        'file_name' => $file->getClientOriginalName(),
                        'file_path' => $path,
                        'file_type' => $file->getClientMimeType(),
                        'file_size' => $file->getSize()
                    ]);
                }
            }

            // Update thread timestamp
            $thread->touch();
            
            // Mark as read by sender
            DB::table('message_read_status')->insert([
                'message_id' => $message->id,
                'user_id' => $user->id,
                'is_read' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Broadcast message sent event
            broadcast(new MessageSent($message))->toOthers();
            
            // Broadcast thread updated event
            broadcast(new ThreadUpdated($thread))->toOthers();

            // Broadcast unread count updates to all participants except sender
            $participants = ThreadParticipant::where('thread_id', $thread->id)
                ->where('user_id', '!=', $user->id)
                ->get();
                
            foreach ($participants as $participant) {
                $this->broadcastUnreadCountUpdate($participant->user_id);
            }

            DB::commit();
            flash(translate('Message has been sent successfully'))->success();
            return redirect()->back();
        } catch (\Exception $e) {
            DB::rollBack();
            flash(translate('Failed to send message'))->error();
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * Display a listing of the conversations.
     *
     * @return \Illuminate\Http\Response
     */
    public function conversations(Request $request)
    {
        $admin = Auth::user();
        
        $query = Conversation::with(['participants', 'lastMessage.sender']);

        // Apply search filter if provided
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->whereHas('participants.user', function ($userQuery) use ($search) {
                $userQuery->where('name', 'like', "%$search%")
                         ->orWhere('email', 'like', "%$search%");
            });
        }

        $conversations = $query->orderBy('updated_at', 'desc')
                            ->paginate($request->input('perPage', 15));

        return view('backend.messaging.conversations.index', compact('conversations'));
    }

    /**
     * Display the conversation details.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function showConversation($id)
    {
        $conversation = Conversation::with(['participants.user', 'messages.sender', 'messages.attachments'])
            ->findOrFail($id);

        // Mark all messages as read for admin
        $this->markConversationAsRead($conversation);

        // Check if there's a participant other than admin for directing replies
        $otherUser = null;
        foreach ($conversation->participants as $participant) {
            if (!$participant->user->isAdmin()) {
                $otherUser = $participant->user;
                break;
            }
        }

        return view('backend.messaging.conversations.show', compact('conversation', 'otherUser'));
    }

    /**
     * Send a message to a conversation.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $conversationId
     * @return \Illuminate\Http\Response
     */
    public function sendMessage(Request $request, $conversationId)
    {
        $request->validate([
            'content' => 'required|string',
            'attachments.*' => 'nullable|file|max:10240',
        ]);

        $conversation = Conversation::findOrFail($conversationId);
        $user = Auth::user();

        // Check if admin is already a participant
        $isParticipant = ConversationParticipant::where('conversation_id', $conversation->id)
            ->where('user_id', $user->id)
            ->exists();

        // If not, add admin as a participant
        if (!$isParticipant) {
            ConversationParticipant::create([
                'conversation_id' => $conversation->id,
                'user_id' => $user->id
            ]);
        }

        DB::beginTransaction();
        try {
            // Create the message
            $message = new ConversationMessage();
            $message->conversation_id = $conversation->id;
            $message->sender_id = $user->id;
            $message->content = $request->content;
            $message->is_read = 0;
            $message->save();

            // Handle attachments if any
            if ($request->hasFile('attachments')) {
                foreach ($request->file('attachments') as $file) {
                    $path = $file->store('attachments/messaging', 'public');
                    $message->attachments()->create([
                        'file_name' => $file->getClientOriginalName(),
                        'file_path' => $path,
                        'file_type' => $file->getClientMimeType(),
                        'file_size' => $file->getSize()
                    ]);
                }
            }

            // Update conversation timestamp
            $conversation->touch();
            
            // Mark as read by sender
            $message->readStatus()->create([
                'user_id' => $user->id,
                'is_read' => 1
            ]);

            // Broadcast message sent event
            broadcast(new ConversationMessageSent($message, $conversation->id))->toOthers();
            
            // Broadcast conversation updated event
            broadcast(new ConversationUpdated($conversation))->toOthers();

            // Broadcast unread count updates to all participants except sender
            $participants = ConversationParticipant::where('conversation_id', $conversation->id)
                ->where('user_id', '!=', $user->id)
                ->get();
                
            foreach ($participants as $participant) {
                $this->broadcastUnreadCountUpdate($participant->user_id);
            }

            DB::commit();
            flash(translate('Message has been sent successfully'))->success();
            return redirect()->back();
        } catch (\Exception $e) {
            DB::rollBack();
            flash(translate('Failed to send message'))->error();
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * Create a new conversation with a user.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function createConversation(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'content' => 'required|string',
            'attachments.*' => 'nullable|file|max:10240',
        ]);

        $admin = Auth::user();
        $user = User::findOrFail($request->user_id);

        // Check if conversation already exists between these users
        $existingConversation = Conversation::whereHas('participants', function ($query) use ($admin) {
            $query->where('user_id', $admin->id);
        })->whereHas('participants', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })->first();

        DB::beginTransaction();
        try {
            // Create new conversation if it doesn't exist
            if (!$existingConversation) {
                $conversation = new Conversation();
                $conversation->save();
                
                // Add participants
                $conversation->participants()->create(['user_id' => $admin->id]);
                $conversation->participants()->create(['user_id' => $user->id]);
            } else {
                $conversation = $existingConversation;
            }

            // Create message
            $message = new ConversationMessage();
            $message->conversation_id = $conversation->id;
            $message->sender_id = $admin->id;
            $message->content = $request->content;
            $message->is_read = 0;
            $message->save();

            // Handle attachments if any
            if ($request->hasFile('attachments')) {
                foreach ($request->file('attachments') as $file) {
                    $path = $file->store('attachments/messaging', 'public');
                    $message->attachments()->create([
                        'file_name' => $file->getClientOriginalName(),
                        'file_path' => $path,
                        'file_type' => $file->getClientMimeType(),
                        'file_size' => $file->getSize()
                    ]);
                }
            }

            // Update conversation timestamp
            $conversation->touch();
            
            // Mark as read by sender
            $message->readStatus()->create([
                'user_id' => $admin->id,
                'is_read' => 1
            ]);

            // Broadcast events
            broadcast(new ConversationMessageSent($message, $conversation->id))->toOthers();
            broadcast(new ConversationUpdated($conversation))->toOthers();
            $this->broadcastUnreadCountUpdate($user->id);

            DB::commit();
            flash(translate('Conversation started successfully'))->success();
            return redirect()->route('admin.messaging.conversations.show', $conversation->id);
        } catch (\Exception $e) {
            DB::rollBack();
            flash(translate('Failed to start conversation'))->error();
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * Create a new thread.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function createThread(Request $request)
    {
        $request->validate([
            'subject' => 'required|string|max:255',
            'content' => 'required|string',
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'attachments.*' => 'nullable|file|max:10240',
        ]);

        $admin = Auth::user();

        DB::beginTransaction();
        try {
            // Create thread
            $thread = new MessageThread();
            $thread->subject = $request->subject;
            $thread->created_by = $admin->id;
            $thread->is_closed = 0;
            $thread->save();
            
            // Add admin as participant
            ThreadParticipant::create([
                'thread_id' => $thread->id,
                'user_id' => $admin->id,
                'is_admin' => 1
            ]);
            
            // Add other participants
            foreach ($request->user_ids as $userId) {
                if ($userId != $admin->id) {
                    ThreadParticipant::create([
                        'thread_id' => $thread->id,
                        'user_id' => $userId,
                        'is_admin' => 0
                    ]);
                }
            }

            // Create message
            $message = new Message();
            $message->thread_id = $thread->id;
            $message->sender_id = $admin->id;
            $message->content = $request->content;
            $message->is_read = 0;
            $message->save();
            
            // Handle attachments if any
            if ($request->hasFile('attachments')) {
                foreach ($request->file('attachments') as $file) {
                    $path = $file->store('attachments/messaging', 'public');
                    $message->attachments()->create([
                        'file_name' => $file->getClientOriginalName(),
                        'file_path' => $path,
                        'file_type' => $file->getClientMimeType(),
                        'file_size' => $file->getSize()
                    ]);
                }
            }
            
            // Mark as read by sender
            DB::table('message_read_status')->insert([
                'message_id' => $message->id,
                'user_id' => $admin->id,
                'is_read' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ]);

            // Broadcast events
            broadcast(new MessageSent($message))->toOthers();
            broadcast(new ThreadUpdated($thread))->toOthers();
            
            // Broadcast unread count updates to all participants except sender
            foreach ($request->user_ids as $userId) {
                if ($userId != $admin->id) {
                    $this->broadcastUnreadCountUpdate($userId);
                }
            }

            DB::commit();
            flash(translate('Thread created successfully'))->success();
            return redirect()->route('admin.messaging.threads.show', $thread->id);
        } catch (\Exception $e) {
            DB::rollBack();
            flash(translate('Failed to create thread'))->error();
            return redirect()->back()->with('error', $e->getMessage());
        }
    }

    /**
     * Close a thread.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function closeThread($id)
    {
        $thread = MessageThread::findOrFail($id);
        $thread->is_closed = 1;
        $thread->save();
        
        // Broadcast thread updated event
        broadcast(new ThreadUpdated($thread))->toOthers();
        
        flash(translate('Thread has been closed'))->success();
        return redirect()->back();
    }

    /**
     * Reopen a thread.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function reopenThread($id)
    {
        $thread = MessageThread::findOrFail($id);
        $thread->is_closed = 0;
        $thread->save();
        
        // Broadcast thread updated event
        broadcast(new ThreadUpdated($thread))->toOthers();
        
        flash(translate('Thread has been reopened'))->success();
        return redirect()->back();
    }

    /**
     * Mark all messages in a thread as read for admin.
     *
     * @param  \App\Models\Messaging\MessageThread  $thread
     * @return void
     */
    protected function markThreadAsRead($thread)
    {
        $user = Auth::user();
        
        // Get all unread messages in the thread
        $unreadMessages = Message::where('thread_id', $thread->id)
            ->whereDoesntHave('readStatus', function ($query) use ($user) {
                $query->where('user_id', $user->id)
                      ->where('is_read', 1);
            })
            ->get();
            
        // Mark each message as read
        foreach ($unreadMessages as $message) {
            DB::table('message_read_status')->updateOrInsert(
                ['message_id' => $message->id, 'user_id' => $user->id],
                ['is_read' => 1, 'updated_at' => now()]
            );
        }
    }

    /**
     * Mark all messages in a conversation as read for admin.
     *
     * @param  \App\Models\Messaging\Conversation  $conversation
     * @return void
     */
    protected function markConversationAsRead($conversation)
    {
        $user = Auth::user();
        
        // Get all unread messages in the conversation
        $unreadMessages = ConversationMessage::where('conversation_id', $conversation->id)
            ->whereDoesntHave('readStatus', function ($query) use ($user) {
                $query->where('user_id', $user->id)
                      ->where('is_read', 1);
            })
            ->get();
            
        // Mark each message as read
        foreach ($unreadMessages as $message) {
            $message->readStatus()->updateOrCreate(
                ['user_id' => $user->id],
                ['is_read' => 1]
            );
        }
    }

    /**
     * Broadcast unread count update for a user.
     *
     * @param  int  $userId
     * @return void
     */
    private function broadcastUnreadCountUpdate($userId)
    {
        // Count unread thread messages
        $unreadThreadsCount = DB::table('messages')
            ->join('thread_participants', 'messages.thread_id', '=', 'thread_participants.thread_id')
            ->leftJoin('message_read_status', function ($join) use ($userId) {
                $join->on('messages.id', '=', 'message_read_status.message_id')
                    ->where('message_read_status.user_id', '=', $userId);
            })
            ->where('thread_participants.user_id', $userId)
            ->where('messages.sender_id', '!=', $userId)
            ->where(function ($query) {
                $query->whereNull('message_read_status.id')
                    ->orWhere('message_read_status.is_read', 0);
            })
            ->count();
            
        // Count unread conversation messages
        $unreadConversationsCount = DB::table('conversation_messages')
            ->join('conversation_participants', 'conversation_messages.conversation_id', '=', 'conversation_participants.conversation_id')
            ->leftJoin('conversation_message_read_status', function ($join) use ($userId) {
                $join->on('conversation_messages.id', '=', 'conversation_message_read_status.message_id')
                    ->where('conversation_message_read_status.user_id', '=', $userId);
            })
            ->where('conversation_participants.user_id', $userId)
            ->where('conversation_messages.sender_id', '!=', $userId)
            ->where(function ($query) {
                $query->whereNull('conversation_message_read_status.id')
                    ->orWhere('conversation_message_read_status.is_read', 0);
            })
            ->count();
            
        // Prepare counts data
        $counts = [
            'threads' => $unreadThreadsCount,
            'conversations' => $unreadConversationsCount,
            'total' => $unreadThreadsCount + $unreadConversationsCount
        ];
        
        // Broadcast update
        broadcast(new UnreadCountUpdated($userId, $counts));
    }

    /**
     * Get users list for creating new conversations/threads.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function searchUsers(Request $request)
    {
        $search = $request->input('q');
        $users = User::where('name', 'like', "%$search%")
                    ->orWhere('email', 'like', "%$search%")
                    ->select('id', 'name', 'email')
                    ->limit(10)
                    ->get();
                    
        return response()->json($users);
    }
} 