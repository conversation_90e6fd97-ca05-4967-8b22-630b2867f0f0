<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\UserFeedback;

class UserFeedbackController extends Controller
{
    public function index(Request $request){
        $sort_search = null;
        $feedback_query = UserFeedback::orderBy('created_at', 'desc');
        if ($request->has('search')) {
            $sort_search = $request->search;
            $feedback_query = $feedback_query->where('name', 'like', '%' . $sort_search . '%');
        }
        $feedbacks = $feedback_query->paginate(15);
        return view('backend.user_feedback.index', compact('feedbacks', 'sort_search'));
    }
}
