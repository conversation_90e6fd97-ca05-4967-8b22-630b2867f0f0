<?php

namespace App\Http\Controllers\Admin;

use App\Enums\NotificationPriority;
use App\Enums\NotificationType;
use App\Enums\ReturnStatus;
use App\Http\Controllers\Controller;
use App\Models\BusinessSetting;
use App\Models\Order;
use App\Models\RefundRequest;
use App\Models\ReturnRequestInfo;
use App\Notifications\order\return_exchange\ReturnApprovedNotification;
use App\Services\ActivityLogService;
use App\Services\UserNotificationServices;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ReturnRefundController extends Controller
{
    /*
     * Cash
     * Cache::forget('return_faq')
     *
     * */
    protected ActivityLogService $activityLogService;
    protected UserNotificationServices $userNotificationServices;
    public function __construct(ActivityLogService $activityLogService,UserNotificationServices $userNotificationServices)
    {
        $this->activityLogService = $activityLogService;
        $this->userNotificationServices = $userNotificationServices;
    }

    public function index(Request $request){
        $returns = ReturnRequestInfo::query()->latest()->paginate(15);
        return view('backend.return_refund.return_request_list', compact('returns'));
    }
    public function return_request_details(Request $request, $return_code){
        $return_request_info = ReturnRequestInfo::with('return_request_products','order','return_request_products.product','activityLogs')->where('return_code', $return_code)->first();
        return view('backend.return_refund.return_request_details', compact('return_request_info'));
    }
    public function change_return_status(Request $request){
        $request->validate([
            'return_id' => 'required',
            'return_status' => 'required',
            'admin_note' => 'required',
        ]);

        $return_request_info = ReturnRequestInfo::find($request->return_id);
        $old_status = $return_request_info->return_status;
        $return_request_info->admin_note = $request->admin_note;
        $return_request_info->return_status = $request->return_status;
        $return_request_info->save();

        if($request->return_status !== $old_status) {
            $this->activityLogService->log(
                'return_request',
                'Admin updated return request status',
                $return_request_info->id,
                ReturnRequestInfo::class,
                auth()->user()->id,
                get_class(auth()->user()),
                $old_status,
                $request->return_status,
                $request->admin_note,
                email_end_time: null,
            );
        }
        if($request->return_status !=$old_status){
            $this->userNotificationServices->saveNotification(
                auth()->user()->id,
                NotificationType::ORDER->value,
                $return_request_info->id,
                ReturnRequestInfo::class,
                'Update On Return Request',
                'Admin updated return request status to '.$request->return_status,
                NotificationPriority::MEDIUM->value,
                0,
                'v3/refund-request/'.$return_request_info->return_code,
                'see the return request details',
                []
            );
        }
        if($request->return_status == ReturnStatus::COMPLETED->value){

            $refund = new RefundRequest;
            $refund->user_id = auth()->user()->id;
            $refund->return_request_info_id = $return_request_info->id;
            $refund->order_id = $return_request_info->order_id;
            $refund->reason = $request->reason;
            $refund->admin_approval = 0;
            $refund->admin_seen = 0;
            $refund->refund_amount = $return_request_info->amount;
            $refund->refund_status = 0;
            $refund->save();
            $products = [];
            foreach ($return_request_info->return_request_products as $return_request_product) {
                if ($return_request_product->product && $return_request_product->product->name ) {
                    $products[] = $return_request_product->product->name;
                }
            }

            $array = array();
            $order = Order::where('id', $return_request_info->order_id)->first();
            $array['order_code'] = $order->code;
            $array['user_name'] = $order->user->name;
            $array['type'] = 'Return';
            $array['subject'] = translate(' Your Return Request Has Been Approved  – ') .$order->code . " ." ;
            $array['product_name'] = implode(', ', $products);

            try {
                $order->user->notify(new ReturnApprovedNotification($array));
                $command = 'php ' . base_path() . '/artisan queue:work --verbose --tries=1 --timeout=220 > /dev/null &';
                exec($command);
            } catch (\Exception $e) {
                Log::channel('email_logs')->error('Error occurred while sending  Order Return Approved  email in ReturnRequestController : ' . $e->getMessage());
            }
        }


        flash(translate('Status has been updated successfully'))->success();
        return redirect()->back();
    }
    public function return_configuration()
    {
        $data['return_faq'] = json_decode(BusinessSetting::where('type', 'return_faq')->first()->value, true);
        return view('backend.return_refund.return_configuration', $data);
    }

    public function save_return_faq(Request $request)
    {
        //update  return_faq cache
        $faq_data = json_decode($request->faq_data, true);
        $business_setting = BusinessSetting::where('type', 'return_faq')->first();
        $business_setting->value = json_encode($faq_data);
        $business_setting->save();

        return response()->json(['success' => true]);
    }
}
