<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Promotions\Banner;
use App\Models\Promotions\FlashSale;
use App\Models\Promotions\SeasonalBanner;
use Illuminate\Http\Request;

class PromotionsController extends Controller
{
    /**
     * Display the promotions dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function dashboard()
    {
        $now = now();
        
        // Count active flash sales
        $activeFlashSales = FlashSale::where('is_active', true)
            ->where('start_date', '<=', $now)
            ->where('end_date', '>=', $now)
            ->count();
            
        // Count active banners
        $activeBanners = Banner::where('is_active', true)
            ->where(function($q) use ($now) {
                $q->whereNull('start_date')
                    ->orWhere('start_date', '<=', $now);
            })
            ->where(function($q) use ($now) {
                $q->whereNull('end_date')
                    ->orWhere('end_date', '>=', $now);
            })
            ->count();
            
        // Count active seasonal banners
        $activeSeasonalBanners = SeasonalBanner::where('is_active', true)
            ->where(function($q) use ($now) {
                $q->whereNull('start_date')
                    ->orWhere('start_date', '<=', $now);
            })
            ->where(function($q) use ($now) {
                $q->whereNull('end_date')
                    ->orWhere('end_date', '>=', $now);
            })
            ->count();
            
        return view('admin.promotions.dashboard', compact('activeFlashSales', 'activeBanners', 'activeSeasonalBanners'));
    }
    
    /**
     * Display the promotions analytics.
     *
     * @return \Illuminate\Http\Response
     */
    public function analytics()
    {
        $now = now();
        
        // Get flash sales statistics
        $flashSales = FlashSale::with('products')
            ->withCount(['products as total_products'])
            ->get();
            
        $flashSaleStats = [
            'total' => $flashSales->count(),
            'active' => $flashSales->filter(function($sale) {
                return $sale->isActive();
            })->count(),
            'upcoming' => $flashSales->filter(function($sale) use ($now) {
                return $sale->is_active && $sale->start_date > $now;
            })->count(),
            'expired' => $flashSales->filter(function($sale) use ($now) {
                return !$sale->is_active || $sale->end_date < $now;
            })->count(),
            'total_products' => $flashSales->sum('total_products'),
            'top_performing' => $flashSales->sortByDesc(function($sale) {
                return $sale->products->sum('pivot.sold_items');
            })->take(3)
        ];
        
        // Get banner statistics
        $banners = Banner::all();
        
        $bannerStats = [
            'total' => $banners->count(),
            'active' => $banners->filter(function($banner) {
                return $banner->isActive();
            })->count(),
            'total_impressions' => $banners->sum('impressions'),
            'total_clicks' => $banners->sum('clicks'),
            'average_ctr' => $banners->sum('impressions') > 0 ? 
                ($banners->sum('clicks') / $banners->sum('impressions')) * 100 : 0,
            'top_performing' => $banners->sortByDesc('conversion_rate')->take(3)
        ];
        
        // Get seasonal banner statistics
        $seasonalBanners = SeasonalBanner::with('campaign')
            ->get();
            
        $seasonalBannerStats = [
            'total' => $seasonalBanners->count(),
            'active' => $seasonalBanners->filter(function($banner) {
                return $banner->isActive();
            })->count(),
            'upcoming' => $seasonalBanners->filter(function($banner) use ($now) {
                return $banner->is_active && $banner->start_date > $now;
            })->count(),
            'expired' => $seasonalBanners->filter(function($banner) use ($now) {
                return !$banner->is_active || ($banner->end_date && $banner->end_date < $now);
            })->count(),
            'by_season_type' => $seasonalBanners->groupBy('season_type')
                ->map(function($banners) {
                    return $banners->count();
                })
        ];
        
        return view('admin.promotions.analytics', compact('flashSaleStats', 'bannerStats', 'seasonalBannerStats'));
    }
} 