<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\FlashDeal;
use App\Models\FlashDealProduct;
use App\Models\Product;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class FlashSaleController extends Controller
{
    public function __construct()
    {
        // Staff Permission Check
        $this->middleware(['permission:view_all_flash_deals'])->only('index');
        $this->middleware(['permission:add_flash_deal'])->only(['create', 'store']);
        $this->middleware(['permission:edit_flash_deal'])->only(['edit', 'update']);
        $this->middleware(['permission:delete_flash_deal'])->only('destroy');
    }

    /**
     * Display a listing of flash sales.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $sort_search = null;
        $flash_deals = FlashDeal::latest();

        if ($request->has('search')) {
            $sort_search = $request->search;
            $flash_deals = $flash_deals->where('title', 'like', '%' . $sort_search . '%');
        }

        $flash_deals = $flash_deals->paginate(15);
        return view('backend.marketing.flash_deals.index', compact('flash_deals', 'sort_search'));
    }

    /**
     * Show the form for creating a new flash sale.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('backend.marketing.flash_deals.create');
    }

    /**
     * Store a newly created flash sale.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'discount' => 'required|numeric|min:0|max:100',
            'discount_type' => 'required|in:amount,percent',
        ]);

        $flash_deal = new FlashDeal;
        $flash_deal->title = $request->title;
        $flash_deal->start_date = strtotime($request->start_date);
        $flash_deal->end_date = strtotime($request->end_date);
        $flash_deal->status = $request->status ?? 1;
        $flash_deal->featured = $request->featured ?? 0;
        $flash_deal->background_color = $request->background_color;
        $flash_deal->text_color = $request->text_color;
        $flash_deal->banner = $request->banner;
        $flash_deal->slug = Str::slug($request->title) . '-' . Str::random(5);

        if ($flash_deal->save()) {
            flash(translate('Flash deal has been inserted successfully'))->success();
            return redirect()->route('admin.flash_sales.index');
        }

        flash(translate('Something went wrong'))->error();
        return back();
    }

    /**
     * Show the form for editing the specified flash sale.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $flash_deal = FlashDeal::findOrFail($id);
        return view('backend.marketing.flash_deals.edit', compact('flash_deal'));
    }

    /**
     * Update the specified flash sale.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
        ]);

        $flash_deal = FlashDeal::findOrFail($id);
        $flash_deal->title = $request->title;
        $flash_deal->start_date = strtotime($request->start_date);
        $flash_deal->end_date = strtotime($request->end_date);
        $flash_deal->status = $request->status ?? 1;
        $flash_deal->featured = $request->featured ?? 0;
        $flash_deal->background_color = $request->background_color;
        $flash_deal->text_color = $request->text_color;
        $flash_deal->banner = $request->banner;

        if ($flash_deal->save()) {
            flash(translate('Flash deal has been updated successfully'))->success();
            return redirect()->route('admin.flash_sales.index');
        }

        flash(translate('Something went wrong'))->error();
        return back();
    }

    /**
     * Remove the specified flash sale.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $flash_deal = FlashDeal::findOrFail($id);
        
        // Delete related flash deal products
        FlashDealProduct::where('flash_deal_id', $flash_deal->id)->delete();

        if ($flash_deal->delete()) {
            flash(translate('Flash deal has been deleted successfully'))->success();
        } else {
            flash(translate('Something went wrong'))->error();
        }

        return back();
    }

    /**
     * Show flash deal products
     */
    public function flash_deal_products($id)
    {
        $flash_deal = FlashDeal::findOrFail($id);
        $flash_deal_products = FlashDealProduct::where('flash_deal_id', $id)
            ->with('product')
            ->paginate(15);
        
        return view('backend.marketing.flash_deals.flash_deal_products', compact('flash_deal', 'flash_deal_products'));
    }

    /**
     * Add products to flash deal
     */
    public function add_product_to_flash_deal(Request $request)
    {
        $request->validate([
            'flash_deal_id' => 'required|exists:flash_deals,id',
            'product_id' => 'required|exists:products,id',
            'discount' => 'required|numeric|min:0',
            'discount_type' => 'required|in:amount,percent',
        ]);

        $flash_deal_product = FlashDealProduct::where('flash_deal_id', $request->flash_deal_id)
            ->where('product_id', $request->product_id)
            ->first();

        if ($flash_deal_product) {
            flash(translate('Product already added to this flash deal'))->warning();
            return back();
        }

        $flash_deal_product = new FlashDealProduct;
        $flash_deal_product->flash_deal_id = $request->flash_deal_id;
        $flash_deal_product->product_id = $request->product_id;
        $flash_deal_product->discount = $request->discount;
        $flash_deal_product->discount_type = $request->discount_type;

        if ($flash_deal_product->save()) {
            flash(translate('Product has been added to flash deal successfully'))->success();
        } else {
            flash(translate('Something went wrong'))->error();
        }

        return back();
    }

    /**
     * Remove product from flash deal
     */
    public function product_flash_deal_delete($id)
    {
        $flash_deal_product = FlashDealProduct::findOrFail($id);

        if ($flash_deal_product->delete()) {
            flash(translate('Product has been removed from flash deal successfully'))->success();
        } else {
            flash(translate('Something went wrong'))->error();
        }

        return back();
    }

    /**
     * Update flash deal status
     */
    public function updateStatus(Request $request)
    {
        $flash_deal = FlashDeal::findOrFail($request->id);
        $flash_deal->status = $request->status;

        if ($flash_deal->save()) {
            return response()->json(['success' => true, 'message' => translate('Flash deal status updated successfully')]);
        }

        return response()->json(['success' => false, 'message' => translate('Something went wrong')]);
    }

    /**
     * Update flash deal featured status
     */
    public function updateFeatured(Request $request)
    {
        $flash_deal = FlashDeal::findOrFail($request->id);
        $flash_deal->featured = $request->featured;

        if ($flash_deal->save()) {
            return response()->json(['success' => true, 'message' => translate('Flash deal featured status updated successfully')]);
        }

        return response()->json(['success' => false, 'message' => translate('Something went wrong')]);
    }
} 