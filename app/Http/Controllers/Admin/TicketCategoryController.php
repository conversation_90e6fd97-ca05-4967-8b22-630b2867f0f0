<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\TicketCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class TicketCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $sort_search = null;
        $categories = TicketCategory::orderBy('created_at', 'desc');

        if ($request->has('search')) {
            $sort_search = $request->search;
            $categories = $categories->where('name', 'like', '%' . $sort_search . '%');
        }

        $categories = $categories->paginate(15);
        return view('backend.support.ticket_categories.index', compact('categories', 'sort_search'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('backend.support.ticket_categories.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:100',
            'slug' => 'nullable|string|max:255|unique:ticket_categories,slug',
            'description' => 'nullable|string',
        ]);

        $category = new TicketCategory;
        $category->name = $request->name;

        if ($request->slug) {
            $category->slug = Str::slug($request->slug);
        } else {
            $category->slug = Str::slug($request->name);
        }

        $category->description = $request->description;
        $category->is_active = $request->has('is_active') ? 1 : 0;
        $category->save();

        flash(translate('Ticket category has been created successfully'))->success();
        return redirect()->route('ticket-categories.index');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $category = TicketCategory::findOrFail($id);
        return view('backend.support.ticket_categories.edit', compact('category'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $category = TicketCategory::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:100',
            'slug' => 'nullable|string|max:255|unique:ticket_categories,slug,' . $category->id,
            'description' => 'nullable|string',

        ]);

        $category->name = $request->name;

       /* if ($request->slug) {
            $category->slug = Str::slug($request->slug);
        }*/

        $category->description = $request->description;
        $category->is_active = $request->has('is_active') ? 1 : 0;
        $category->save();

        flash(translate('Ticket category has been updated successfully'))->success();
        return redirect()->route('ticket-categories.index');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $category = TicketCategory::findOrFail($id);

        // Check if there are tickets associated with this category
        if ($category->tickets()->count() > 0) {
            flash(translate('Cannot delete category with associated tickets'))->error();
            return redirect()->route('ticket-categories.index');
        }

        $category->delete();

        flash(translate('Ticket category has been deleted successfully'))->success();
        return redirect()->route('ticket-categories.index');
    }

    /**
     * Update the status of the specified resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateStatus(Request $request)
    {
        $category = TicketCategory::findOrFail($request->id);
        $category->is_active = $request->status;
        $category->save();

        return 1;
    }
}
