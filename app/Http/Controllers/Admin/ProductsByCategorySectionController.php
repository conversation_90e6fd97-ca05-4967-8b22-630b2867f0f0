<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\FlashDeal;
use App\Rules\BannerImageRule;
use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Category;
use App\Models\ProductsByCategorySectionInfo;
use App\Models\ProductsByCategorySectionDetails;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Validator;

class ProductsByCategorySectionController extends Controller
{
    public function __construct()
    {

    }

    public function index(Request $request)
    {
        $sort_search = null;
        $product_by_categories = ProductsByCategorySectionInfo::orderBy('created_at', 'desc');
        if ($request->has('search')) {
            $sort_search = $request->search;
            $product_by_categories = $product_by_categories->where('name', 'like', '%' . $sort_search . '%');
        }
        $product_by_categories = $product_by_categories->paginate(15);
        return view('backend.product_by_category_section.index', compact('product_by_categories', 'sort_search'));
    }

    public function updateStatus(Request $request)
    {
        $product_by_categories = ProductsByCategorySectionInfo::findOrFail($request->id);
        $product_by_categories->status = $request->status;
        if ($product_by_categories->save()) {
            flash(translate('Top Picks of Category status updated successfully'))->success();
            return 1;
        }
        return 0;
    }

    public function create()
    {
        $categories = Category::where('parent_id', 0)
            ->where('digital', 0)
            ->with('childrenCategories')
            ->get();
        return view('backend.product_by_category_section.create', compact('categories'));
    }

    public function store(Request $request)
    {
        $rules = [
            'category_one' => [
                'nullable',
                Rule::exists('categories', 'id'),
                'required_without:category_tow',
            ],
            'category_tow' => [
                'nullable',
                Rule::exists('categories', 'id'),
                'required_without:category_one',
            ],
            'banner_img' => ['required', new BannerImageRule],
            'category_banner_img_one' => [
                'nullable',
                'string',
                'required_if:category_one,'.$request->category_one, // required if category_one exists
            ],
            'category_banner_img_tow' => [
                'nullable',
                'string',
                'required_if:category_tow,'.$request->category_tow, // required if category_tow exists
            ],
        ];

        // Define custom error messages
        $messages = [
            'category_one.required_without' => 'Either category one or category two must be provided.',
            'category_tow.required_without' => 'Either category one or category two must be provided.',
            'category_banner_img_one.required_with' => 'Category banner image one is required when category one is present.',
            'category_banner_img_tow.required_with' => 'Category banner image two is required when category two is present.',
        ];

        // Create a validator instance
        $validator = Validator::make($request->all(), $rules, $messages);

        if ($validator->fails()) {
            dd($validator->errors());
            flash($validator->errors())->error();
            return redirect()->back();
        }
        DB::beginTransaction();
        try {
            $category_one = Category::find($request->category_one);
            $category_tow = Category::find($request->category_tow);
            $name = $category_one->name . "," . $category_tow->name;

            $productsByCategorySectionInfo = new ProductsByCategorySectionInfo;
            $productsByCategorySectionInfo->name = $name;
            $productsByCategorySectionInfo->slug = preg_replace('/[^A-Za-z0-9\-]/', '', str_replace(' ', '-', strtoupper($category_one->name . "-" . $category_tow->name)));
            $productsByCategorySectionInfo->banner_img = $request->banner_img;
            $productsByCategorySectionInfo->save();
            if ($request->category_one) {
                $productsByCategorySectionDetails = new ProductsByCategorySectionDetails;
                $productsByCategorySectionDetails->products_by_category_section_info_id = $productsByCategorySectionInfo->id;
                $productsByCategorySectionDetails->category_id = $request->category_one;
                $productsByCategorySectionDetails->category_banner_img = $request->category_banner_img_one;
                $productsByCategorySectionDetails->status = isset($request->category_banner_img_one_status) ? 1 : 0;
                $productsByCategorySectionDetails->save();

            }
            if ($request->category_tow) {
                $productsByCategorySectionDetails = new ProductsByCategorySectionDetails;
                $productsByCategorySectionDetails->products_by_category_section_info_id = $productsByCategorySectionInfo->id;
                $productsByCategorySectionDetails->category_id = $request->category_tow;
                $productsByCategorySectionDetails->category_banner_img = $request->category_banner_img_tow;
                $productsByCategorySectionDetails->status = isset($request->category_banner_img_tow_status) ? 1 : 0;
                $productsByCategorySectionDetails->save();
            }

            DB::commit();
        } catch (\Error $th) {
            DB::rollBack();
            flash(translate('Something went wrong'))->error();
            return back();
        } catch (\Exception $e) {
            DB::rollBack();
            flash(translate('Something went wrong'))->error();
            return back();
        }
        flash(translate('Top Picks of Category inserted successfully'))->success();
        return redirect()->route('product_by_categories.index');
    }

    public function edit(Request $request, $id)
    {
        $productsByCategorySectionInfo = ProductsByCategorySectionInfo::findOrFail($id);
        $productsByCategorySectionDetails = ProductsByCategorySectionDetails::orderBy('id', 'asc')->where('products_by_category_section_info_id', $id)->get();
        $categories = Category::where('parent_id', 0)
            ->where('digital', 0)
            ->with('childrenCategories')
            ->get();
        return view('backend.product_by_category_section.edit', compact('categories', 'productsByCategorySectionInfo', 'productsByCategorySectionDetails'));
    }

    public function update(Request $request)
    {
        /*$rules = [
            'category_one' => [
                'nullable',
                Rule::exists('categories', 'id'),
                'required_without:category_tow',
            ],
            'category_tow' => [
                'nullable',
                Rule::exists('categories', 'id'),
                'required_without:category_one',
            ],
            'banner_img' => ['required', new BannerImageRule],
            'category_banner_img_one' => [
                'nullable',
                'string',
                'required_with:category_one',
            ],
            'category_banner_img_tow' => [
                'nullable',
                'string',
                'required_with:category_tow',
            ],
        ];

        // Define custom error messages
        $messages = [
            'category_one.required_without' => 'Either category one or category two must be provided.',
            'category_tow.required_without' => 'Either category one or category two must be provided.',
            'category_banner_img_one.required_with' => 'Category banner image one is required when category one is present.',
            'category_banner_img_tow.required_with' => 'Category banner image two is required when category two is present.',
        ];

        // Create a validator instance
        $validator = Validator::make($request->all(), $rules, $messages);

        // Check if validation fails
        if ($validator->fails()) {
            flash($validator->errors())->error();
            return redirect()->back();
        }*/

        DB::beginTransaction();
        try {
            $category_one = Category::find($request->category_one);
            $category_tow = Category::find($request->category_tow);
            $name = $category_one->name . "," . $category_tow->name;

            $productsByCategorySectionInfo = ProductsByCategorySectionInfo::find($request->id);
            $productsByCategorySectionInfo->name = $name;
            $productsByCategorySectionInfo->slug = preg_replace('/[^A-Za-z0-9\-]/', '', str_replace(' ', '-', strtoupper($category_one->name . "-" . $category_tow->name)));
            $productsByCategorySectionInfo->banner_img = $request->banner_img;
            $productsByCategorySectionInfo->save();

            ProductsByCategorySectionDetails::where('products_by_category_section_info_id', $request->id)->delete();


            $productsByCategorySectionDetails = new ProductsByCategorySectionDetails;
            $productsByCategorySectionDetails->products_by_category_section_info_id = $productsByCategorySectionInfo->id;
            $productsByCategorySectionDetails->category_id = $request->category_one;
            $productsByCategorySectionDetails->category_banner_img = $request->category_banner_img_one;
            $productsByCategorySectionDetails->status = isset($request->category_banner_img_one_status) ? 1 : 0;
            $productsByCategorySectionDetails->save();

            $productsByCategorySectionDetails = new ProductsByCategorySectionDetails;
            $productsByCategorySectionDetails->products_by_category_section_info_id = $productsByCategorySectionInfo->id;
            $productsByCategorySectionDetails->category_id = $request->category_tow;
            $productsByCategorySectionDetails->category_banner_img = $request->category_banner_img_tow;
            $productsByCategorySectionDetails->status = isset($request->category_banner_img_tow_status) ? 1 : 0;
            $productsByCategorySectionDetails->save();
            DB::commit();
        } catch (\Error $th) {
            DB::rollBack();
            flash(translate('Something went wrong'))->error();
            return back();
        } catch (\Exception $e) {
            DB::rollBack();
            flash(translate('Something went wrong'))->error();
            return back();
        }
        flash(translate('Top Picks of Category inserted successfully'))->success();
        return redirect()->route('product_by_categories.index');
    }

    public function destroy(Request $request)
    {
        ProductsByCategorySectionDetails::where('products_by_category_section_info_id', $request->id)->delete();
        $product_by_categories = ProductsByCategorySectionInfo::find($request->id);
        $product_by_categories->delete();
        flash(translate('Top Picks of Category inserted successfully'))->success();
        return redirect()->route('product_by_categories.index');
    }
}
