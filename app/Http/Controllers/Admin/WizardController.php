<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Services\VideoService;
use Illuminate\Http\Request;
use App\Models\WizardInfo;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Exception;

class WizardController extends Controller
{
    protected VideoService $videoService;
    public function __construct(
        VideoService            $videoService
    )
    {
        $this->videoService = $videoService;
    }

    public function index(Request $request)
    {
        $sort_search = null;
        $wizard_query = WizardInfo::orderBy('created_at', 'desc');
        if ($request->has('search')) {
            $sort_search = $request->search;
            $wizard_query = $wizard_query->where('wizard_info_name', 'like', '%' . $sort_search . '%');
        }
        $wizards = $wizard_query->paginate(15);
        return view('backend.wizards.index', compact('wizards', 'sort_search'));
    }

    public function edit($slug)
    {
        $wizard = WizardInfo::where('slug', $slug)->firstOrFail();
        $data = array();
        // Eager load wizard_details ordered by wizard_detail_position
        $wizard->load(['wizard_details' => function ($query) {
            $query->orderBy('wizard_detail_position', 'asc');
        }]);

        // Retrieve products in the same order as wizard_details
        $selectedProducts = $wizard->wizard_details->map(function ($detail) {
            return $detail->product; // Assuming `wizard_details` has a `product` relationship
        })->filter()->all(); //

        $data['categories'] = \App\Models\Category::all();
        $data['brands'] = \App\Models\Brand::all();
        $data['blogs'] = \App\Models\Blog::select('id', 'title')->where('status', 1)->get();
        $data['wizard'] = $wizard;
        $data['selectedProducts'] = $selectedProducts;
        return view('backend.wizards.edit', $data);
    }
    public function update_banner(Request $request, WizardInfo $wizard)
    {
        try {
            if(isset($request->admin_view_file) && $request->admin_view_file=='products-wizard'){
                $this->validateWizardProductRequest($request);
            }else if(isset($request->admin_view_file) && $request->admin_view_file =="categories-wizard"){
                $this->validateWizardCategoryRequest($request);
            }else if(isset($request->admin_view_file) && $request->admin_view_file =="brands-wizard"){
                $this->validateWizardBrandRequest($request);
            }else if(isset($request->admin_view_file) && $request->admin_view_file =="home-page-featured-videos"){
                $this->validateWizardVideoRequest($request);
            }else{
                $this->validateWizardBannerRequest($request);
            }
            // Validate request data


            // Process the update within a transaction
            return DB::transaction(function () use ($request, $wizard) {
                // Update wizard basic info
               $wizard_info =  $this->updateWizardBasicInfo($wizard, $request);

                if($request->admin_view_file == 'home-page-featured-videos'){

                    foreach ($wizard_info->videos as $key => $video) {
                        $video->delete();
                    }


                    foreach ($request->video_files as $key => $video) {
                        $video_thumbnail='';
                        $video_title='';
                        $video_duration='';
                        $video_sequence='';
                        if(isset($request->video_thumbnail[$key]) && $request->video_thumbnail[$key] != null){
                            $video_thumbnail = $request->video_thumbnail[$key];
                        }
                        if(isset($request->video_title[$key]) && $request->video_title[$key] != null){
                            $video_title = $request->video_title[$key];
                        }
                        if(isset($request->video_duration[$key]) && $request->video_duration[$key] != null){
                            $video_duration = $request->video_duration[$key];
                        }
                        if(isset($request->video_sequence[$key]) && $request->video_sequence[$key] != null){
                            $video_sequence = $request->video_sequence[$key];
                        }
                        $this->videoService->create(
                            $video_thumbnail,
                            $video_title,
                            $video_duration,
                            WizardInfo::class,
                            $wizard_info->id,
                            $video,
                            $video_sequence,
                            Auth::id()
                        );
                    }
                }else{
                    // Update wizard details
                    $this->updateWizardDetails($wizard, $request);
                }


                flash(translate('Wizard banner has been updated successfully'))->success();
                return redirect()->route('wizards.index');
            });

        } catch (ValidationException $e) {
            flash($e->validator->errors()->first())->error();
            return redirect()->back()->withErrors($e->validator->errors())->withInput();
        } catch (ModelNotFoundException $e) {
            flash(translate('Wizard not found'))->error();
            return redirect()->route('wizards.index');
        } catch (Exception $e) {
            flash(translate('An error occurred while updating the wizard banner'))->error();
            return back()->withInput();
        }
    }

    /**
     * Validate the wizard banner update request
     */
    private function validateWizardCategoryRequest(Request $request): void
    {
        $request->validate([
            'category_id' => 'required|array',
            'category_id.*' => 'required',
            'wizard_detail_position' => 'required|array',
            'wizard_detail_position.*' => 'required|numeric|min:0'
        ], [
            'category_id.required' => 'Category are required',
            'category_id.*.required' => 'All Category are required',
            'wizard_detail_position.required' => 'Wizard detail position is required',
            'wizard_detail_position.*.required' => 'All position fields are required',
            'wizard_detail_position.*.numeric' => 'Position must be a number',
            'wizard_detail_position.*.min' => 'Position cannot be negative'
        ]);
    }
    private function validateWizardProductRequest(Request $request): void
    {
        $request->validate([
            'product_id' => 'required|array',
            'product_id.*' => 'required',
            'wizard_detail_position' => 'required|array',
            'wizard_detail_position.*' => 'required|numeric|min:0'
        ], [
            'wizard_detail.required' => 'Wizard detail is required',
            'wizard_detail.*.required' => 'All wizard detail fields are required',
            'product_id.required' => 'Wizard detail images are required',
            'product_id.*.required' => 'All wizard detail images are required',
            'wizard_detail_position.required' => 'Wizard detail position is required',
            'wizard_detail_position.*.required' => 'All position fields are required',
            'wizard_detail_position.*.numeric' => 'Position must be a number',
            'wizard_detail_position.*.min' => 'Position cannot be negative'
        ]);
    }
    private function validateWizardBannerRequest(Request $request): void
    {
        $request->validate([
            'wizard_detail' => 'required|array',
            'wizard_detail.*' => 'required',
            'wizard_detail_images' => 'required|array',
            'wizard_detail_images.*' => 'required',
            'wizard_detail_position' => 'required|array',
            'wizard_detail_position.*' => 'required|numeric|min:0'
        ], [
            'wizard_detail.required' => 'Wizard detail is required',
            'wizard_detail.*.required' => 'All wizard detail fields are required',
            'wizard_detail_images.required' => 'Wizard detail images are required',
            'wizard_detail_images.*.required' => 'All wizard detail images are required',
            'wizard_detail_position.required' => 'Wizard detail position is required',
            'wizard_detail_position.*.required' => 'All position fields are required',
            'wizard_detail_position.*.numeric' => 'Position must be a number',
            'wizard_detail_position.*.min' => 'Position cannot be negative'
        ]);
    }
    private function validateWizardBrandRequest(Request $request): void
    {
        $request->validate([
            'brand_id' => 'required|array',
            'brand_id.*' => 'required',
            'wizard_detail_position' => 'required|array',
            'wizard_detail_position.*' => 'required|numeric|min:0'
        ], [
            'brand_id.required' => 'Brands are required',
            'brand_id.*.required' => 'Brands are required',
            'wizard_detail_position.required' => 'Brand position is required',
            'wizard_detail_position.*.required' => 'All position fields are required',
            'wizard_detail_position.*.numeric' => 'Position must be a number',
            'wizard_detail_position.*.min' => 'Position cannot be negative'
        ]);
    }
    private function validateWizardVideoRequest(Request $request): void
    {
        $request->validate([
            'video_files' => 'required|array',
            'video_files.*' => 'required',
            'video_thumbnail' => 'required|array',
            'video_thumbnail.*' => 'required',
            'video_sequence' => 'required|array',
            'video_sequence.*' => 'required',
        ], [
            'video_files.required' => 'Brands are required',
            'video_files.*.required' => 'Brands are required',
            'video_thumbnail.required' => 'Brands are required',
            'video_thumbnail.*.required' => 'Brands are required',
            'video_sequence.required' => 'Brands are required',
            'video_sequence.*.required' => 'Brands are required',
        ]);
    }

    /**
     * Update wizard basic information
     */
    private function updateWizardBasicInfo(WizardInfo $wizard, Request $request): WizardInfo
    {
        // List of slugs that will trigger the cache invalidation
        $homeHeroBannersCache = [
            'home-page-hero-banner-one',
            'home-page-hero-banner-two',
            'home-page-hero-banner-three'
        ];

        // Check if the wizard's slug matches any of the predefined slugs
        if (in_array($wizard->slug, $homeHeroBannersCache)) {
            // Forget the cache for hero banners if the slug matches
            Cache::forget('home_hero_banners');
        }
        $homeHeroBannersTow = [
            'home-page-hero-banner-one',
            'home-page-hero-banner-two',
            'home-page-hero-banner-three'
        ];

        // Check if the wizard's slug matches any of the predefined slugs
        if (in_array($wizard->slug, $homeHeroBannersTow)) {
            // Forget the cache for hero banners if the slug matches
            Cache::forget('home_hero_banners_tow');
        }
        if ($wizard->slug=='home-page-featured-category') {
            // Forget the cache for hero banners if the slug matches
            Cache::forget('home_page_featured_categories');
        }
        $wizard->background_color = $request->background_color ?? Null;
        $wizard->start_date = $request->start_date ? $request->start_date : null;
        $wizard->end_date = $request->end_date ? $request->end_date : null;
        $wizard->save();
        return $wizard;
    }

    /**
     * Update wizard details
     */
    private function updateWizardDetails(WizardInfo $wizard, Request $request): void
    {
        $wizard->wizard_details()->delete();
        $details = $this->prepareWizardDetails($request);
        $wizard->wizard_details()->createMany($details);
    }

    /**
     * Prepare wizard details array from request
     */
    private function prepareWizardDetails(Request $request): array
    {
        $details = [];
        foreach ($request->wizard_detail as $key => $detail) {
            $details[$key] = [
                'wizard_detail_title' => $request->wizard_detail_title[$key] ?? '',
                'wizard_detail_sub_title' => $request->wizard_detail_sub_title[$key] ?? '',
                'wizard_detail_description' => $request->wizard_detail_description[$key] ?? '',
                'wizard_detail_images' => $request->wizard_detail_images[$key] ?? '',
                'wizard_detail_images_url' => $request->wizard_detail_images_url[$key] ?? '',
                'wizard_detail_mobile_images' => $request->wizard_detail_mobile_images[$key] ?? '',
                'wizard_detail_redirect_url' => $request->wizard_detail_redirect_url[$key] ?? '',
                'wizard_detail_position' => $request->wizard_detail_position[$key] ?? '',
                'wizard_detail_button_text' => $request->wizard_detail_button_text[$key] ?? '',
                'wizard_detail_button_link' => $request->wizard_detail_button_link[$key] ?? '',
                'category_id' => $request->category_id[$key] ?? '',
                'product_id' => $request->product_id[$key] ?? '',
                'blog_id' => $request->blog_id[$key] ?? '',
                'brand_id' => $request->brand_id[$key] ?? '',
                'wizard_detail_html' => $request->wizard_detail_html[$key] ?? '',
                'wizard_detail_background_color' => $request->wizard_detail_background_color[$key] ?? ''
            ];
        }
        return $details;
    }

    public function update(Request $request, $slug)
    {
        $wizard = WizardInfo::where('slug', $slug)->firstOrFail();
        $wizard->wizard_info_name = $request->wizard_info_name;
        $wizard->slug = Str::slug($request->wizard_info_name);
        $wizard->save();

        flash(translate('Wizard has been updated successfully'))->success();
        return redirect()->route('wizards.index');
    }
    public function get_wizard_products(Request $request){
        $product_ids = array_reverse($request->product_ids);
        $wizard_id = $request->id;
        return view('backend.wizards.get_wizard_products', compact('product_ids', 'wizard_id'));
    }
    public function update_status(Request $request)
    {
       WizardInfo::where('id', $request->id)->update(['wizard_info_status' => $request->status]);
       return 1;
    }
}
