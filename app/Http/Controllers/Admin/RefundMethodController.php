<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ReturnMethod;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class RefundMethodController extends Controller
{
    protected $type ;
    public function __construct()
    {
        $this->type = 'refund';
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $returnMethods = ReturnMethod::where('type', $this->type)->paginate(15);
        return view('backend.return_refund.refund_methods.index', compact('returnMethods'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('backend.return_refund.refund_methods.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'processing_time' => 'required|string|max:255',
            'fee' => 'required|numeric|min:0',
            'restrictions' => 'nullable|string',
            'requirement_keys' => 'nullable|array',
            'requirement_values' => 'nullable|array',
            'additional_info_keys' => 'nullable|array',
            'additional_info_values' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            flash(translate('Error creating return method'))->error();
            return back()->withErrors($validator)->withInput();
        }

        // Parse restrictions
        $restrictions = null;
        if (!empty($request->restrictions)) {
            $restrictions = explode(',', $request->restrictions);
            $restrictions = array_map('trim', $restrictions);
        }

        // Process requirements
        $requirements = [];
        if ($request->has('requirement_keys') && $request->has('requirement_values')) {
            $requirementKeys = $request->requirement_keys;
            $requirementValues = $request->requirement_values;

            foreach ($requirementKeys as $index => $key) {
                if (!empty($key) && isset($requirementValues[$index])) {
                    $requirements[$key] = (bool) $requirementValues[$index];
                }
            }
        }

        // Process additional information
        $additionalInformation = [];
        if ($request->has('additional_info_keys') && $request->has('additional_info_values')) {
            $additionalInfoKeys = $request->additional_info_keys;
            $additionalInfoValues = $request->additional_info_values;

            foreach ($additionalInfoKeys as $index => $key) {
                if (!empty($key) && isset($additionalInfoValues[$index])) {
                    // Check if the value contains commas, which might indicate an array
                    $value = $additionalInfoValues[$index];
                    if (strpos($value, ',') !== false && !empty($value)) {
                        // Split by comma and trim each value
                        $valueArray = explode(',', $value);
                        $valueArray = array_map('trim', $valueArray);
                        $additionalInformation[$key] = $valueArray;
                    } else {
                        $additionalInformation[$key] = $value;
                    }
                }
            }
        }

        // Convert empty arrays to null to avoid JSON encoding issues
        if (empty($requirements)) {
            $requirements = [];
        }

        if (empty($additionalInformation)) {
            $additionalInformation = [];
        }

        // If this is set as default, unset all other defaults
        if ($request->has('is_default') && $request->is_default) {
            ReturnMethod::where('is_default', true)->update(['is_default' => false]);
        }

        $returnMethod = new ReturnMethod();
        $returnMethod->id = Str::uuid()->toString(); // Generate UUID
        $returnMethod->type = $this->type;
        $returnMethod->name = $request->name;
        $returnMethod->description = $request->description;
        $returnMethod->processing_time = $request->processing_time;
        $returnMethod->fee = $request->fee;
        $returnMethod->restrictions = $restrictions;
        $returnMethod->requirements = json_encode($requirements);
        $returnMethod->additionalInformation = json_encode($additionalInformation, JSON_PRETTY_PRINT);
        $returnMethod->is_default = $request->has('is_default') ? 1 : 0;
        $returnMethod->is_active = $request->has('is_active') ? 1 : 0;
        $returnMethod->save();

        flash(translate('Return method has been created successfully'))->success();
        return redirect()->route('refund_methods.index');
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        $returnMethod = ReturnMethod::findOrFail($id);
        return view('backend.return_refund.refund_methods.edit', compact('returnMethod'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $returnMethod = ReturnMethod::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'processing_time' => 'required|string|max:255',
            'fee' => 'required|numeric|min:0',
            'restrictions' => 'nullable|string',
            'requirement_keys' => 'nullable|array',
            'requirement_values' => 'nullable|array',
            'additional_info_keys' => 'nullable|array',
            'additional_info_values' => 'nullable|array',
        ]);
        if ($validator->fails()) {
            flash(translate('Error updating return method'))->error();
            return back()->withErrors($validator)->withInput();
        }

        // Parse restrictions
        $restrictions = null;
        if (!empty($request->restrictions)) {
            $restrictions = explode(',', $request->restrictions);
            $restrictions = array_map('trim', $restrictions);
        }

        // Process requirements
        $requirements = [];
        if ($request->has('requirement_keys') && $request->has('requirement_values')) {
            $requirementKeys = $request->requirement_keys;
            $requirementValues = $request->requirement_values;

            foreach ($requirementKeys as $index => $key) {
                if (!empty($key) && isset($requirementValues[$index])) {
                    $requirements[$key] = (bool) $requirementValues[$index];
                }
            }
        }

        // Process additional information
        $additionalInformation = [];
        if ($request->has('additional_info_keys') && $request->has('additional_info_values')) {
            $additionalInfoKeys = $request->additional_info_keys;
            $additionalInfoValues = $request->additional_info_values;

            foreach ($additionalInfoKeys as $index => $key) {
                if (!empty($key) && isset($additionalInfoValues[$index])) {
                    // Check if the value contains commas, which might indicate an array
                    $value = $additionalInfoValues[$index];
                    if (strpos($value, ',') !== false && !empty($value)) {
                        // Split by comma and trim each value
                        $valueArray = explode(',', $value);
                        $valueArray = array_map('trim', $valueArray);
                        $additionalInformation[$key] = $valueArray;
                    } else {
                        $additionalInformation[$key] = $value;
                    }
                }
            }
        }

        // Convert empty arrays to null to avoid JSON encoding issues
        if (empty($requirements)) {
            $requirements = null;
        }

        if (empty($additionalInformation)) {
            $additionalInformation = null;
        }

        // If this is set as default, unset all other defaults
        if ($request->has('is_default') && $request->is_default && !$returnMethod->is_default) {
            ReturnMethod::where('is_default', true)->update(['is_default' => false]);
        }

        $returnMethod->name = $request->name;
        $returnMethod->description = $request->description;
        $returnMethod->processing_time = $request->processing_time;
        $returnMethod->fee = $request->fee;
        $returnMethod->restrictions = $restrictions;
        $returnMethod->requirements = $requirements;
        $returnMethod->additionalInformation = $additionalInformation;
        $returnMethod->is_default = $request->has('is_default') ? 1 : 0;
        $returnMethod->is_active = $request->has('is_active') ? 1 : 0;
        $returnMethod->save();

        flash(translate('Return method has been updated successfully'))->success();
        return redirect()->route('refund_methods.index');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $returnMethod = ReturnMethod::findOrFail($id);
        $returnMethod->delete();

        flash(translate('Return method has been deleted successfully'))->success();
        return redirect()->route('refund_methods.index');
    }

    /**
     * Update the status of the specified resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return int
     */
    public function updateStatus(Request $request)
    {
        $returnMethod = ReturnMethod::findOrFail($request->id);
        $returnMethod->is_active = $request->status;
        $returnMethod->save();

        return 1;
    }
}
