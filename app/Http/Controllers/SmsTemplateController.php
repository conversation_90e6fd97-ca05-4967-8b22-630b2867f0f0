<?php

namespace App\Http\Controllers;

use App\Models\SmsTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SmsTemplateController extends Controller
{
    /**
     * Display a listing of SMS templates
     */
    public function index()
    {
        $sms_templates = SmsTemplate::latest()->paginate(20);
        return view('backend.sms.templates.index', compact('sms_templates'));
    }

    /**
     * Show the form for creating a new SMS template
     */
    public function create()
    {
        return view('backend.sms.templates.create');
    }

    /**
     * Store a newly created SMS template
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:sms_templates,name',
            'identifier' => 'required|string|max:255|unique:sms_templates,identifier',
            'template' => 'required|string|max:160',
            'status' => 'nullable|boolean'
        ]);

        if ($validator->fails()) {
            flash(translate('Please check your inputs and try again'))->error();
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $sms_template = new SmsTemplate;
        $sms_template->name = $request->name;
        $sms_template->identifier = $request->identifier;
        $sms_template->template = $request->template;
        $sms_template->status = $request->has('status') ? 1 : 0;
        $sms_template->save();

        flash(translate('SMS template created successfully'))->success();
        return redirect()->route('sms-templates.index');
    }

    /**
     * Display the specified SMS template
     */
    public function show($id)
    {
        $sms_template = SmsTemplate::findOrFail($id);
        return view('backend.sms.templates.show', compact('sms_template'));
    }

    /**
     * Show the form for editing the specified SMS template
     */
    public function edit($id)
    {
        $sms_template = SmsTemplate::findOrFail($id);
        return view('backend.sms.templates.edit', compact('sms_template'));
    }

    /**
     * Update the specified SMS template
     */
    public function update(Request $request, $id)
    {
        $sms_template = SmsTemplate::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:sms_templates,name,' . $id,
            'identifier' => 'required|string|max:255|unique:sms_templates,identifier,' . $id,
            'template' => 'required|string|max:160',
            'status' => 'nullable|boolean'
        ]);

        if ($validator->fails()) {
            flash(translate('Please check your inputs and try again'))->error();
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $sms_template->name = $request->name;
        $sms_template->identifier = $request->identifier;
        $sms_template->template = $request->template;
        $sms_template->status = $request->has('status') ? 1 : 0;
        $sms_template->save();

        flash(translate('SMS template updated successfully'))->success();
        return redirect()->route('sms-templates.index');
    }

    /**
     * Remove the specified SMS template
     */
    public function destroy($id)
    {
        $sms_template = SmsTemplate::findOrFail($id);
        $sms_template->delete();

        flash(translate('SMS template deleted successfully'))->success();
        return redirect()->route('sms-templates.index');
    }

    /**
     * Get SMS template by identifier for API use
     */
    public function getByIdentifier($identifier)
    {
        $template = SmsTemplate::where('identifier', $identifier)
                               ->where('status', 1)
                               ->first();
        
        return $template ? $template->template : null;
    }

    /**
     * Replace placeholders in template with actual values
     */
    public function parseTemplate($template, $variables = [])
    {
        $parsed_template = $template;
        
        foreach ($variables as $key => $value) {
            $parsed_template = str_replace('{{' . $key . '}}', $value, $parsed_template);
        }
        
        return $parsed_template;
    }

    /**
     * Get available template variables/placeholders
     */
    public function getAvailableVariables()
    {
        return [
            'user_name' => 'User Name',
            'verification_code' => 'Verification Code',
            'order_code' => 'Order Code',
            'amount' => 'Amount',
            'shop_name' => 'Shop Name',
            'app_name' => 'Application Name'
        ];
    }
} 