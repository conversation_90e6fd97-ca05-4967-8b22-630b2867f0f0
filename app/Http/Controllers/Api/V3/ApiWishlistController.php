<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Wishlist;
use App\Models\Product;
use App\Models\User;
use Auth;
use App\Http\Controllers\Api\V3\ApiResponse;

class ApiWishlistController extends Controller
{
    protected $apiResponse;

    public function __construct()
    {
        $this->apiResponse = new ApiResponse();
        // Check if there's any middleware being applied here
    }

    /**
     * Get user's wishlist
     */
    public function getWishlistByUser(Request $request)
    {
        try {
            $user = Auth::user();
            
            if (!$user) {
                return $this->apiResponse->notAuthorizedResponse();
            }

            $page = $request->input('page', 1);
            $limit = $request->input('perPage', 10);
            $search = $request->input('search', '');
            $sortBy = $request->input('sortBy', $request->input('sort', 'created_at'));
            $sortOrder = $request->input('sortOrder', $request->input('sortDirection', 'desc'));
            $category = $request->input('category', '');
            $minPrice = $request->input('minPrice', 0);
            $maxPrice = $request->input('maxPrice', 0);
            $inStock = $request->input('inStock', null);

            $query = Wishlist::with(['product'])
                ->where('user_id', $user->id);

            // Apply filters
            if (!empty($search)) {
                $query->whereHas('product', function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%");
                });
            }
            
            if (!empty($category)) {
                $query->whereHas('product', function ($q) use ($category) {
                    $q->where('category_id', $category);
                });
            }
            
            if ($minPrice > 0) {
                $query->whereHas('product', function ($q) use ($minPrice) {
                    $q->where('unit_price', '>=', $minPrice);
                });
            }
            
            if ($maxPrice > 0) {
                $query->whereHas('product', function ($q) use ($maxPrice) {
                    $q->where('unit_price', '<=', $maxPrice);
                });
            }
            
            if ($inStock !== null) {
                $query->whereHas('product', function ($q) use ($inStock) {
                    $q->where('current_stock', '>', 0);
                });
            }
            
            // Apply sorting
            if ($sortBy === 'price_asc') {
                $query->join('products', 'wishlists.product_id', '=', 'products.id')
                      ->orderBy('products.unit_price', 'asc')
                      ->select('wishlists.*');
            } elseif ($sortBy === 'price_desc') {
                $query->join('products', 'wishlists.product_id', '=', 'products.id')
                      ->orderBy('products.unit_price', 'desc')
                      ->select('wishlists.*');
            } elseif ($sortBy === 'name_asc') {
                $query->join('products', 'wishlists.product_id', '=', 'products.id')
                      ->orderBy('products.name', 'asc')
                      ->select('wishlists.*');
            } elseif ($sortBy === 'name_desc') {
                $query->join('products', 'wishlists.product_id', '=', 'products.id')
                      ->orderBy('products.name', 'desc')
                      ->select('wishlists.*');
            } elseif ($sortBy === 'rating') {
                $query->join('products', 'wishlists.product_id', '=', 'products.id')
                      ->orderBy('products.rating', 'desc')
                      ->select('wishlists.*');
            } elseif ($sortBy === 'addedAt') {
                $query->orderBy('wishlists.created_at', $sortOrder);
            } else {
                // Handle other sorting fields, mapping addedAt to created_at
                $actualSortBy = ($sortBy === 'addedAt') ? 'wishlists.created_at' : 'wishlists.' . $sortBy;
                $query->orderBy($actualSortBy, $sortOrder);
            }

            $totalItems = $query->count();
            $wishlistItems = $query->paginate($limit, ['*'], 'page', $page);
            
            $formattedItems = $this->formatWishlistItems($wishlistItems);
            
            return $this->apiResponse->successResponse([
                'wishlist' => $formattedItems,
                'pagination' => [
                    'currentPage' => $wishlistItems->currentPage(),
                    'totalPages' => $wishlistItems->lastPage(),
                    'totalItems' => $wishlistItems->total(),
                    'itemsPerPage' => $limit
                ]
            ]);
        } catch (\Exception $e) {
            return $this->apiResponse->errorResponse('Error fetching wishlist: ' . $e->getMessage());
        }
    }
    
    /**
     * Format wishlist items for API response
     */
    protected function formatWishlistItems($wishlistItems)
    {
        $formattedItems = [];
        
        foreach ($wishlistItems as $item) {
            if ($item->product) {
                $formattedItems[] = [
                    'id' => $item->id,
                    'wishlistItemId' => $item->id,
                    'productId' => $item->product_id,
                    'name' => $item->product->name,
                    'slug' => $item->product->slug,
                    'price' => (float) $item->product->unit_price,
                    'discountedPrice' => (float) home_discounted_price($item->product->id),
                    'image' => api_asset($item->product->thumbnail_img),
                    'category' => optional($item->product->category)->name,
                    'categoryId' => $item->product->category_id,
                    'rating' => (float) $item->product->rating,
                    'inStock' => $item->product->current_stock > 0,
                    'stock' => (int) $item->product->current_stock,
                    'dateAdded' => $item->created_at->format('Y-m-d H:i:s'),
                    'supplier' => optional($item->product->user)->name
                ];
            }
        }
        
        return $formattedItems;
    }
    
    /**
     * Add product to wishlist
     */
    public function add(Request $request)
    {
        try {
            $user = Auth::user();
            
            if (!$user) {
                return $this->apiResponse->notAuthorizedResponse();
            }
            
            $productId = $request->input('product_id');
            
            // Validate product ID
            if (!$productId) {
                return $this->apiResponse->validationErrorResponse('Product ID is required');
            }
            
            // Check if product exists
            $product = Product::find($productId);
            if (!$product) {
                return $this->apiResponse->notFoundResponse('Product not found');
            }
            
            // Check if product is already in wishlist
            $existingWishlistItem = Wishlist::where('user_id', $user->id)
                ->where('product_id', $productId)
                ->first();
                
            if ($existingWishlistItem) {
                return $this->apiResponse->successResponse([
                    'message' => 'Product is already in your wishlist',
                    'wishlistItemId' => $existingWishlistItem->id
                ]);
            }
            
            // Add to wishlist
            $wishlistItem = new Wishlist();
            $wishlistItem->user_id = $user->id;
            $wishlistItem->product_id = $productId;
            $wishlistItem->save();
            
            return $this->apiResponse->successResponse([
                'message' => 'Product added to wishlist successfully',
                'wishlistItemId' => $wishlistItem->id
            ]);
        } catch (\Exception $e) {
            return $this->apiResponse->errorResponse('Error adding product to wishlist: ' . $e->getMessage());
        }
    }
    
    /**
     * Delete item from wishlist
     */
    public function deleteFromWishlist(Request $request)
    {
        try {
            $user = Auth::user();
            
            if (!$user) {
                return $this->apiResponse->notAuthorizedResponse();
            }
            
            $wishlistItemId = $request->input('wishlist_item_id');
            
            if (!$wishlistItemId) {
                return $this->apiResponse->validationErrorResponse('Wishlist item ID is required');
            }
            
            $wishlistItem = Wishlist::where('id', $wishlistItemId)
                ->where('user_id', $user->id)
                ->first();
                
            if (!$wishlistItem) {
                return $this->apiResponse->notFoundResponse('Wishlist item not found');
            }
            
            $wishlistItem->delete();
            
            return $this->apiResponse->successResponse([
                'message' => 'Item removed from wishlist successfully'
            ]);
        } catch (\Exception $e) {
            return $this->apiResponse->errorResponse('Error removing item from wishlist: ' . $e->getMessage());
        }
    }
    
    /**
     * Clear entire wishlist
     */
    public function clearWishlist()
    {
        try {
            $user = Auth::user();
            
            if (!$user) {
                return $this->apiResponse->notAuthorizedResponse();
            }
            
            Wishlist::where('user_id', $user->id)->delete();
            
            return $this->apiResponse->successResponse([
                'message' => 'Wishlist cleared successfully'
            ]);
        } catch (\Exception $e) {
            return $this->apiResponse->errorResponse('Error clearing wishlist: ' . $e->getMessage());
        }
    }
    
    /**
     * Check if product is in wishlist
     */
    public function checkProductInWishlist(Request $request)
    {
        try {
            $user = Auth::user();
            
            if (!$user) {
                return $this->apiResponse->notAuthorizedResponse();
            }
            
            $productId = $request->input('product_id');
            
            if (!$productId) {
                return $this->apiResponse->validationErrorResponse('Product ID is required');
            }
            
            $wishlistItem = Wishlist::where('user_id', $user->id)
                ->where('product_id', $productId)
                ->first();
                
            return $this->apiResponse->successResponse([
                'inWishlist' => !!$wishlistItem,
                'wishlistItemId' => $wishlistItem ? $wishlistItem->id : null
            ]);
        } catch (\Exception $e) {
            return $this->apiResponse->errorResponse('Error checking wishlist status: ' . $e->getMessage());
        }
    }
    
    /**
     * Move wishlist items to cart
     */
    public function moveToCart(Request $request)
    {
        try {
            $user = Auth::user();
            
            if (!$user) {
                return $this->apiResponse->notAuthorizedResponse();
            }
            
            $items = $request->input('items', []);
            
            if (empty($items)) {
                return $this->apiResponse->validationErrorResponse('No items provided to move to cart');
            }
            
            $movedItems = [];
            
            foreach ($items as $item) {
                $wishlistItemId = $item['wishlistItemId'] ?? null;
                $quantity = $item['quantity'] ?? 1;
                
                if (!$wishlistItemId) {
                    continue;
                }
                
                $wishlistItem = Wishlist::where('id', $wishlistItemId)
                    ->where('user_id', $user->id)
                    ->first();
                    
                if (!$wishlistItem || !$wishlistItem->product) {
                    continue;
                }
                
                // Add to cart logic here
                // This is a stub - you would integrate with your cart system
                // For example: $cart->addItem($wishlistItem->product_id, $quantity);
                
                $movedItems[] = [
                    'wishlistItemId' => $wishlistItemId,
                    'productId' => $wishlistItem->product_id,
                    'quantity' => $quantity
                ];
            }
            
            return $this->apiResponse->successResponse([
                'message' => 'Items moved to cart successfully',
                'movedItems' => $movedItems
            ]);
        } catch (\Exception $e) {
            return $this->apiResponse->errorResponse('Error moving items to cart: ' . $e->getMessage());
        }
    }
    
    /**
     * Get wishlist statistics
     */
    public function getWishlistStats() : JsonResponse
    {
        try {
            
            $user = Auth::user();
            
            if (!$user) {
                return $this->apiResponse->notAuthorizedResponse();
            }
            
            // Add debugging to check user type
            \Log::info('Wishlist stats accessed by user:', [
                'user_id' => $user->id,
                'user_type' => $user->user_type ?? 'not_set',
                'email' => $user->email
            ]);
            
            $wishlistItems = Wishlist::with('product')
                ->where('user_id', $user->id)
                ->get();
                
            $totalItems = $wishlistItems->count();
            $totalValue = 0;
            $totalInStock = 0;
            $totalOutOfStock = 0;
            $prices = [];
            
            $categoryCount = [];
            $brandCount = [];
            
            $mostExpensiveItem = null;
            $leastExpensiveItem = null;
            $mostRecentItem = null;
            $oldestItem = null;
            
            foreach ($wishlistItems as $item) {
                if ($item->product) {
                    $discountedPrice = home_discounted_price($item->product, false);
                    // Parse the price if it's a range (e.g., "10 - 20")
                    if (is_string($discountedPrice) && strpos($discountedPrice, ' - ') !== false) {
                        $prices_range = explode(' - ', $discountedPrice);
                        $discountedPrice = (float) $prices_range[0]; // Use the lower price
                    } else {
                        $discountedPrice = (float) $discountedPrice;
                    }
                    
                    $totalValue += $discountedPrice;
                    $prices[] = $discountedPrice;
                    
                    // Count stock status
                    if ($item->product->current_stock > 0) {
                        $totalInStock++;
                    } else {
                        $totalOutOfStock++;
                    }
                    
                    // Track most/least expensive items
                    if (!$mostExpensiveItem) {
                        $mostExpensiveItem = $item;
                    } else {
                        $mostExpensivePrice = home_discounted_price($mostExpensiveItem->product, false);
                        if (is_string($mostExpensivePrice) && strpos($mostExpensivePrice, ' - ') !== false) {
                            $prices_range = explode(' - ', $mostExpensivePrice);
                            $mostExpensivePrice = (float) $prices_range[0];
                        } else {
                            $mostExpensivePrice = (float) $mostExpensivePrice;
                        }
                        
                        if ($discountedPrice > $mostExpensivePrice) {
                            $mostExpensiveItem = $item;
                        }
                    }
                    
                    if (!$leastExpensiveItem) {
                        $leastExpensiveItem = $item;
                    } else {
                        $leastExpensivePrice = home_discounted_price($leastExpensiveItem->product, false);
                        if (is_string($leastExpensivePrice) && strpos($leastExpensivePrice, ' - ') !== false) {
                            $prices_range = explode(' - ', $leastExpensivePrice);
                            $leastExpensivePrice = (float) $prices_range[0];
                        } else {
                            $leastExpensivePrice = (float) $leastExpensivePrice;
                        }
                        
                        if ($discountedPrice < $leastExpensivePrice) {
                            $leastExpensiveItem = $item;
                        }
                    }
                    
                    // Track most recent and oldest items
                    if (!$mostRecentItem || $item->created_at > $mostRecentItem->created_at) {
                        $mostRecentItem = $item;
                    }
                    if (!$oldestItem || $item->created_at < $oldestItem->created_at) {
                        $oldestItem = $item;
                    }
                    
                    // Count categories
                    if ($item->product->category_id) {
                        $categoryId = $item->product->category_id;
                        $categoryName = optional($item->product->category)->name ?? 'Unknown Category';
                        
                        if (!isset($categoryCount[$categoryName])) {
                            $categoryCount[$categoryName] = 0;
                        }
                        
                        $categoryCount[$categoryName]++;
                    }
                    
                    // Count brands
                    if ($item->product->brand_id) {
                        $brandId = $item->product->brand_id;
                        $brandName = optional($item->product->brand)->name ?? 'Unknown Brand';
                        
                        if (!isset($brandCount[$brandId])) {
                            $brandCount[$brandId] = [
                                'brandId' => $brandId,
                                'brandName' => $brandName,
                                'count' => 0
                            ];
                        }
                        
                        $brandCount[$brandId]['count']++;
                    }
                }
            }
            
            // Calculate average price
            $averagePrice = $totalItems > 0 ? $totalValue / $totalItems : 0;
            
            // Format popular categories
            $popularCategories = [];
            foreach ($categoryCount as $category => $count) {
                $popularCategories[] = [
                    'category' => $category,
                    'count' => $count
                ];
            }
            
            // Sort categories by count
            usort($popularCategories, function($a, $b) {
                return $b['count'] - $a['count'];
            });
            
            // Format expensive/recent items
            $formatWishlistItem = function($item) {
                if (!$item || !$item->product) return null;
                
                $discountedPrice = home_discounted_price($item->product, false);
                // Parse the price if it's a range
                if (is_string($discountedPrice) && strpos($discountedPrice, ' - ') !== false) {
                    $prices_range = explode(' - ', $discountedPrice);
                    $discountedPrice = (float) $prices_range[0];
                } else {
                    $discountedPrice = (float) $discountedPrice;
                }
                
                return [
                    'id' => $item->id,
                    'productId' => $item->product_id,
                    'name' => $item->product->name,
                    'slug' => $item->product->slug,
                    'price' => (float) $item->product->unit_price,
                    'discountedPrice' => $discountedPrice,
                    'image' => uploaded_asset($item->product->thumbnail_img),
                    'category' => optional($item->product->category)->name,
                    'categoryId' => $item->product->category_id,
                    'rating' => (float) $item->product->rating,
                    'inStock' => $item->product->current_stock > 0,
                    'stock' => (int) $item->product->current_stock,
                    'dateAdded' => $item->created_at->format('Y-m-d H:i:s'),
                    'supplier' => optional($item->product->user)->name
                ];
            };
            
            return $this->apiResponse->successResponse([
                'totalItems' => $totalItems,
                'totalInStock' => $totalInStock,
                'totalOutOfStock' => $totalOutOfStock,
                'totalValue' => $totalValue,
                'averagePrice' => $averagePrice,
                'mostExpensiveItem' => $formatWishlistItem($mostExpensiveItem),
                'leastExpensiveItem' => $formatWishlistItem($leastExpensiveItem),
                'mostRecentItem' => $formatWishlistItem($mostRecentItem),
                'oldestItem' => $formatWishlistItem($oldestItem),
                'popularCategories' => $popularCategories
            ]);
        } catch (\Exception $e) {
            return $this->apiResponse->errorResponse('Error fetching wishlist stats: ' . $e->getMessage());
        }
    }
    
    /**
     * Check migration status
     */
    public function checkMigrationStatus(Request $request)
    {
        try {
            $user = Auth::user();
            
            if (!$user) {
                return $this->apiResponse->notAuthorizedResponse();
            }
            
            // This is a stub - you would implement your actual migration check logic
            $userType = $request->input('userType', 'customer');
            $isMigrated = $user->wishlist_migrated_to_v3 ?? false;
            
            return $this->apiResponse->successResponse([
                'isMigrated' => $isMigrated,
                'totalItems' => Wishlist::where('user_id', $user->id)->count(),
                'canMigrate' => !$isMigrated
            ]);
        } catch (\Exception $e) {
            return $this->apiResponse->errorResponse('Error checking migration status: ' . $e->getMessage());
        }
    }
    
    /**
     * Migrate wishlist
     */
    public function migrateWishlist(Request $request)
    {
        try {
            $user = Auth::user();
            
            if (!$user) {
                return $this->apiResponse->notAuthorizedResponse();
            }
            
            // This is a stub - you would implement your actual migration logic
            $userType = $request->input('userType', 'customer');
            
            // Simulate migration
            $itemsCount = Wishlist::where('user_id', $user->id)->count();
            $user->wishlist_migrated_to_v3 = true;
            $user->save();
            
            return $this->apiResponse->successResponse([
                'success' => true,
                'message' => 'Wishlist migrated successfully',
                'migratedItems' => $itemsCount
            ]);
        } catch (\Exception $e) {
            return $this->apiResponse->errorResponse('Error migrating wishlist: ' . $e->getMessage());
        }
    }
} 