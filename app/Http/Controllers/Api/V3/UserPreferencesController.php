<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Http\Resources\V3\UserPreferencesResource;
use App\Models\UserPreference;
use App\Models\UserNotificationPreferences;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class UserPreferencesController extends ApiResponse
{
    /**
     * Get all user preferences
     * 
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        $user = Auth::user();
        $preferences = $user->preferences()->pluck('value', 'key')->toArray();

        // Default preferences
        $defaultPreferences = [
            'theme' => 'system',
            'language' => 'en',
            'currency' => 'USD',
            'notifications' => [
                'email' => true,
                'push' => false,
                'sms' => false,
                'orderUpdates' => true,
                'promotions' => true,
                'accountAlerts' => true
            ]
        ];

        // Merge with notifications from notification preferences
        $notificationPrefs = $user->notificationPreferences()->pluck('value', 'key')->toArray();
        $defaultPreferences['notifications'] = array_merge($defaultPreferences['notifications'], [
            'orderUpdates' => $notificationPrefs['order_updates'] ?? true,
            'promotions' => $notificationPrefs['system_announcements'] ?? true,
            'accountAlerts' => $notificationPrefs['system_announcements'] ?? true,
            'email' => $notificationPrefs['email_notifications'] ?? true,
            'push' => $notificationPrefs['push_notifications'] ?? false,
        ]);

        // Merge with user preferences, using defaults if not set
        foreach ($preferences as $key => $value) {
            if ($key == 'theme' || $key == 'language' || $key == 'currency') {
                $defaultPreferences[$key] = $value;
            }
        }

        return $this->success($defaultPreferences);
    }

    /**
     * Update user preferences
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function update(Request $request): JsonResponse
    {
        $user = Auth::user();
        $validator = Validator::make($request->all(), [
            'theme' => 'nullable|string|in:light,dark,system',
            'language' => 'nullable|string|max:10',
            'currency' => 'nullable|string|max:10',
            'notifications' => 'nullable|array',
            'notifications.email' => 'nullable|boolean',
            'notifications.push' => 'nullable|boolean',
            'notifications.sms' => 'nullable|boolean',
            'notifications.orderUpdates' => 'nullable|boolean',
            'notifications.promotions' => 'nullable|boolean',
            'notifications.accountAlerts' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid parameters',
                $validator->errors()->messages(),
                422
            );
        }

        $validatedData = $validator->validated();

        // Update theme, language, currency preferences
        foreach (['theme', 'language', 'currency'] as $key) {
            if (isset($validatedData[$key])) {
                UserPreference::updateOrCreate(
                    ['user_id' => $user->id, 'key' => $key],
                    ['value' => $validatedData[$key]]
                );
            }
        }

        // Update notification preferences
        if (isset($validatedData['notifications'])) {
            $notificationData = $validatedData['notifications'];
            
            // Map to backend notification keys
            $mappings = [
                'email' => 'email_notifications',
                'push' => 'push_notifications',
                'orderUpdates' => 'order_updates',
                'promotions' => 'system_announcements',
                'accountAlerts' => 'system_announcements'
            ];

            foreach ($mappings as $frontendKey => $backendKey) {
                if (isset($notificationData[$frontendKey])) {
                    UserNotificationPreferences::updateOrCreate(
                        ['user_id' => $user->id, 'key' => $backendKey],
                        ['value' => $notificationData[$frontendKey]]
                    );
                }
            }
        }

        // Return updated preferences
        return $this->index();
    }

    /**
     * Get theme preference
     * 
     * @return JsonResponse
     */
    public function getTheme(): JsonResponse
    {
        $user = Auth::user();
        $theme = $user->preferences()->where('key', 'theme')->first();
        
        return $this->success([
            'theme' => $theme ? $theme->value : 'system'
        ]);
    }

    /**
     * Update theme preference
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function updateTheme(Request $request): JsonResponse
    {
        $user = Auth::user();
        $validator = Validator::make($request->all(), [
            'theme' => 'required|string|in:light,dark,system',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid parameters',
                $validator->errors()->messages(),
                422
            );
        }

        $theme = $request->input('theme');
        
        UserPreference::updateOrCreate(
            ['user_id' => $user->id, 'key' => 'theme'],
            ['value' => $theme]
        );

        return $this->success([
            'theme' => $theme
        ]);
    }

    /**
     * Get notification preferences
     * 
     * @return JsonResponse
     */
    public function getNotificationPreferences(): JsonResponse
    {
        $user = Auth::user();
        $notificationPrefs = $user->notificationPreferences()->pluck('value', 'key')->toArray();
        
        $preferences = [
            'order_updates' => $notificationPrefs['order_updates'] ?? true,
            'stock_alerts' => $notificationPrefs['stock_alerts'] ?? true,
            'product_changes' => $notificationPrefs['product_changes'] ?? true,
            'commission_notifications' => $notificationPrefs['commission_notifications'] ?? true,
            'support_messages' => $notificationPrefs['support_messages'] ?? true,
            'system_announcements' => $notificationPrefs['system_announcements'] ?? true,
            'email_notifications' => $notificationPrefs['email_notifications'] ?? true,
            'push_notifications' => $notificationPrefs['push_notifications'] ?? false,
            'sms_notifications' => $notificationPrefs['sms_notifications'] ?? false,
            'notification_sound' => $notificationPrefs['notification_sound'] ?? true,
            'browserNotifications' => $notificationPrefs['browserNotifications'] ?? false,
            'payment_alerts' => $notificationPrefs['payment_alerts'] ?? true,
            'promotions' => $notificationPrefs['promotions'] ?? false,
            'coupons' => $notificationPrefs['coupons'] ?? false,
            'support_alerts' => $notificationPrefs['support_alerts'] ?? true,
            'system_alerts' => $notificationPrefs['system_alerts'] ?? true,
            'vibration_alerts' => $notificationPrefs['vibration_alerts'] ?? false,
        ];

        return $this->success($preferences);
    }

    /**
     * Update notification preferences
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function updateNotificationPreferences(Request $request): JsonResponse
    {
        $user = Auth::user();
        $validator = Validator::make($request->all(), [
            'order_updates' => 'nullable|boolean',
            'stock_alerts' => 'nullable|boolean',
            'product_changes' => 'nullable|boolean',
            'commission_notifications' => 'nullable|boolean',
            'support_messages' => 'nullable|boolean',
            'system_announcements' => 'nullable|boolean',
            'email_notifications' => 'nullable|boolean',
            'push_notifications' => 'nullable|boolean',
            'sms_notifications' => 'nullable|boolean',
            'notification_sound' => 'nullable|boolean',
            'browserNotifications' => 'nullable|boolean',
            'payment_alerts' => 'nullable|boolean',
            'promotions' => 'nullable|boolean',
            'coupons' => 'nullable|boolean',
            'support_alerts' => 'nullable|boolean',
            'system_alerts' => 'nullable|boolean',
            'vibration_alerts' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid parameters',
                $validator->errors()->messages(),
                422
            );
        }

        $validatedData = $validator->validated();
        
        // Update each preference
        foreach ($validatedData as $key => $value) {
            if ($value !== null) {
                UserNotificationPreferences::updateOrCreate(
                    ['user_id' => $user->id, 'key' => $key],
                    ['value' => $value]
                );
            }
        }

        return $this->getNotificationPreferences();
    }
} 