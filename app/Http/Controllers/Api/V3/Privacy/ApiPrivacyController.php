<?php

namespace App\Http\Controllers\Api\V3\Privacy;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Models\User;
use App\Models\PrivacyConsent;
use App\Models\PrivacyPolicy;
use App\Models\UserPrivacySetting;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Carbon;

class ApiPrivacyController extends ApiResponse
{
    /**
     * Get privacy settings
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getPrivacySettings(Request $request): JsonResponse
    {
        $user = $request->user();
        
        // Get user's privacy settings
        $settings = UserPrivacySetting::where('user_id', $user->id)->first();
        
        if (!$settings) {
            // Create default settings if not found
            $settings = new UserPrivacySetting();
            $settings->user_id = $user->id;
            $settings->marketing_emails = false;
            $settings->third_party_cookies = false;
            $settings->analytics_tracking = false;
            $settings->data_sharing = false;
            $settings->personalization = false;
            $settings->save();
        }
        
        return $this->success([
            'marketingEmails' => (bool)$settings->marketing_emails,
            'thirdPartyCookies' => (bool)$settings->third_party_cookies,
            'analyticsTracking' => (bool)$settings->analytics_tracking,
            'dataSharing' => (bool)$settings->data_sharing,
            'personalization' => (bool)$settings->personalization,
            'updatedAt' => $settings->updated_at->toISOString(),
        ]);
    }
    
    /**
     * Update privacy settings
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function updatePrivacySettings(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'marketingEmails' => 'sometimes|boolean',
            'thirdPartyCookies' => 'sometimes|boolean',
            'analyticsTracking' => 'sometimes|boolean',
            'dataSharing' => 'sometimes|boolean',
            'personalization' => 'sometimes|boolean',
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid privacy settings',
                $validator->errors()->messages(),
                422
            );
        }
        
        $user = $request->user();
        
        // Get or create user's privacy settings
        $settings = UserPrivacySetting::firstOrNew(['user_id' => $user->id]);
        
        // Update settings if provided
        if ($request->has('marketingEmails')) {
            $settings->marketing_emails = $request->marketingEmails;
        }
        
        if ($request->has('thirdPartyCookies')) {
            $settings->third_party_cookies = $request->thirdPartyCookies;
        }
        
        if ($request->has('analyticsTracking')) {
            $settings->analytics_tracking = $request->analyticsTracking;
        }
        
        if ($request->has('dataSharing')) {
            $settings->data_sharing = $request->dataSharing;
        }
        
        if ($request->has('personalization')) {
            $settings->personalization = $request->personalization;
        }
        
        $settings->save();
        
        return $this->getPrivacySettings($request);
    }
    
    /**
     * Get user consents
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getConsents(Request $request): JsonResponse
    {
        $user = $request->user();
        
        // Get user's consents
        $consents = PrivacyConsent::where('user_id', $user->id)->get()->map(function ($consent) {
            return [
                'id' => $consent->id,
                'type' => $consent->type,
                'description' => $consent->description,
                'granted' => (bool)$consent->granted,
                'grantedAt' => $consent->granted_at ? Carbon::parse($consent->granted_at)->toISOString() : null,
                'revokedAt' => $consent->revoked_at ? Carbon::parse($consent->revoked_at)->toISOString() : null,
                'expiresAt' => $consent->expires_at ? Carbon::parse($consent->expires_at)->toISOString() : null,
                'version' => $consent->version,
            ];
        });
        
        // If no consents found, return default required consents
        if ($consents->isEmpty()) {
            $defaultConsents = $this->getDefaultConsents();
            
            return $this->success([
                'consents' => $defaultConsents,
            ]);
        }
        
        return $this->success([
            'consents' => $consents,
        ]);
    }
    
    /**
     * Update a consent
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function updateConsent(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|string',
            'granted' => 'required|boolean',
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid consent information',
                $validator->errors()->messages(),
                422
            );
        }
        
        $user = $request->user();
        
        // If it's a default consent that doesn't exist yet, create it
        if (in_array($request->id, ['marketing', 'analytics', 'personalization', 'third_party_sharing', 'data_processing'])) {
            $consent = PrivacyConsent::firstOrNew([
                'user_id' => $user->id,
                'type' => $request->id,
            ]);
            
            // Set default description based on type
            if (!$consent->exists) {
                $consent->description = $this->getConsentDescription($request->id);
                $consent->version = '1.0';
            }
        } else {
            // Otherwise, find the existing consent
            $consent = PrivacyConsent::where('user_id', $user->id)
                ->where('id', $request->id)
                ->first();
            
            if (!$consent) {
                return $this->error(
                    'NOT_FOUND',
                    'Consent not found',
                    'The specified consent does not exist',
                    404
                );
            }
        }
        
        // Update granted status
        $consent->granted = $request->granted;
        
        if ($request->granted) {
            $consent->granted_at = now();
            $consent->revoked_at = null;
        } else {
            $consent->revoked_at = now();
        }
        
        $consent->save();
        
        return $this->success([
            'id' => $consent->id,
            'type' => $consent->type,
            'description' => $consent->description,
            'granted' => (bool)$consent->granted,
            'grantedAt' => $consent->granted_at ? Carbon::parse($consent->granted_at)->toISOString() : null,
            'revokedAt' => $consent->revoked_at ? Carbon::parse($consent->revoked_at)->toISOString() : null,
            'expiresAt' => $consent->expires_at ? Carbon::parse($consent->expires_at)->toISOString() : null,
            'version' => $consent->version,
        ]);
    }
    
    /**
     * Request data export
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function requestDataExport(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'dataTypes' => 'required|array',
            'dataTypes.*' => 'required|string|in:profile,orders,addresses,payment_methods,communications,browsing_history,wishlist,reviews',
            'format' => 'required|string|in:json,csv,pdf',
            'timeRange.from' => 'sometimes|date',
            'timeRange.to' => 'sometimes|date|after_or_equal:timeRange.from',
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid export parameters',
                $validator->errors()->messages(),
                422
            );
        }
        
        $user = $request->user();
        
        // In a real implementation, we would queue a job to generate the export
        // For now, we'll just return a success message
        
        return $this->success([
            'requestId' => 'export_' . uniqid(),
            'estimatedCompletionTime' => Carbon::now()->addHours(24)->toISOString(),
            'status' => 'processing',
        ], 'Data export request has been submitted');
    }
    
    /**
     * Request account deletion
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function requestAccountDeletion(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'password' => 'required|string',
            'reason' => 'sometimes|nullable|string|max:1000',
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                422
            );
        }
        
        $user = $request->user();
        
        if (!Hash::check($request->password, $user->password)) {
            return $this->error(
                'INVALID_PASSWORD',
                'The password is incorrect',
                'Please provide your correct password to request account deletion',
                401
            );
        }
        
        // In a real implementation, we would schedule the account for deletion
        // For now, we'll just mark it for deletion
        
        $user->scheduled_for_deletion = true;
        $user->deletion_scheduled_at = now();
        $user->deletion_reason = $request->reason;
        $user->save();
        
        // Revoke all tokens
        $user->tokens()->delete();
        
        return $this->success([
            'scheduledDeletionDate' => Carbon::now()->addDays(30)->toISOString(),
        ], 'Your account has been scheduled for deletion');
    }
    
    /**
     * Request data rectification
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function requestDataRectification(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'fieldName' => 'required|string',
            'currentValue' => 'required|string',
            'newValue' => 'required|string',
            'reason' => 'sometimes|nullable|string|max:1000',
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                422
            );
        }
        
        // In a real implementation, we would create a data rectification request
        // For now, we'll just return a success message
        
        return $this->success([
            'requestId' => 'rectify_' . uniqid(),
            'status' => 'pending',
            'estimatedCompletionTime' => Carbon::now()->addDays(7)->toISOString(),
        ], 'Data rectification request has been submitted');
    }
    
    /**
     * Get privacy policies
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getPrivacyPolicies(Request $request): JsonResponse
    {
        // Get all privacy policies
        $policies = PrivacyPolicy::orderBy('effective_date', 'desc')->get()->map(function ($policy) {
            return [
                'id' => $policy->id,
                'version' => $policy->version,
                'effectiveDate' => Carbon::parse($policy->effective_date)->toISOString(),
                'content' => $policy->content,
                'isActive' => (bool)$policy->is_active,
            ];
        });
        
        return $this->success([
            'policies' => $policies,
        ]);
    }
    
    /**
     * Get active privacy policy
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getActivePrivacyPolicy(Request $request): JsonResponse
    {
        // Get the active privacy policy
        $policy = PrivacyPolicy::where('is_active', true)->first();
        
        if (!$policy) {
            return $this->error(
                'NOT_FOUND',
                'No active privacy policy found',
                'There is no active privacy policy at this time',
                404
            );
        }
        
        // If user is authenticated, check if they've accepted this policy
        $acceptedAt = null;
        if ($request->user()) {
            $user = $request->user();
            $policyAcceptance = PrivacyConsent::where('user_id', $user->id)
                ->where('type', 'privacy_policy')
                ->where('version', $policy->version)
                ->where('granted', true)
                ->first();
            
            if ($policyAcceptance) {
                $acceptedAt = $policyAcceptance->granted_at;
            }
        }
        
        return $this->success([
            'id' => $policy->id,
            'version' => $policy->version,
            'effectiveDate' => Carbon::parse($policy->effective_date)->toISOString(),
            'content' => $policy->content,
            'isActive' => true,
            'acceptedAt' => $acceptedAt ? Carbon::parse($acceptedAt)->toISOString() : null,
        ]);
    }
    
    /**
     * Accept privacy policy
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function acceptPrivacyPolicy(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'policyId' => 'required|string',
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide a valid policy ID',
                $validator->errors()->messages(),
                422
            );
        }
        
        $user = $request->user();
        
        // Get the policy
        $policy = PrivacyPolicy::find($request->policyId);
        
        if (!$policy) {
            return $this->error(
                'NOT_FOUND',
                'Privacy policy not found',
                'The specified privacy policy does not exist',
                404
            );
        }
        
        // Create or update consent record
        $consent = PrivacyConsent::firstOrNew([
            'user_id' => $user->id,
            'type' => 'privacy_policy',
            'version' => $policy->version,
        ]);
        
        $consent->description = 'Privacy Policy v' . $policy->version;
        $consent->granted = true;
        $consent->granted_at = now();
        $consent->revoked_at = null;
        $consent->save();
        
        return $this->success([
            'acceptedAt' => $consent->granted_at->toISOString(),
        ], 'Privacy policy accepted successfully');
    }
    
    /**
     * Get processing activities
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getProcessingActivities(Request $request): JsonResponse
    {
        // In a real implementation, we would fetch actual processing activities
        // For now, we'll return a static list
        
        return $this->success([
            'activities' => [
                [
                    'id' => 'proc_1',
                    'purpose' => 'Order Processing',
                    'dataCategories' => ['personal_details', 'contact_information', 'payment_information', 'shipping_details'],
                    'legalBasis' => 'contract',
                    'retention' => '7 years after order completion',
                    'recipients' => ['payment_processors', 'shipping_providers'],
                    'crossBorderTransfers' => [],
                    'createdAt' => Carbon::now()->subYears(2)->toISOString(),
                    'updatedAt' => Carbon::now()->subMonths(6)->toISOString(),
                ],
                [
                    'id' => 'proc_2',
                    'purpose' => 'Marketing Communications',
                    'dataCategories' => ['contact_information', 'preferences'],
                    'legalBasis' => 'consent',
                    'retention' => 'Until consent is withdrawn',
                    'recipients' => ['marketing_service_providers'],
                    'crossBorderTransfers' => [],
                    'createdAt' => Carbon::now()->subYears(2)->toISOString(),
                    'updatedAt' => Carbon::now()->subMonths(3)->toISOString(),
                ],
                [
                    'id' => 'proc_3',
                    'purpose' => 'Website Analytics',
                    'dataCategories' => ['usage_data', 'device_information'],
                    'legalBasis' => 'legitimate_interest',
                    'retention' => '26 months',
                    'recipients' => ['analytics_providers'],
                    'crossBorderTransfers' => ['United States'],
                    'createdAt' => Carbon::now()->subYears(1)->toISOString(),
                    'updatedAt' => Carbon::now()->subMonths(1)->toISOString(),
                ],
            ],
        ]);
    }
    
    /**
     * Get default consents
     * 
     * @return array
     */
    private function getDefaultConsents(): array
    {
        return [
            [
                'id' => 'marketing',
                'type' => 'marketing',
                'description' => $this->getConsentDescription('marketing'),
                'granted' => false,
                'grantedAt' => null,
                'revokedAt' => null,
                'expiresAt' => null,
                'version' => '1.0',
            ],
            [
                'id' => 'analytics',
                'type' => 'analytics',
                'description' => $this->getConsentDescription('analytics'),
                'granted' => false,
                'grantedAt' => null,
                'revokedAt' => null,
                'expiresAt' => null,
                'version' => '1.0',
            ],
            [
                'id' => 'personalization',
                'type' => 'personalization',
                'description' => $this->getConsentDescription('personalization'),
                'granted' => false,
                'grantedAt' => null,
                'revokedAt' => null,
                'expiresAt' => null,
                'version' => '1.0',
            ],
            [
                'id' => 'third_party_sharing',
                'type' => 'third_party_sharing',
                'description' => $this->getConsentDescription('third_party_sharing'),
                'granted' => false,
                'grantedAt' => null,
                'revokedAt' => null,
                'expiresAt' => null,
                'version' => '1.0',
            ],
            [
                'id' => 'data_processing',
                'type' => 'data_processing',
                'description' => $this->getConsentDescription('data_processing'),
                'granted' => false,
                'grantedAt' => null,
                'revokedAt' => null,
                'expiresAt' => null,
                'version' => '1.0',
            ],
        ];
    }
    
    /**
     * Get consent description based on type
     * 
     * @param string $type
     * @return string
     */
    private function getConsentDescription($type): string
    {
        switch ($type) {
            case 'marketing':
                return 'I consent to receive marketing communications about products, services, and promotions via email, SMS, and other channels.';
            
            case 'analytics':
                return 'I consent to the collection and processing of my browsing data for analytics purposes to improve the website and services.';
            
            case 'personalization':
                return 'I consent to the use of my personal data and browsing behavior to personalize content, recommendations, and offers.';
            
            case 'third_party_sharing':
                return 'I consent to the sharing of my personal data with third-party partners for purposes described in the privacy policy.';
            
            case 'data_processing':
                return 'I consent to the processing of my personal data as described in the privacy policy for the purposes of providing the service.';
            
            default:
                return 'Consent for data processing activities.';
        }
    }
} 