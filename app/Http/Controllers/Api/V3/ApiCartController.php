<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Http\Resources\V3\Cart\CartInfosResource;
use App\Http\Resources\V3\Cart\CartItemResource;
use App\Http\Resources\V3\Cart\CartSummaryResource;
use App\Http\Resources\V3\Cart\CheckoutSummaryResource;
use App\Http\Resources\V3\CartCollectionResource;
use App\Http\Resources\V3\ProductsResource;
use App\Http\Resources\V3\ShippingAddress\ShippingAddressResource;
use App\Http\Resources\V3\StripePaymentMethod\StripeCardsResource;
use App\Models\Address;
use App\Models\Cart;
use App\Models\CartInfo;
use App\Models\Coupon;
use App\Models\CouponUsage;
use App\Models\Product;
use App\Models\StripeCard;
use App\Services\CartService;
use App\Services\OrderProcessingService;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Artisan;
use App\Models\Order;
use App\Models\OrderDetail;
use Carbon\Carbon;
use App\Services\ApiOrderService;
use App\Services\StripeService;
use App\Services\OrderService;
use App\Services\ActivityLogService;

class ApiCartController extends ApiResponse
{
    protected CartService $cartService;
    /**
     * @var \App\Services\OrderService
     */
    protected OrderService $orderService;
    protected StripeService $stripeService;
    protected ActivityLogService $activityLogService;
    protected ApiOrderService $apiOrderService;
    protected OrderProcessingService $orderProcessingService;
    public function __construct(
        StripeService $stripeService,
        OrderService $orderService,
        ActivityLogService $activityLogService,
        ApiOrderService $apiOrderService,
        OrderProcessingService $orderProcessingService,
        CartService $cartService
    ) {
        parent::__construct();
        $this->stripeService = $stripeService;
        $this->orderService = $orderService;
        $this->activityLogService = $activityLogService;
        $this->apiOrderService = $apiOrderService;
        $this->orderProcessingService = $orderProcessingService;
        $this->cartService = $cartService;
    }

    public function index(Request $request, $cart_info_id)
    {
        $cartInfo = CartInfo::with('carts');
        $cartInfo->where('id', $cart_info_id);
        $cartInfo = $cartInfo->first();
        if (!$cartInfo) {
            return $this->error(
                'Cart Not Found',
                'The requested cart could not be found',
                null,
                404
            );
        }
        //dd(new CartInfosResource($cartInfo));
        return $this->success(
            new CartInfosResource($cartInfo),
            'Product added to cart successfully'
        );

    }



    public function user_cart(Request $request)
    {
        $addressResponse = null;
        $user_id = auth()->user()->id;
        $cartInfo = CartInfo::with('carts');
        $cartInfo->where('user_id', $user_id);
        $cartInfo = $cartInfo->first();
        if (!$cartInfo) {
            return $this->error(
                'Cart Not Found',
                'The requested cart could not be found',
                null,
                404
            );
        }
        $cartProducts = Cart::query();
        $cartProducts = $cartProducts->where('cart_info_id', $cartInfo->id)->where('user_id', auth()->user()->id)->get();

        if ($cartInfo->address_id != null) {
            $address = Address::where('id', $cartInfo->address_id)
                ->where('user_id', auth()->user()->id)
                ->first();
            $addressResponse = new ShippingAddressResource($address);
        }
        return $this->success(new CartInfosResource($cartInfo), 'Product added to cart successfully');

    }


    public function changeQuantity(Request $request, $id): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'quantity' => 'required|numeric|min:1',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        $request_product_qty = $request->input('quantity');
        $cart = Cart::find($id);
        if (!$cart) {
            return $this->error(
                'Cart Not Found',
                'The requested cart could not be found',
                null,
                404
            );
        }
        $product = Product::find($cart->product_id);
        if ($this->cartService->checkProductStockQty($product, $request->quantity, $cart->variation)) {
            return $this->error(
                'Out of Stock',
                'Product is out of stock',
                null,
                200
            );
        }
        $cart->update([
            'quantity' => $request_product_qty
        ]);
        $cartInfo = CartInfo::with('carts')->where('id', $cart->cart_info_id)->first();
        $responce = [
            'cart' => new CartInfosResource($cartInfo),
            'updated_item' => new CartItemResource($cart),
        ];
        return $this->success(
            $responce,
            'Product quantity updated successfully',
            200
        );
    }

    public function destroy($id): \Illuminate\Http\JsonResponse
    {
        $cart = Cart::find($id);
        if (!$cart) {
            return $this->error(
                'Cart Not Found',
                'The requested cart could not be found',
                null,
                404
            );
        }
        Cart::destroy($id);
        $cartInfo = CartInfo::with('carts')->where('id', $cart->cart_info_id)->first();
        return $this->success(new CartInfosResource($cartInfo), 'Product is successfully removed from your cart', 200);
    }
    public function destroy_cart(): \Illuminate\Http\JsonResponse
    {
        $cartInfo = CartInfo::where('user_id', auth()->user()->id)->first();
        if (!$cartInfo) {
            return $this->error(
                'Cart Not Found',
                'The requested cart could not be found',
                null,
                404
            );
        }
        $user_id = auth()->user()->id;
        CartInfo::where('user_id', $user_id)->delete();
        Cart::where('user_id', $user_id)->delete();


        return $this->success(
            [],
            'Cart is successfully cleared',
            200
        );
    }
    public function tag_user_id_in_cart(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'cart_info_id' => 'required|exists:cart_info,id',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        $user_id = auth()->user()->id;

        $cart_info_id = $request->cart_info_id;
        try {
            CartInfo::where('id', $cart_info_id)
                ->update(['user_id' => $user_id]);

            Cart::where('cart_info_id', $cart_info_id)
                ->update(['user_id' => $user_id]);

            $cartInfo = CartInfo::with('carts')->where('id', $cart_info_id)->first();
            $shipping_address = null;
            if ($cartInfo->address_id != null || $cartInfo->address_id != '') {
                $address = Address::where('id', $cartInfo->address_id)
                    ->where('user_id', auth()->user()->id)
                    ->first();
                if ($address) {
                    $shipping_address = new ShippingAddressResource($address);
                }
            }
            $rasponce = [
                'cart' => new CartInfosResource($cartInfo),
                'shipping_address' => $shipping_address,
            ];
            return $this->success(
                $rasponce,
                'Successfully tagged user id',
                200
            );

        } catch (\Exception $e) {
            Log::channel('api_cart')->error('Error in Tagging user id : ApiCartController ' . print_r($e->getMessage(), true));
            $this->error('Something went wrong', 'Something went wrong', null, 200);
        }
    }
    public function attached_address_shipping_id_to_user_card(Request $request): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'shipping_address_id' => 'required|exists:addresses,id',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        $user_id = auth()->user()->id;
        $address_id = $request->shipping_address_id;
        $billing_address_id = $request->billing_address_id;
        $shipping_method_id = $request->shipping_method_id;
        $address_info = Address::where('user_id', $user_id)
            ->where('id', $address_id)
            ->first();
        if (!$address_info) {
            return $this->error(
                'Address Not Found',
                'The requested address could not be found',
                null,
                404
            );
        }

        try {
            CartInfo::where('user_id', $user_id)
                ->update(['address_id' => $address_id]);
            Cart::where('user_id', $user_id)
                ->update(['address_id' => $address_id]);
            $paymentMethodResponse = [];
            $paymentMethods = StripeCard::where('user_id', $user_id)->get();
            if ($paymentMethods) {
                $paymentMethodResponse = new StripeCardsResource($paymentMethods);
            }
            $cartInfo = CartInfo::where('user_id', $user_id)->first();
            $response = [
                'checkout_id' => $cartInfo->id,
                'cart' => new CartInfosResource($cartInfo),
                'shipping_address' => new ShippingAddressResource($address_info),
                'billing_address' => [],
                'shipping_method' => [],
                'available_payment_methods' => $paymentMethodResponse,
            ];



            return $this->success(
                $response,
                'Successfully Started Checkout',
                200
            );
        } catch (\Exception $e) {
            Log::channel('api_cart')->error('Error Attached Address in attached_address_id_to_user_card : ApiCartController ' . print_r($e->getMessage(), true));
            return $this->error(
                'Something went wrong',
                'Something went wrong',
                null,
                200
            );
        }
    }
    /**
     * Add a product to cart (v2)
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function addToCart(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'product_id' => 'required|integer|exists:products,id',
                'quantity' => 'required|integer|min:1',
                'options' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return $this->validation_error(
                    400,
                    'Please provide valid information',
                    $validator->errors()->messages(),
                    400
                );
            }

            $user_id = auth()->check() ? auth()->id() : null;
            $temp_user_id = $request->header('X-Temp-User-Id');
            $cart_id = $request->header('X-Cart-Id');
            
            // Ensure we have either a user ID or temp user ID
            if (!$user_id && !$temp_user_id) {
                return $this->error(
                    'Cart Error',
                    'User identification required',
                    null,
                    400
                );
            }
            
            // Find or create cart info
            $cartInfo = null;
            
            if ($cart_id) {
                $cartInfo = CartInfo::find($cart_id);
            } elseif ($user_id) {
                $cartInfo = CartInfo::where('user_id', $user_id)->first();
            } elseif ($temp_user_id) {
                $cartInfo = CartInfo::where('temp_user_id', $temp_user_id)->first();
            }
            
            if (!$cartInfo) {
                $cartInfo = new CartInfo();
                $cartInfo->user_id = $user_id;
                $cartInfo->temp_user_id = $temp_user_id;
                $cartInfo->save();
            }
            
            // Get product
            $product = Product::find($request->product_id);
            if (!$product) {
                return $this->error(
                    'Product Not Found',
                    'The requested product could not be found',
                    null,
                    404
                );
            }
            
            // Prepare variant
            $options = $request->options ?? [];
            $variation = !empty($options) ? json_encode($options) : '';
            
            // Check stock
            $availableStock = $this->cartService->getProductStock($product, $variation);
            
            // Log stock check details
            Log::channel('api_cart')->info('Stock validation for add to cart', [
                'product_id' => $product->id,
                'product_name' => $product->name,
                'variation' => $variation,
                'requested_quantity' => $request->quantity,
                'available_stock' => $availableStock,
                'product_digital' => $product->digital,
                'product_variant_product' => $product->variant_product ?? 0,
            ]);
            
            if ($availableStock < $request->quantity) {
                Log::channel('api_cart')->warning('Insufficient stock detected', [
                    'product_id' => $product->id,
                    'requested_quantity' => $request->quantity,
                    'available_stock' => $availableStock,
                    'variation' => $variation,
                ]);
                
                return $this->error(
                    'Insufficient Stock',
                    'The requested quantity exceeds available stock',
                    [
                        'available_quantity' => $availableStock,
                        'requested_quantity' => $request->quantity,
                        'product_id' => $product->id,
                        'variation' => $variation,
                    ],
                    400
                );
            }
            
            // Calculate price
            $price = $this->cartService->calculateProductPrice($product, $variation);
            
            // Check if item already exists in cart
            $existingItem = Cart::where('cart_info_id', $cartInfo->id)
                ->where('product_id', $product->id)
                ->where('variation', $variation)
                ->first();
            
            if ($existingItem) {
                // Update quantity
                $newQuantity = $existingItem->quantity + $request->quantity;
                
                // Log existing item update
                Log::channel('api_cart')->info('Updating existing cart item', [
                    'cart_item_id' => $existingItem->id,
                    'product_id' => $product->id,
                    'current_quantity' => $existingItem->quantity,
                    'adding_quantity' => $request->quantity,
                    'new_total_quantity' => $newQuantity,
                    'available_stock' => $availableStock,
                ]);
                
                // Check if new quantity exceeds stock
                if ($newQuantity > $availableStock) {
                    Log::channel('api_cart')->warning('New quantity exceeds stock for existing item', [
                        'cart_item_id' => $existingItem->id,
                        'product_id' => $product->id,
                        'new_quantity' => $newQuantity,
                        'available_stock' => $availableStock,
                        'variation' => $variation,
                    ]);
                    
                    return $this->error(
                        'Insufficient Stock',
                        'The requested quantity exceeds available stock',
                        [
                            'available_quantity' => $availableStock,
                            'requested_total_quantity' => $newQuantity,
                            'current_cart_quantity' => $existingItem->quantity,
                            'product_id' => $product->id,
                            'variation' => $variation,
                        ],
                        400
                    );
                }
                
                $existingItem->quantity = $newQuantity;
                $existingItem->save();
                $item = $existingItem;
                
                Log::channel('api_cart')->info('Successfully updated existing cart item', [
                    'cart_item_id' => $existingItem->id,
                    'final_quantity' => $newQuantity,
                ]);
            } else {
                // Create new cart item
                $item = new Cart();
                $item->cart_info_id = $cartInfo->id;
                $item->user_id = $user_id;
                $item->temp_user_id = $temp_user_id;
                $item->product_id = $product->id;
                $item->owner_id = $product->user_id;
                $item->variation = $variation;
                $item->price = $price;
                $item->quantity = $request->quantity;
                $item->save();
            }
            
            // Get the updated cart with the new item
            $cart = $this->getCart($request);
            
            // Add the added item to the response
            $addedItem = [
                'id' => $item->id,
                'product_id' => $item->product_id,
                'title' => $product->name,
                'slug' => $product->slug,
                'price' => $price,
                'original_price' => $product->unit_price,
                'quantity' => $item->quantity,
                'total_price' => $price * $item->quantity,
                'image' => $product->thumbnail_img ? uploaded_asset($product->thumbnail_img) : null,
                'options' => $options,
                'variation_id' => null,
                'stock_quantity' => $availableStock,
                'is_in_stock' => $availableStock > 0,
                'seller_id' => $product->user_id,
                'seller' => $product->user_id ? [
                    'id' => $product->user_id,
                    'name' => $product->user ? $product->user->name : 'Unknown'
                ] : null,
                'added_at' => $item->created_at->format('Y-m-d H:i:s')
            ];
            
            $response = json_decode($cart->getContent(), true);
            $response['added_item'] = $addedItem;
            
            return $this->success($response['data'], 'Product added to cart successfully');
        } catch (\Exception $e) {
            Log::channel('api_cart')->error('Error adding to cart: ' . $e->getMessage());
            return $this->error('Error adding to cart', $e->getMessage(), null, 500);
        }
    }

    /**
     * Get product recommendations based on cart items
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getRecommendations(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'type' => 'nullable|string|in:related,cross_sell,up_sell',
                'limit' => 'nullable|integer|min:1|max:20'
            ]);

            if ($validator->fails()) {
                return $this->validation_error(
                    400,
                    'Invalid parameters',
                    $validator->errors()->messages(),
                    400
                );
            }
            
            $type = $request->type ?? 'related';
            $limit = $request->limit ?? 6;
            
            $user_id = auth()->check() ? auth()->id() : null;
            $temp_user_id = $request->header('X-Temp-User-Id');
            $cart_id = $request->header('X-Cart-Id');
            
            // Find cart items
            $cartItems = collect(); // Initialize as empty collection
            
            if ($cart_id) {
                $cartInfo = CartInfo::find($cart_id);
                if ($cartInfo) {
                    $cartItems = Cart::where('cart_info_id', $cartInfo->id)->get();
                }
            } elseif ($user_id) {
                $cartInfo = CartInfo::where('user_id', $user_id)->first();
                if ($cartInfo) {
                    $cartItems = Cart::where('cart_info_id', $cartInfo->id)->get();
                }
            } elseif ($temp_user_id) {
                $cartInfo = CartInfo::where('temp_user_id', $temp_user_id)->first();
                if ($cartInfo) {
                    $cartItems = Cart::where('cart_info_id', $cartInfo->id)->get();
                }
            }
            
            // Get product IDs from cart
            $productIds = $cartItems->pluck('product_id')->toArray();
            
            // Get recommendations based on type
            $recommendedProducts = collect(); // Initialize as empty collection
            
            if (!empty($productIds)) {
                switch ($type) {
                    case 'related':
                        // Get products in the same categories as cart items
                        $categories = DB::table('products')
                            ->whereIn('id', $productIds)
                            ->pluck('category_id')
                            ->unique()
                            ->filter()
                            ->toArray();
                            
                        $recommendedProducts = Product::whereIn('category_id', $categories)
                            ->where('published', 1)
                            ->whereNotIn('id', $productIds)
                            ->inRandomOrder()
                            ->limit($limit)
                            ->get();
                        break;
                        
                    case 'cross_sell':
                        // Get products that are frequently bought together
                        $recommendedProducts = Product::where('published', 1)
                            ->whereNotIn('id', $productIds)
                            ->where('featured', 1)
                            ->inRandomOrder()
                            ->limit($limit)
                            ->get();
                        break;
                        
                    case 'up_sell':
                        // Get higher priced alternatives
                        $avgPrice = Product::whereIn('id', $productIds)->avg('unit_price');
                        $recommendedProducts = Product::where('published', 1)
                            ->whereNotIn('id', $productIds)
                            ->where('unit_price', '>', $avgPrice)
                            ->orderBy('unit_price', 'asc')
                            ->limit($limit)
                            ->get();
                        break;
                }
            } else {
                // If cart is empty, get popular products
                $recommendedProducts = Product::where('published', 1)
                    ->orderBy('num_of_sale', 'desc')
                    ->limit($limit)
                    ->get();
            }
            
            // If we don't have enough recommendations, pad with popular products
            if (count($recommendedProducts) < $limit) {
                $remainingLimit = $limit - count($recommendedProducts);
                $existingIds = $recommendedProducts->pluck('id')->toArray();
                $allExistingIds = array_merge($productIds, $existingIds);
                
                $popularProducts = Product::where('published', 1)
                    ->whereNotIn('id', $allExistingIds)
                    ->orderBy('num_of_sale', 'desc')
                    ->limit($remainingLimit)
                    ->get();
                    
                $recommendedProducts = $recommendedProducts->concat($popularProducts);
            }
            
            // Format products for response
            $formattedProducts = new ProductsResource($recommendedProducts);
            
            return $this->success(
                $formattedProducts,
                'Recommendations retrieved successfully'
            );
        } catch (\Exception $e) {
            Log::channel('api_cart')->error('Error getting recommendations: ' . $e->getMessage());
            return $this->error('Error getting recommendations', $e->getMessage(), null, 500);
        }
    }

    /**
     * Check if all items in the cart are in stock before checkout
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function checkStock(Request $request): JsonResponse
    {
        try {
            $user_id = auth()->check() ? auth()->id() : null;
            $temp_user_id = $request->header('X-Temp-User-Id');
            $cart_id = $request->header('X-Cart-Id');
            
            // Find cart items
            $cartItems = collect(); // Initialize as empty collection
            
            if ($cart_id) {
                $cartInfo = CartInfo::find($cart_id);
                if ($cartInfo) {
                    $cartItems = Cart::where('cart_info_id', $cartInfo->id)->get();
                }
            } elseif ($user_id) {
                $cartInfo = CartInfo::where('user_id', $user_id)->first();
                if ($cartInfo) {
                    $cartItems = Cart::where('cart_info_id', $cartInfo->id)->get();
                }
            } elseif ($temp_user_id) {
                $cartInfo = CartInfo::where('temp_user_id', $temp_user_id)->first();
                if ($cartInfo) {
                    $cartItems = Cart::where('cart_info_id', $cartInfo->id)->get();
                }
            }
            
            if (empty($cartItems)) {
                return $this->success(
                    ['all_in_stock' => true, 'items' => []],
                    'Cart is empty'
                );
            }
            
            $stockStatuses = [];
            $allInStock = true;
            
            foreach ($cartItems as $item) {
                $product = Product::find($item->product_id);
                if (!$product) continue;
                
                $availableQuantity = $this->cartService->getProductStock($product, $item->variation);
                $isInStock = $availableQuantity >= $item->quantity;
                
                if (!$isInStock) {
                    $allInStock = false;
                }
                
                $stockStatuses[] = [
                    'cart_item_id' => $item->id,
                    'product_id' => $item->product_id,
                    'requested_quantity' => $item->quantity,
                    'available_quantity' => $availableQuantity,
                    'is_in_stock' => $isInStock
                ];
            }
            
            return $this->success(
                [
                    'all_in_stock' => $allInStock,
                    'items' => $stockStatuses
                ],
                'Stock check completed'
            );
        } catch (\Exception $e) {
            Log::channel('api_cart')->error('Error checking stock: ' . $e->getMessage());
            return $this->error('Error checking stock', $e->getMessage(), null, 500);
        }
    }

    public function apply_coupon_code_in_cart(Request $request): \Illuminate\Http\JsonResponse
    {
        $messages = array(
            'coupon_code.required' => translate('Please enter a Coupon code'),
            'coupon_code.exists' => translate('Invalid coupon code..'),
        );
        $validator = Validator::make($request->all(), [
            'coupon_code' => 'required|string|exists:coupons,code',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error(
                '400',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        $user_id = auth()->user()->id;
        $coupon_code = $request->coupon_code;
        try {
            $coupon = Coupon::where('code', $coupon_code)->first();
            $cart_items = Cart::where('user_id', $user_id)
                ->where('owner_id', $coupon->user_id)
                ->get();

            $coupon_discount = 0;
            if ($cart_items->isEmpty()) {
                return $this->error(
                    'Invalid Coupon',
                    'This coupon is not applicable to your cart products!',
                    null,
                    200
                );
            }
            $in_range = strtotime(date('d-m-Y')) >= $coupon->start_date && strtotime(date('d-m-Y')) <= $coupon->end_date;
            if (!$in_range) {
                return $this->error(
                    'Coupon Expired',
                    'Coupon expired!',
                    null,
                    200
                );
            }
            $is_used = CouponUsage::where('user_id', $user_id)
                ->where('coupon_id', $coupon->id)
                ->first() != null;

            if ($is_used) {
                return $this->error(
                    'Coupon Already Used',
                    'You already used this coupon!',
                    null,
                    200
                );
            }
            $coupon_details = json_decode($coupon->details);
            if ($coupon->type == 'cart_base') {
                $subtotal = $tax = $shipping = 0;
                foreach ($cart_items as $key => $cartItem) {
                    $subtotal += $cartItem->price * $cartItem->quantity;
                    $tax += $cartItem->tax;
                    $shipping += $cartItem->shipping_cost;
                }
                $sum = $subtotal + $tax + $shipping;
                if ($sum >= $coupon_details->min_buy) {
                    if ($coupon->discount_type == 'percent') {
                        $coupon_discount = ($sum * $coupon->discount) / 100;
                        if ($coupon_discount > $coupon_details->max_discount) {
                            $coupon_discount = $coupon_details->max_discount;
                        }
                    } elseif ($coupon->discount_type == 'amount') {
                        $coupon_discount = $coupon->discount;
                    }
                }
            } elseif ($coupon->type == 'product_base') {
                foreach ($cart_items as $key => $cartItem) {
                    foreach ($coupon_details as $key => $coupon_detail) {
                        if ($coupon_detail->product_id == $cartItem->product_id) {
                            if ($coupon->discount_type == 'percent') {
                                $coupon_discount += $cartItem->price * $coupon->discount / 100;
                            } elseif ($coupon->discount_type == 'amount') {
                                $coupon_discount += $coupon->discount;
                            }
                        }
                    }

                }
            }
            if ($coupon_discount > 0) {
                $coupon_usage = new CouponUsage();
                $coupon_usage->user_id = $user_id;
                $coupon_usage->coupon_id = $coupon->id;
                $coupon_usage->save();
                CartInfo::where('user_id', auth()->user()->id)
                    ->update([
                        'coupon_code' => $request->coupon_code,
                        'discount' => $coupon_discount
                    ]);
                Cart::where('user_id', auth()->user()->id)
                    ->where('owner_id', $coupon->user_id)
                    ->update([
                        'discount' => $coupon_discount / count($cart_items),
                        'coupon_code' => $request->coupon_code,
                        'coupon_applied' => 1
                    ]);

                $cartInfo = CartInfo::with('carts')
                    ->where('user_id', auth()->user()->id)
                    ->first();


                $shipping_address = null;
                if ($cartInfo->address_id != null || $cartInfo->address_id != '') {
                    $address = Address::where('id', $cartInfo->address_id)
                        ->where('user_id', auth()->user()->id)
                        ->first();
                    if ($address) {
                        $shipping_address = new ShippingAddressResource($address);
                    }
                }

                return $this->success(
                    new CartInfosResource($cartInfo),
                    'Coupon Applied Successfully',
                    200
                );
            } else {
                return $this->error(
                    'Invalid Coupon',
                    'This coupon is not applicable to your cart products!',
                    null,
                    200
                );
            }
        } catch (\Exception $e) {
            Log::channel('api_cart')->error('Apply Coupon Code : ApiCartController ' . print_r($e->getMessage(), true));
            return $this->error(
                'Failed to Apply Coupon Code',
                'Failed to Apply Coupon Code',
                $e->getMessage(),
                500
            );
        }
    }
    public function remove_coupon_code(Request $request)
    {
        $user_id = auth()->user()->id;
        $cartInfo = CartInfo::where('user_id', $user_id)->first();


        if ($cartInfo) {
            $couponInfo = Coupon::where('code', $cartInfo->coupon_code)->first();
            $cartInfo->update([
                'coupon_code' => null,
                'discount' => 0
            ]);
            Cart::where('user_id', $user_id)
                ->update([
                    'coupon_code' => null,
                    'coupon_applied' => 0,
                    'discount' => 0
                ]);
            $coupon_usage = CouponUsage::where('user_id', $user_id)
                ->where('coupon_id', $couponInfo->id)
                ->first();
            if ($coupon_usage) {
                $coupon_usage->delete();
            }
            return $this->success(
                null,
                'Coupon code removed successfully',
                200
            );
        } else {
            return $this->error(
                'Cart Not Found',
                'The requested cart could not be found',
                null,
                404
            );
        }
    }

    public function checkout_summary(Request $request){
        $user_id = auth()->user()->id;
        $cartInfo = CartInfo::with('carts')->where('user_id', $user_id)->first();
        if (!$cartInfo) {
            return $this->error(
                'Cart Not Found',
                'The requested cart could not be found',
                null,
                404
            );
        }
        $cart_items = Cart::where('user_id', $user_id)
            ->where('cart_info_id', $cartInfo->id)
            ->get();
        if ($cart_items->isEmpty()) {
            return $this->error(
                'Cart Empty',
                'The requested cart is empty',
                null,
                404
            );
        }
        return $this->success(
            new CheckoutSummaryResource($cartInfo),
            'Cart Summary',
            200
        );
    }

    /**
     * Get the current cart for the user or guest
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getCart(Request $request): JsonResponse
    {   
        return $this->success($request->all(), 'Cart retrieved successfully');
        try {
            $user_id = auth()->check() ? auth()->id() : null;
            $temp_user_id = $request->header('X-Temp-User-Id');
            $cart_id = $request->header('X-Cart-Id');
            
            // Get cart
            $cartInfo = null;
            
            // If cart ID provided, try to get that cart
            if ($cart_id) {
                $cartInfo = CartInfo::with('carts')->where('id', $cart_id)->first();
            }
            
            // If not found with cart ID, try user authentication
            if (!$cartInfo && $user_id) {
                $cartInfo = CartInfo::with('carts')->where('user_id', $user_id)->first();
            }
            
            // If still not found and we have a temp user ID, try that
            if (!$cartInfo && $temp_user_id) {
                $cartInfo = CartInfo::with('carts')->where('temp_user_id', $temp_user_id)->first();
            }
            
            // If still no cart found, create a new empty cart
            if (!$cartInfo) {
                $cartInfo = new CartInfo();
                $cartInfo->temp_user_id = $temp_user_id;
                $cartInfo->user_id = $user_id;
                $cartInfo->save();
            }
            
            // Format response
            $response = [
                'id' => $cartInfo->id,
                'cart_info_id' => $cartInfo->id,
                'user_id' => $cartInfo->user_id,
                'items' => [],
                'subtotal' => 0,
                'discount' => 0,
                'total' => 0,
                'coupon_code' => $cartInfo->coupon_code,
                'summary' => [
                    'itemCount' => 0,
                    'totalQuantity' => 0,
                    'subtotal' => 0,
                    'discount' => 0,
                    'tax' => 0,
                    'shipping' => 0,
                    'total' => 0,
                    'couponApplied' => !empty($cartInfo->coupon_code)
                ]
            ];
            
            // Add cart items if any
            if ($cartInfo->carts && $cartInfo->carts->count() > 0) {
                $items = [];
                $totalQuantity = 0;
                $subtotal = 0;
                
                foreach ($cartInfo->carts as $item) {
                    $product = Product::find($item->product_id);
                    if (!$product) continue;
                    
                    $price = $item->price;
                    $totalPrice = $price * $item->quantity;
                    $totalQuantity += $item->quantity;
                    $subtotal += $totalPrice;
                    
                    $productImage = $product->thumbnail_img ? asset($product->thumbnail_img) : null;
                    
                    $items[] = [
                        'id' => $item->id,
                        'product_id' => $item->product_id,
                        'title' => $product->name,
                        'slug' => $product->slug,
                        'price' => $price,
                        'original_price' => $product->unit_price,
                        'quantity' => $item->quantity,
                        'total_price' => $totalPrice,
                        'image' => $productImage,
                        'options' => $item->variation ? json_decode($item->variation, true) : null,
                        'variation_id' => null,
                        'stock_quantity' => $this->cartService->getProductStock($product, $item->variation),
                        'is_in_stock' => $this->cartService->getProductStock($product, $item->variation) > 0,
                        'seller_id' => $product->user_id,
                        'seller' => $product->user_id ? [
                            'id' => $product->user_id,
                            'name' => $product->user ? $product->user->name : 'Unknown'
                        ] : null,
                        'added_at' => $item->created_at->format('Y-m-d H:i:s')
                    ];
                }
                
                $response['items'] = $items;
                $response['subtotal'] = $subtotal;
                $response['summary']['itemCount'] = count($items);
                $response['summary']['totalQuantity'] = $totalQuantity;
                $response['summary']['subtotal'] = $subtotal;
                
                // Calculate discount if coupon is applied
                if ($cartInfo->coupon_code) {
                    $coupon = Coupon::where('code', $cartInfo->coupon_code)->first();
                    if ($coupon) {
                        $discount = $this->cartService->calculateCouponDiscount($coupon, $subtotal);
                        $response['discount'] = $discount;
                        $response['summary']['discount'] = $discount;
                    }
                }
                
                // Calculate shipping if a method is selected
                if ($cartInfo->shipping_method_id) {
                    $shippingCost = $this->cartService->calculateCartShippingCost($cartInfo->shipping_method_id, $items);
                    $response['summary']['shipping'] = $shippingCost;
                }
                
                // Calculate tax
                $tax = $this->cartService->calculateCartTax($items, $cartInfo->address_id);
                $response['summary']['tax'] = $tax;
                
                // Calculate total
                $total = $subtotal - $response['discount'] + $response['summary']['shipping'] + $tax;
                $response['total'] = $total;
                $response['summary']['total'] = $total;
            }
            
            return $this->success($response, 'Cart retrieved successfully');
        } catch (\Exception $e) {
            Log::channel('api_cart')->error('Error in getCart: ' . $e->getMessage());
            return $this->error('Error retrieving cart', $e->getMessage(), null, 500);
        }
    }

    /**
     * Update a cart item quantity
     *
     * @param Request $request
     * @param string $id
     * @return JsonResponse
     */
    public function updateCartItem(Request $request, $id): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'quantity' => 'required|integer|min:1',
            ]);

            if ($validator->fails()) {
                return $this->validation_error(
                    400,
                    'Please provide valid information',
                    $validator->errors()->messages(),
                    400
                );
            }

            $cart = Cart::find($id);
            if (!$cart) {
                return $this->error(
                    'Cart Item Not Found',
                    'The requested cart item could not be found',
                    null,
                    404
                );
            }

            // Check if user has access to this cart item
            $user_id = auth()->check() ? auth()->id() : null;
            $temp_user_id = $request->header('X-Temp-User-Id');
            
            if (($user_id && $cart->user_id != $user_id) && 
                ($temp_user_id && $cart->temp_user_id != $temp_user_id)) {
                return $this->error(
                    'Unauthorized',
                    'You do not have permission to update this cart item',
                    null,
                    403
                );
            }

            // Check product stock
            $product = Product::find($cart->product_id);
            if (!$product) {
                return $this->error(
                    'Product Not Found',
                    'The product associated with this cart item could not be found',
                    null,
                    404
                );
            }

            $available = $this->cartService->getProductStock($product, $cart->variation);
            if ($available < $request->quantity) {
                return $this->error(
                    'Insufficient Stock',
                    'The requested quantity exceeds available stock',
                    ['available_quantity' => $available],
                    400
                );
            }

            // Update cart item
            $cart->quantity = $request->quantity;
            $cart->save();

            // Update cart totals
            $cartInfo = CartInfo::find($cart->cart_info_id);
            if ($cartInfo) {
                // Update cart info calculations here if needed
            }

            // Return updated cart
            return $this->getCart($request);
        } catch (\Exception $e) {
            Log::channel('api_cart')->error('Error updating cart item: ' . $e->getMessage());
            return $this->error('Error updating cart item', $e->getMessage(), null, 500);
        }
    }

    /**
     * Remove a cart item
     *
     * @param Request $request
     * @param string $id
     * @return JsonResponse
     */
    public function removeCartItem(Request $request, $id): JsonResponse
    {
        try {
            $cart = Cart::find($id);
            if (!$cart) {
                return $this->error(
                    'Cart Item Not Found',
                    'The requested cart item could not be found',
                    null,
                    404
                );
            }

            // Check if user has access to this cart item
            $user_id = auth()->check() ? auth()->id() : null;
            $temp_user_id = $request->header('X-Temp-User-Id');
            
            if (($user_id && $cart->user_id != $user_id) && 
                ($temp_user_id && $cart->temp_user_id != $temp_user_id)) {
                return $this->error(
                    'Unauthorized',
                    'You do not have permission to remove this cart item',
                    null,
                    403
                );
            }

            // Delete the cart item
            $cart->delete();

            // Return updated cart
            return $this->getCart($request);
        } catch (\Exception $e) {
            Log::channel('api_cart')->error('Error removing cart item: ' . $e->getMessage());
            return $this->error('Error removing cart item', $e->getMessage(), null, 500);
        }
    }

    /**
     * Clear the entire cart
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function clearCart(Request $request): JsonResponse
    {
        try {
            $user_id = auth()->check() ? auth()->id() : null;
            $temp_user_id = $request->header('X-Temp-User-Id');
            $cart_id = $request->header('X-Cart-Id');
            
            // Find cart info
            $cartInfo = null;
            
            if ($cart_id) {
                $cartInfo = CartInfo::find($cart_id);
            } elseif ($user_id) {
                $cartInfo = CartInfo::where('user_id', $user_id)->first();
            } elseif ($temp_user_id) {
                $cartInfo = CartInfo::where('temp_user_id', $temp_user_id)->first();
            }
            
            if (!$cartInfo) {
                return $this->error(
                    'Cart Not Found',
                    'No cart found to clear',
                    null,
                    404
                );
            }
            
            // Delete all cart items
            Cart::where('cart_info_id', $cartInfo->id)->delete();
            
            // Reset cart info
            $cartInfo->coupon_code = null;
            $cartInfo->shipping_method_id = null;
            $cartInfo->save();
            
            // Return empty cart
            return $this->getCart($request);
        } catch (\Exception $e) {
            Log::channel('api_cart')->error('Error clearing cart: ' . $e->getMessage());
            return $this->error('Error clearing cart', $e->getMessage(), null, 500);
        }
    }

    /**
     * Sync cart from guest to authenticated user
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function syncCart(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'items' => 'required|array',
                'items.*.product_id' => 'required|integer|exists:products,id',
                'items.*.quantity' => 'required|integer|min:1',
                'items.*.options' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return $this->validation_error(
                    400,
                    'Please provide valid information',
                    $validator->errors()->messages(),
                    400
                );
            }

            $user_id = auth()->check() ? auth()->id() : null;
            $temp_user_id = $request->header('X-Temp-User-Id');
            $cart_id = $request->header('X-Cart-Id');
            
            if (!$user_id && !$temp_user_id && !$cart_id) {
                return $this->error(
                    'Cart Error',
                    'User identification required',
                    null,
                    400
                );
            }
            
            // Find or create cart
            $cartInfo = null;
            
            if ($cart_id) {
                $cartInfo = CartInfo::find($cart_id);
            } elseif ($user_id) {
                $cartInfo = CartInfo::where('user_id', $user_id)->first();
            } elseif ($temp_user_id) {
                $cartInfo = CartInfo::where('temp_user_id', $temp_user_id)->first();
            }
            
            if (!$cartInfo) {
                $cartInfo = new CartInfo();
                $cartInfo->user_id = $user_id;
                $cartInfo->temp_user_id = $temp_user_id;
                $cartInfo->save();
            }
            
            // Get existing cart items
            $existingItems = Cart::where('cart_info_id', $cartInfo->id)->get()->keyBy(function($item) {
                return $item->product_id . '-' . $item->variation;
            });
            
            // Track which items to keep
            $itemsToKeep = [];
            $syncReport = [
                'added' => [],
                'updated' => [],
                'removed' => []
            ];
            
            // Process items from request
            foreach ($request->items as $itemData) {
                $product = Product::find($itemData['product_id']);
                if (!$product) continue;
                
                $options = $itemData['options'] ?? [];
                $variation = !empty($options) ? json_encode($options) : '';
                $key = $itemData['product_id'] . '-' . $variation;
                
                // Check stock
                $availableStock = $this->cartService->getProductStock($product, $variation);
                $quantity = min($itemData['quantity'], $availableStock);
                
                if ($quantity <= 0) continue;
                
                $price = $this->cartService->calculateProductPrice($product, $variation);
                
                // If item exists, update it
                if (isset($existingItems[$key])) {
                    $existingItem = $existingItems[$key];
                    
                    if ($existingItem->quantity != $quantity || $existingItem->price != $price) {
                        $oldQuantity = $existingItem->quantity;
                        $oldPrice = $existingItem->price;
                        
                        $existingItem->quantity = $quantity;
                        $existingItem->price = $price;
                        $existingItem->save();
                        
                        $syncReport['updated'][] = [
                            'id' => $existingItem->id,
                            'product_id' => $product->id,
                            'product_name' => $product->name,
                            'old_quantity' => $oldQuantity,
                            'new_quantity' => $quantity,
                            'old_price' => $oldPrice,
                            'new_price' => $price
                        ];
                    }
                    
                    $itemsToKeep[] = $existingItem->id;
                } else {
                    // Create new item
                    $newItem = new Cart();
                    $newItem->cart_info_id = $cartInfo->id;
                    $newItem->user_id = $user_id;
                    $newItem->temp_user_id = $temp_user_id;
                    $newItem->product_id = $product->id;
                    $newItem->owner_id = $product->user_id;
                    $newItem->variation = $variation;
                    $newItem->price = $price;
                    $newItem->quantity = $quantity;
                    $newItem->save();
                    
                    $syncReport['added'][] = [
                        'id' => $newItem->id,
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'quantity' => $quantity,
                        'price' => $price
                    ];
                    
                    $itemsToKeep[] = $newItem->id;
                }
            }
            
            // Remove items not in the request
            foreach ($existingItems as $item) {
                if (!in_array($item->id, $itemsToKeep)) {
                    $product = Product::find($item->product_id);
                    $productName = $product ? $product->name : 'Unknown Product';
                    
                    $syncReport['removed'][] = [
                        'id' => $item->id,
                        'product_id' => $item->product_id,
                        'product_name' => $productName,
                        'quantity' => $item->quantity
                    ];
                    
                    $item->delete();
                }
            }
            
            // Get updated cart
            $cart = $this->getCart($request);
            $response = json_decode($cart->getContent(), true);
            $response['data']['sync_report'] = $syncReport;
            
            return $this->success($response['data'], 'Cart synchronized successfully');
        } catch (\Exception $e) {
            Log::channel('api_cart')->error('Error syncing cart: ' . $e->getMessage());
            return $this->error('Error syncing cart', $e->getMessage(), null, 500);
        }
    }

    /**
     * Apply a coupon to the cart
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function applyCoupon(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'code' => 'required|string',
            ]);

            if ($validator->fails()) {
                return $this->validation_error(
                    400,
                    'Please provide a valid coupon code',
                    $validator->errors()->messages(),
                    400
                );
            }

            $user_id = auth()->check() ? auth()->id() : null;
            $temp_user_id = $request->header('X-Temp-User-Id');
            $cart_id = $request->header('X-Cart-Id');
            
            // Find cart info
            $cartInfo = null;
            
            if ($cart_id) {
                $cartInfo = CartInfo::find($cart_id);
            } elseif ($user_id) {
                $cartInfo = CartInfo::where('user_id', $user_id)->first();
            } elseif ($temp_user_id) {
                $cartInfo = CartInfo::where('temp_user_id', $temp_user_id)->first();
            }
            
            if (!$cartInfo) {
                return $this->error(
                    'Cart Not Found',
                    'No cart found to apply coupon to',
                    null,
                    404
                );
            }
            
            // Check if cart has items
            $cartItems = Cart::where('cart_info_id', $cartInfo->id)->get();
            if ($cartItems->isEmpty()) {
                return $this->error(
                    'Empty Cart',
                    'Cannot apply coupon to an empty cart',
                    null,
                    400
                );
            }
            
            // Find coupon
            $coupon = Coupon::where('code', $request->code)->first();
            if (!$coupon) {
                return $this->error(
                    'Invalid Coupon',
                    'The coupon code is invalid',
                    null,
                    400
                );
            }
            
            // Check if coupon is active
            if (!$coupon->status) {
                return $this->error(
                    'Inactive Coupon',
                    'This coupon is currently inactive',
                    null,
                    400
                );
            }
            
            // Check expiry date
            if ($coupon->end_date && $coupon->end_date < now()) {
                return $this->error(
                    'Expired Coupon',
                    'This coupon has expired',
                    null,
                    400
                );
            }
            
            // Check usage limits
            if ($coupon->usage_limit && $coupon->usage_count >= $coupon->usage_limit) {
                return $this->error(
                    'Usage Limit Exceeded',
                    'This coupon has reached its usage limit',
                    null,
                    400
                );
            }
            
            // Check if user has already used this coupon (if authenticated)
            if ($user_id && $coupon->one_time_use) {
                $usage = CouponUsage::where('user_id', $user_id)
                    ->where('coupon_id', $coupon->id)
                    ->first();
                    
                if ($usage) {
                    return $this->error(
                        'Already Used',
                        'You have already used this coupon',
                        null,
                        400
                    );
                }
            }
            
            // Check minimum purchase amount
            $subtotal = 0;
            foreach ($cartItems as $item) {
                $subtotal += $item->price * $item->quantity;
            }
            
            if ($coupon->min_purchase && $subtotal < $coupon->min_purchase) {
                return $this->error(
                    'Minimum Purchase Required',
                    'This coupon requires a minimum purchase of ' . $coupon->min_purchase,
                    ['min_purchase' => $coupon->min_purchase],
                    400
                );
            }
            
            // Apply coupon to cart
            $cartInfo->coupon_code = $coupon->code;
            $cartInfo->save();
            
            // Calculate discount
            $discount = $this->cartService->calculateCouponDiscount($coupon, $subtotal);
            
            // Get updated cart
            $updatedCart = $this->getCart($request);
            
            // Prepare response
            $response = json_decode($updatedCart->getContent(), true);
            $response['data']['coupon'] = [
                'code' => $coupon->code,
                'discount_amount' => $discount,
                'discount_type' => $coupon->discount_type,
                'min_purchase' => $coupon->min_purchase
            ];
            
            return response()->json($response);
        } catch (\Exception $e) {
            Log::channel('api_cart')->error('Error applying coupon: ' . $e->getMessage());
            return $this->error('Error applying coupon', $e->getMessage(), null, 500);
        }
    }

    /**
     * Remove a coupon from the cart
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function removeCoupon(Request $request): JsonResponse
    {
        try {
            $user_id = auth()->check() ? auth()->id() : null;
            $temp_user_id = $request->header('X-Temp-User-Id');
            $cart_id = $request->header('X-Cart-Id');
            
            // Find cart info
            $cartInfo = null;
            
            if ($cart_id) {
                $cartInfo = CartInfo::find($cart_id);
            } elseif ($user_id) {
                $cartInfo = CartInfo::where('user_id', $user_id)->first();
            } elseif ($temp_user_id) {
                $cartInfo = CartInfo::where('temp_user_id', $temp_user_id)->first();
            }
            
            if (!$cartInfo) {
                return $this->error(
                    'Cart Not Found',
                    'No cart found to remove coupon from',
                    null,
                    404
                );
            }
            
            // Remove coupon
            $hadCoupon = !empty($cartInfo->coupon_code);
            $cartInfo->coupon_code = null;
            $cartInfo->save();
            
            if (!$hadCoupon) {
                return $this->error(
                    'No Coupon',
                    'No coupon was applied to this cart',
                    null,
                    400
                );
            }
            
            // Get updated cart
            return $this->getCart($request);
        } catch (\Exception $e) {
            Log::channel('api_cart')->error('Error removing coupon: ' . $e->getMessage());
            return $this->error('Error removing coupon', $e->getMessage(), null, 500);
        }
    }

    /**
     * Merge a guest cart with a user cart after login
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function mergeCarts(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'temp_user_id' => 'required|string',
            ]);

            if ($validator->fails()) {
                return $this->validation_error(
                    400,
                    'Please provide valid information',
                    $validator->errors()->messages(),
                    400
                );
            }

            if (!auth()->check()) {
                return $this->error(
                    'Unauthorized',
                    'You must be logged in to merge carts',
                    null,
                    401
                );
            }

            $user_id = auth()->id();
            $temp_user_id = $request->temp_user_id;

            // Find guest cart
            $guestCart = CartInfo::where('temp_user_id', $temp_user_id)->first();
            if (!$guestCart) {
                return $this->error(
                    'Cart Not Found',
                    'No guest cart found to merge',
                    null,
                    404
                );
            }

            // Find or create user cart
            $userCart = CartInfo::where('user_id', $user_id)->first();
            if (!$userCart) {
                // If no user cart exists, simply update the guest cart with user ID
                $guestCart->user_id = $user_id;
                $guestCart->temp_user_id = null;
                $guestCart->save();

                // Update all cart items
                Cart::where('cart_info_id', $guestCart->id)
                    ->update(['user_id' => $user_id, 'temp_user_id' => null]);

                return $this->getCart($request);
            }

            // Merge items from guest cart to user cart
            $guestItems = Cart::where('cart_info_id', $guestCart->id)->get();
            
            foreach ($guestItems as $guestItem) {
                // Check if same item exists in user cart
                $existingItem = Cart::where('cart_info_id', $userCart->id)
                    ->where('product_id', $guestItem->product_id)
                    ->where('variation', $guestItem->variation)
                    ->first();
                
                if ($existingItem) {
                    // Update quantity
                    $product = Product::find($guestItem->product_id);
                    if (!$product) continue;
                    
                    $availableStock = $this->cartService->getProductStock($product, $guestItem->variation);
                    $newQuantity = $existingItem->quantity + $guestItem->quantity;
                    
                    // Ensure we don't exceed stock
                    $existingItem->quantity = min($newQuantity, $availableStock);
                    $existingItem->save();
                    
                    // Delete guest item
                    $guestItem->delete();
                } else {
                    // Move item to user cart
                    $guestItem->cart_info_id = $userCart->id;
                    $guestItem->user_id = $user_id;
                    $guestItem->temp_user_id = null;
                    $guestItem->save();
                }
            }
            
            // Delete guest cart
            $guestCart->delete();
            
            return $this->getCart($request);
        } catch (\Exception $e) {
            Log::channel('api_cart')->error('Error merging carts: ' . $e->getMessage());
            return $this->error('Error merging carts', $e->getMessage(), null, 500);
        }
    }

    /**
     * Repair a cart by checking stock availability and removing invalid items
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function repairCart(Request $request): JsonResponse
    {
        try {
            $user_id = auth()->check() ? auth()->id() : null;
            $temp_user_id = $request->header('X-Temp-User-Id');
            $cart_id = $request->header('X-Cart-Id');
            
            if (!$user_id && !$temp_user_id && !$cart_id) {
                return $this->error(
                    'Cart Error',
                    'User identification required',
                    null,
                    400
                );
            }
            
            // Find cart
            $cartInfo = null;
            
            if ($cart_id) {
                $cartInfo = CartInfo::find($cart_id);
            } elseif ($user_id) {
                $cartInfo = CartInfo::where('user_id', $user_id)->first();
            } elseif ($temp_user_id) {
                $cartInfo = CartInfo::where('temp_user_id', $temp_user_id)->first();
            }
            
            if (!$cartInfo) {
                return $this->error(
                    'Cart Not Found',
                    'No cart found to repair',
                    null,
                    404
                );
            }
            
            // Get all cart items
            $cartItems = Cart::where('cart_info_id', $cartInfo->id)->get();
            $repairReport = [
                'removed_items' => [],
                'updated_items' => [],
            ];
            
            foreach ($cartItems as $item) {
                $product = Product::find($item->product_id);
                
                // Remove item if product doesn't exist
                if (!$product) {
                    $repairReport['removed_items'][] = [
                        'id' => $item->id,
                        'reason' => 'Product no longer exists'
                    ];
                    $item->delete();
                    continue;
                }
                
                // Check stock
                $availableStock = $this->cartService->getProductStock($product, $item->variation);
                
                // Remove item if out of stock
                if ($availableStock <= 0) {
                    $repairReport['removed_items'][] = [
                        'id' => $item->id,
                        'product_name' => $product->name,
                        'reason' => 'Product out of stock'
                    ];
                    $item->delete();
                    continue;
                }
                
                // Update quantity if it exceeds available stock
                if ($item->quantity > $availableStock) {
                    $repairReport['updated_items'][] = [
                        'id' => $item->id,
                        'product_name' => $product->name,
                        'old_quantity' => $item->quantity,
                        'new_quantity' => $availableStock,
                        'reason' => 'Adjusted to max available stock'
                    ];
                    $item->quantity = $availableStock;
                    $item->save();
                }
                
                // Update price in case it changed
                $currentPrice = $this->cartService->calculateProductPrice($product, $item->variation);
                if ($item->price != $currentPrice) {
                    $repairReport['updated_items'][] = [
                        'id' => $item->id,
                        'product_name' => $product->name,
                        'old_price' => $item->price,
                        'new_price' => $currentPrice,
                        'reason' => 'Price updated'
                    ];
                    $item->price = $currentPrice;
                    $item->save();
                }
            }
            
            // Get updated cart
            $cart = $this->getCart($request);
            $response = json_decode($cart->getContent(), true);
            $response['data']['repair_report'] = $repairReport;
            
            return $this->success($response['data'], 'Cart repaired successfully');
        } catch (\Exception $e) {
            Log::channel('api_cart')->error('Error repairing cart: ' . $e->getMessage());
            return $this->error('Error repairing cart', $e->getMessage(), null, 500);
        }
    }

    /**
     * Generate a temporary user ID for guest cart operations
     *
     * @return JsonResponse
     */
    public function generateTempUserId(): JsonResponse
    {
        try {
            $tempUserId = $this->cartService->generateTempUserId();
            
            return $this->success([
                'temp_user_id' => $tempUserId
            ], 'Temporary user ID generated successfully');
        } catch (\Exception $e) {
            Log::channel('api_cart')->error('Error generating temp user ID: ' . $e->getMessage());
            return $this->error('Error generating temporary user ID', $e->getMessage(), null, 500);
        }
    }

    /**
     * Get bulk order cart
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getBulkOrderCart(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();
            
            // Check if migration is requested
            $migrate = $request->get('migrate', false);
            
            // Get bulk order cart items using the existing cart table with 'bulk' type
            $cartItems = Cart::where('user_id', $user->id)
                ->where('added_by', 'bulk')
                ->with(['product', 'product.category', 'product.brand', 'product.taxes'])
                ->get();
            
            // If bulk cart is empty and migration is requested, check for existing cart items
            if ($cartItems->isEmpty() && $migrate) {
                // Get existing cart items that might be intended for bulk orders
                $existingCartItems = Cart::where('user_id', $user->id)
                    ->where('added_by', 'manual')
                    ->with(['product', 'product.category', 'product.brand', 'product.taxes'])
                    ->get();
                
                if ($existingCartItems->isNotEmpty()) {
                    // Convert existing items to bulk order items
                    foreach ($existingCartItems as $item) {
                        $item->added_by = 'bulk';
                        $item->save();
                    }
                    
                    // Refetch the cart items
                    $cartItems = Cart::where('user_id', $user->id)
                        ->where('added_by', 'bulk')
                        ->with(['product', 'product.category', 'product.brand', 'product.taxes'])
                        ->get();
                        
                    Log::info("Migrated {$existingCartItems->count()} cart items to bulk order cart for user {$user->id}");
                }
            }
                
            if ($cartItems->isEmpty()) {
                return $this->success(
                    [
                        'items' => [],
                        'summary' => [
                            'subtotal' => 0.0,
                            'tax' => 0.0,
                            'shipping' => 0.0,
                            'grand_total' => 0.0,
                            'item_count' => 0
                        ]
                    ],
                    'Bulk order cart is empty',
                    200
                );
            }
            
            // Calculate totals using existing helper functions
            $subtotal = 0;
            $tax = 0;
            $shipping = 0;
            $items = [];
            
            foreach ($cartItems as $cartItem) {
                $product = $cartItem->product;
                
                if (!$product) {
                    continue;
                }
                
                $itemPrice = $cartItem->price;
                $itemQuantity = $cartItem->quantity;
                $itemTotal = $itemPrice * $itemQuantity;
                
                // Calculate tax using existing helper function
                $itemTax = product_tax($product, $itemPrice);
                
                // Calculate shipping cost using existing helper function
                $itemShipping = product_shipping_cost($product, $itemPrice);
                
                $subtotal += $itemTotal;
                $tax += $itemTax * $itemQuantity;
                $shipping += $itemShipping;
                
                // Get dropshipper price and original price for profit calculation
                $originalPrice = $product->unit_price;
                $b2bPrice = $product->dropshipper_price ?? $itemPrice;
                
                $items[] = [
                    'id' => $cartItem->id,
                    'productId' => $product->id,
                    'name' => $product->name,
                    'slug' => $product->slug,
                    'price' => (float) $itemPrice,
                    'originalPrice' => (float) $originalPrice,
                    'b2bPrice' => (float) $b2bPrice,
                    'quantity' => (int) $itemQuantity,
                    'variation' => $cartItem->variation,
                    'imageUrl' => uploaded_asset($product->thumbnail_img),
                    'sku' => $product->sku,
                    'stock' => (int) $product->current_stock,
                    'category' => $product->category ? $product->category->name : '',
                    'brand' => $product->brand ? $product->brand->name : '',
                    'notes' => $cartItem->notes,
                    'total' => $itemTotal,
                    'tax' => $itemTax,
                    'shipping_cost' => $itemShipping
                ];
            }
            
            $grandTotal = $subtotal + $tax + $shipping;
            
            return $this->success([
                'items' => $items,
                'summary' => [
                    'subtotal' => (float) $subtotal,
                    'tax' => (float) $tax,
                    'shipping' => (float) $shipping,
                    'grand_total' => (float) $grandTotal,
                    'item_count' => count($items)
                ]
            ], 'Bulk order cart retrieved successfully', 200);
            
        } catch (\Exception $e) {
            Log::error('Error fetching bulk order cart: ' . $e->getMessage());
            return $this->error(
                'Server Error',
                'Failed to fetch bulk order cart: ' . $e->getMessage(),
                null,
                500
            );
        }
    }

    /**
     * Add item to bulk order cart
     *
     * @param Request $request
     * @param string $productId
     * @return JsonResponse
     */
    public function addToBulkOrderCart(Request $request, $productId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string|max:500',
            'variation' => 'nullable|string',
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        
        try {
            $user = auth()->user();
            $product = Product::with('taxes')->find($productId);
            
            if (!$product) {
                return $this->error(
                    'Product Not Found',
                    'The requested product was not found',
                    null,
                    404
                );
            }
            
            if ($product->published != 1 || $product->approved != 1) {
                return $this->error(
                    'Product Unavailable',
                    'This product is not available for purchase',
                    null,
                    400
                );
            }
            
            $quantity = $request->input('quantity');
            $variation = $request->input('variation');
            $notes = $request->input('notes');
            
            // Check stock availability
            $availableStock = product_stock_quantity($product, $variation);
            if ($quantity > $availableStock) {
                return $this->error(
                    'Insufficient Stock',
                    "Only {$availableStock} items available in stock",
                    null,
                    400
                );
            }
            
            // Calculate price using CartService method for consistency
            $price = $this->cartService->calculateProductPrice($product, $variation);
            
            // Check if item already exists in bulk cart
            $existingCartItem = Cart::where('user_id', $user->id)
                ->where('product_id', $productId)
                ->where('added_by', 'bulk')
                ->where('variation', $variation)
                ->first();
                
            if ($existingCartItem) {
                // Update existing item
                $newQuantity = $existingCartItem->quantity + $quantity;
                
                if ($newQuantity > $availableStock) {
                    return $this->error(
                        'Insufficient Stock',
                        "Cannot add {$quantity} more items. Only {$availableStock} available, and you already have {$existingCartItem->quantity} in cart",
                        null,
                        400
                    );
                }
                
                $existingCartItem->quantity = $newQuantity;
                if ($notes) {
                    $existingCartItem->notes = $notes;
                }
                $existingCartItem->save();
                
                return $this->success([
                    'cart_item_id' => $existingCartItem->id,
                    'quantity' => $newQuantity,
                    'action' => 'updated'
                ], 'Item quantity updated in bulk order cart', 200);
            }
            
            // Get or create cart info
            $cartInfo = CartInfo::where('user_id', $user->id)->first();
            if (!$cartInfo) {
                $cartInfo = new CartInfo();
                $cartInfo->user_id = $user->id;
                $cartInfo->save();
            }
            
            // Calculate tax and shipping using existing helper functions
            $itemTax = product_tax($product, $price);
            $itemShipping = product_shipping_cost($product, $price);
            
            // Create new cart item
            $cartItem = new Cart();
            $cartItem->user_id = $user->id;
            $cartItem->cart_info_id = $cartInfo->id;
            $cartItem->product_id = $productId;
            $cartItem->owner_id = $product->user_id;
            $cartItem->variation = $variation;
            $cartItem->quantity = $quantity;
            $cartItem->price = $price;
            $cartItem->tax = $itemTax;
            $cartItem->shipping_cost = $itemShipping;
            $cartItem->added_by = 'bulk';
            $cartItem->notes = $notes;
            $cartItem->save();
            
            return $this->success([
                'cart_item_id' => $cartItem->id,
                'quantity' => $quantity,
                'action' => 'added'
            ], 'Item added to bulk order cart successfully', 201);
            
        } catch (\Exception $e) {
            Log::error('Error adding item to bulk order cart: ' . $e->getMessage());
            return $this->error(
                'Server Error',
                'Failed to add item to bulk order cart: ' . $e->getMessage(),
                null,
                500
            );
        }
    }

    /**
     * Update bulk order cart item
     *
     * @param Request $request
     * @param string $productId
     * @return JsonResponse
     */
    public function updateBulkOrderCartItem(Request $request, $productId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'quantity' => 'nullable|integer|min:1',
            'notes' => 'nullable|string|max:500',
            'variation' => 'nullable|string',
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        
        try {
            $user = auth()->user();
            $variation = $request->input('variation');
            
            // Find the cart item
            $cartItem = Cart::where('user_id', $user->id)
                ->where('product_id', $productId)
                ->where('added_by', 'bulk')
                ->where('variation', $variation)
                ->first();
                
            if (!$cartItem) {
                return $this->error(
                    'Item Not Found',
                    'The requested item is not in your bulk order cart',
                    null,
                    404
                );
            }
            
            $product = $cartItem->product;
            if (!$product) {
                return $this->error(
                    'Product Not Found',
                    'The product for this cart item no longer exists',
                    null,
                    404
                );
            }
            
            $updates = [];
            
            // Update quantity if provided
            if ($request->has('quantity')) {
                $newQuantity = $request->input('quantity');
                $availableStock = product_stock_quantity($product, $variation);
                
                if ($newQuantity > $availableStock) {
                    return $this->error(
                        'Insufficient Stock',
                        "Only {$availableStock} items available in stock",
                        null,
                        400
                    );
                }
                
                $cartItem->quantity = $newQuantity;
                $updates['quantity'] = $newQuantity;
            }
            
            // Update notes if provided
            if ($request->has('notes')) {
                $cartItem->notes = $request->input('notes');
                $updates['notes'] = $request->input('notes');
            }
            
            $cartItem->save();
            
            return $this->success([
                'cart_item_id' => $cartItem->id,
                'updates' => $updates
            ], 'Bulk order cart item updated successfully', 200);
            
        } catch (\Exception $e) {
            Log::error('Error updating bulk order cart item: ' . $e->getMessage());
            return $this->error(
                'Server Error',
                'Failed to update bulk order cart item: ' . $e->getMessage(),
                null,
                500
            );
        }
    }

    /**
     * Remove item from bulk order cart
     *
     * @param Request $request
     * @param string $productId
     * @return JsonResponse
     */
    public function removeBulkOrderCartItem(Request $request, $productId): JsonResponse
    {
        try {
            $user = auth()->user();
            $variation = $request->input('variation');
            
            // Find and delete the cart item
            $cartItem = Cart::where('user_id', $user->id)
                ->where('product_id', $productId)
                ->where('added_by', 'bulk')
                ->where('variation', $variation)
                ->first();
                
            if (!$cartItem) {
                return $this->error(
                    'Item Not Found',
                    'The requested item is not in your bulk order cart',
                    null,
                    404
                );
            }
            
            $cartItem->delete();
            
            return $this->success(
                ['cart_item_id' => $cartItem->id],
                'Item removed from bulk order cart successfully',
                200
            );
            
        } catch (\Exception $e) {
            Log::error('Error removing bulk order cart item: ' . $e->getMessage());
            return $this->error(
                'Server Error',
                'Failed to remove item from bulk order cart: ' . $e->getMessage(),
                null,
                500
            );
        }
    }

    /**
     * Checkout bulk order
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function checkoutBulkOrder(Request $request): JsonResponse
    {
        try {
            return $request->all();
            $validator = Validator::make($request->all(), [
                'address_id' => 'required|string',
                'payment_method_id' => 'required|string',
                'additional_info' => 'nullable|string',
                'is_gift' => 'nullable|boolean',
                'order_from' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated'
                ], 401);
            }

            $additionalInfo = [
                'additional_info' => $request->additional_info,
                'is_gift' => $request->is_gift ?? false,
                'order_from' => $request->order_from ?? 'api'
            ];

            $result = $this->orderProcessingService->processOrderFromCart(
                $user,
                $request->address_id,
                $request->payment_method_id,
                $additionalInfo
            );

            if (!$result['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                    'error_code' => $result['error_code']
                ], 400);
            }

            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'data' => $result['data']
            ], 200);

        } catch (\Exception $e) {
            Log::channel('api_order')->error('Error processing order from cart: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Something went wrong while processing your order'
            ], 500);
        }
    }

    /**
     * Clear bulk order cart
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function clearBulkOrderCart(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();
            
            // Delete all bulk order cart items for the user
            $deletedCount = Cart::where('user_id', $user->id)
                ->where('added_by', 'bulk')
                ->delete();
            
            return $this->success(
                ['deleted_items' => $deletedCount],
                'Bulk order cart cleared successfully',
                200
            );
            
        } catch (\Exception $e) {
            Log::error('Error clearing bulk order cart: ' . $e->getMessage());
            return $this->error(
                'Server Error',
                'Failed to clear bulk order cart: ' . $e->getMessage(),
                null,
                500
            );
        }
    }
}
