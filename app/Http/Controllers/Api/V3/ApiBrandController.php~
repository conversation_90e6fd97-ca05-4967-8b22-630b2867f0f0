<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Http\Resources\V3\BrandResource;
use App\Http\Resources\V3\BrandsResource;
use App\Http\Resources\V3\Categories\CategoriesResource;
use App\Models\Brand;
use App\Models\Category;
use App\Models\Product;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;

class ApiBrandController extends ApiResponse
{
    public function __construct()
    {
        parent::__construct();
    }

    public function index(Request $request)
    {
        $per_page = min((int)$request->input('per_page', 8), 10);
        $page = max((int)$request->input('page', 1), 1);
        $sort = in_array($request->input('sort'), [
            'name_asc',
            'name_desc',
            /*'popular',*/
            'product_count',
        ]
        ) ? $request->input('sort') : 'name_asc';
        $category=$request->input('category', null);
        $featured=$request->input('featured', false);
        $search=$request->input('search', null);
        $brands = Brand::query();

        if ($category != null) {
            $brand_ids = Product::where('category_id', $category)->whereNotNull('brand_id')->pluck('brand_id')->unique()->toArray();
            $brands = $brands->whereIn('id', $brand_ids);
        }
       if($featured){
            $brands=$brands->where('top', 1);
        }
        if($search){
            $brands=$brands->where('name', 'like', "%$search%");
        }
        switch ($sort) {
            case 'name_asc':
                $brands = $brands->orderBy('name', 'asc');
                break;
            case 'name_desc':
                $brands = $brands->orderBy('name', 'desc');
                break;
            case 'product_count':
                $brands = $brands->orderBy('products_count', 'desc');
                break;
        }
        $paginatedBrands = $brands->paginate($per_page);
        $total_items = $paginatedBrands->total();
        $total_pages = $paginatedBrands->lastPage();

        $data = [
            'brands' => new BrandsResource($paginatedBrands),
            'pagination' => [
                'currentPage' => (int) $page,
                'totalPages' => $total_pages,
                'totalItems' => $total_items,
                'itemsPerPage' => (int) $per_page,
            ]
        ];
        return $this->success($data);
    }
    public function details(Request $request)
    {
        $messages = array(
            'slug.required' => translate('Please enter a valid Brand slug'),
            'slug.exists' => translate('Invalid Brand slug.Brand not found'),
        );
        $validator = Validator::make($request->all(), [
            'slug' => 'required|exists:brands,slug',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error('BRAND_NOT_FOUND', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }
        $brand = Brand::where('slug', $request->input('slug'))->first();
        return $this->success(
            New BrandResource($brand),
            translate('Brand details fetched successfully')
        );
    }
    public function get_brand_categories(Request $request,$slug){
        dd($request->all());
        $brand_info = Brand::where('slug', $slug)->first();
        if (!$brand_info) {
            return $this->validation_error(
                'BRAND_NOT_FOUND',
                'Please provide valid credentials',
                ['slug' => ['Brand not found']], 400
            );
        }

        $categories_id = Product::where('brand_id', $brand_info)->whereNotNull('category_id')->pluck('category_id')->unique()->toArray();
        $categories = Category::whereIn('id', $categories_id)->get();
       return $this->success(
           New CategoriesResource($categories),
           translate('Brand categories fetched successfully')
       );
    }
}
