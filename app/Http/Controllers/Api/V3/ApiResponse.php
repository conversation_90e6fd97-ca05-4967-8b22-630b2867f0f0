<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Http\Resources\V3\User\UserResource;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Carbon;

class ApiResponse extends Controller {
    protected int $statusCode = 200;
    protected array $headers = [];
    protected array $meta = [];

    public function __construct() {
        // Set default cache control headers
        $this->headers['Cache-Control'] = 'private, max-age=3600';
    }

    /**
     * Set custom headers for the response
     *
     * @param array $headers
     * @return self
     */
    public function withHeaders(array $headers): self {
        $this->headers = array_merge($this->headers, $headers);
        return $this;
    }

    /**
     * Set cache control headers
     *
     * @param int $maxAge Maximum age in seconds
     * @param bool $private Whether the response is private
     * @return self
     */
    public function withCacheControl(int $maxAge, bool $private = true): self {
        $this->headers['Cache-Control'] = ($private ? 'private' : 'public') . ', max-age=' . $maxAge;
        return $this;
    }

    /**
     * Set metadata for the response
     *
     * @param array $meta
     * @return self
     */
    public function withMeta(array $meta): self {
        $this->meta = array_merge($this->meta, $meta);
        return $this;
    }

    /**
     * Format success response
     *
     * @param mixed $data
     * @param string|null $message
     * @return JsonResponse
     */
    public function success($data = null, ?string $message = null,int $statusCode = 200): JsonResponse {
        $this->statusCode= $statusCode;
        $response = [
            'status' => 'success',
            'data' => $this->formatData($data)
        ];

        if ($message) {
            $response['message'] = $message;
        }

        if (!empty($this->meta)) {
            $response['meta'] = $this->meta;
        }

        return new JsonResponse($response, $this->statusCode, $this->headers);
    }

    /**
     * Format error response
     *
     * @param string $code Error code
     * @param string $message User-friendly error message
     * @param string|null $details Additional error details
     * @param int $statusCode HTTP status code
     * @return JsonResponse
     */
    public function validation_error(string $code, string $message, ?array $details = null, int $statusCode = 400): JsonResponse {
        $this->statusCode = $statusCode;

        $response = [
            'success' => false,
            'error' => [
                'code' => 'VALIDATION_ERROR',
                'message' => $message
            ]
        ];

        if ($details) {
            $response['error']['details'] = $details;
        }

        if (!empty($this->meta)) {
            $response['meta'] = $this->meta;
        }

        return new JsonResponse($response, $this->statusCode, $this->headers);
    }
    public function error(string $code, string $message, ?string $details = null, int $statusCode = 400): JsonResponse {
        $this->statusCode = $statusCode;

        $response = [
            'status' => 'error',
            'error' => [
                'code' => $code,
                'message' => $message
            ]
        ];

        if ($details) {
            $response['error']['details'] = $details;
        }

        if (!empty($this->meta)) {
            $response['meta'] = $this->meta;
        }

        return new JsonResponse($response, $this->statusCode, $this->headers);
    }

    /**
     * Format data according to API standards
     *
     * @param mixed $data
     * @return mixed
     */
    protected function formatData($data) {
        if (is_array($data)) {
            return array_map([$this, 'formatValue'], $data);
        }

        return $this->formatValue($data);
    }

    /**
     * Format individual values according to API standards
     *
     * @param mixed $value
     * @return mixed
     */
    protected function formatValue($value) {

        if (is_array($value)) {
            return array_map([$this, 'formatValue'], $value);
        }

        // Format dates to ISO 8601, except for start_date and end_date
        if ($value instanceof Carbon && !in_array(array_key_last(request()->segments()), ['start_date', 'end_date'])) {
            return $value->toIso8601String();
        }

        // Format image URLs to absolute paths
        if (is_string($value) && $this->isImagePath($value)) {
            return $this->getAbsoluteUrl($value);
        }

        return $value;
    }

    /**
     * Check if a string is an image path
     *
     * @param string $path
     * @return bool
     */
    protected function isImagePath(string $path): bool {
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        return in_array($extension, $imageExtensions);
    }

    /**
     * Convert relative path to absolute URL
     *
     * @param string $path
     * @return string
     */
    protected function getAbsoluteUrl(string $path): string {
        if (filter_var($path, FILTER_VALIDATE_URL)) {
            return $path;
        }

        return url($path);
    }

    /**
     * Create a paginated response
     *
     * @param mixed $data
     * @param int $total
     * @param int $perPage
     * @param int $currentPage
     * @return JsonResponse
     */
    public function paginate($data, int $total, int $perPage, int $currentPage): JsonResponse {
        return $this->success($data)->withMeta([
            'pagination' => [
                'total' => $total,
                'per_page' => $perPage,
                'current_page' => $currentPage,
                'total_pages' => ceil($total / $perPage)
            ]
        ]);
    }
    protected function generateAuthData(User $user)
    {
        $token = $user->createToken('API Token')->plainTextToken;
        
        return [
            'user' => new UserResource($user),
            'token' => [
                'token_type' => 'Bearer',
                'access_token' => $token,
                'refresh_token' => $token,
                'expires_in' => config('sanctum.expiration', 60) * 60,
                'message' => 'Authentication successful'
            ]
        ];
    }

    /**
     * Generate authentication response with cookie
     * 
     * @param User $user
     * @return \Illuminate\Http\JsonResponse
     */
    protected function generateAuthResponse(User $user)
    {
        $authData = $this->generateAuthData($user);
        
        // Create secure httpOnly cookie for JWT
        $cookie = cookie(
            'jwt', 
            $authData['token']['access_token'], 
            config('sanctum.expiration', 60) * 60, // Convert to minutes 
            '/', // Path
            config('session.domain'), // Domain
            config('session.secure'), // Secure (HTTPS only)
            true, // HttpOnly (prevent XSS)
            false, // Raw
            config('session.same_site') // SameSite
        );

        return $this->success($authData, 'Authentication successful')->withCookie($cookie);
    }

    /**
     * Return a success response
     * 
     * @param mixed $data
     * @param string $message
     * @param int $status
     * @return \Illuminate\Http\JsonResponse
     */
    public function successResponse($data = null, $message = 'Success', $status = 200)
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data
        ], $status);
    }
    
    /**
     * Return an error response
     * 
     * @param string $message
     * @param int $status
     * @param mixed $errors
     * @return \Illuminate\Http\JsonResponse
     */
    public function errorResponse($message = 'Error', $status = 500, $errors = null)
    {
        $response = [
            'success' => false,
            'message' => $message
        ];
        
        if ($errors) {
            $response['errors'] = $errors;
        }
        
        return response()->json($response, $status);
    }
    
    /**
     * Return a validation error response
     * 
     * @param string $message
     * @param mixed $errors
     * @return \Illuminate\Http\JsonResponse
     */
    public function validationErrorResponse($message = 'Validation failed', $errors = null)
    {
        return $this->errorResponse($message, 422, $errors);
    }
    
    /**
     * Return a not found response
     * 
     * @param string $message
     * @return \Illuminate\Http\JsonResponse
     */
    public function notFoundResponse($message = 'Resource not found')
    {
        return $this->errorResponse($message, 404);
    }
    
    /**
     * Return an unauthorized response
     * 
     * @param string $message
     * @return \Illuminate\Http\JsonResponse
     */
    public function notAuthorizedResponse($message = 'You are not authorized to access this resource')
    {
        return $this->errorResponse($message, 403);
    }
    
    /**
     * Return an unauthenticated response
     * 
     * @param string $message
     * @return \Illuminate\Http\JsonResponse
     */
    public function unauthenticatedResponse($message = 'You need to be logged in to access this resource')
    {
        return $this->errorResponse($message, 401);
    }
}
