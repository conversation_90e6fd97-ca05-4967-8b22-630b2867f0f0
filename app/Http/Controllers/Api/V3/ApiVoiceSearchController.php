<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\Search;
use App\Models\VoiceSearch;
use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ApiVoiceSearchController extends ApiResponse
{
    // Patterns for intent recognition
    protected $intentPatterns = [
        'price_query' => [
            'en' => ['how much', 'price of', 'cost of', 'what is the price', 'how much is', 'what does', 'cost'],
            'bn' => ['দাম কত', 'মূল্য কত', 'কত টাকা', 'দাম জানতে চাই', 'মূল্য কি', 'কত দাম']
        ],
        'category_query' => [
            'en' => ['show me', 'find', 'search for', 'look for', 'category', 'in the category of', 'products in'],
            'bn' => ['দেখাও', 'খুঁজে দাও', 'খোঁজ', 'বিভাগ', 'ক্যাটাগরি', 'ক্যাটাগরিতে', 'পণ্য খুঁজছি']
        ],
        'comparison_query' => [
            'en' => ['compare', 'difference between', 'better than', 'vs', 'versus', 'which is better'],
            'bn' => ['তুলনা', 'পার্থক্য', 'এর চেয়ে ভাল', 'কোনটি ভাল', 'তুলনা করুন']
        ],
        'trending_query' => [
            'en' => ['trending', 'popular', 'best selling', 'top selling', 'most popular', 'hot products'],
            'bn' => ['ট্রেন্ডিং', 'জনপ্রিয়', 'সেরা বিক্রয়', 'সর্বাধিক বিক্রিত', 'সবচেয়ে জনপ্রিয়']
        ],
        'new_products_query' => [
            'en' => ['new arrivals', 'new products', 'latest products', 'just arrived', 'recently added'],
            'bn' => ['নতুন পণ্য', 'সাম্প্রতিক পণ্য', 'নতুন এসেছে', 'সবে এসেছে']
        ],
        'discount_query' => [
            'en' => ['discount', 'sale', 'offer', 'special offer', 'deal', 'promotion', 'bargain'],
            'bn' => ['ছাড়', 'অফার', 'প্রমোশন', 'সেল', 'স্পেশাল অফার', 'ডিসকাউন্ট']
        ],
        'price_range_query' => [
            'en' => ['under', 'less than', 'cheaper than', 'below', 'maximum', 'up to', 'not more than', 'price range', 'between'],
            'bn' => ['নিচে', 'কম দামে', 'এর কম', 'সর্বোচ্চ', 'পর্যন্ত', 'দাম সীমা', 'মধ্যে']
        ],
        'rating_query' => [
            'en' => ['best rated', 'top rated', 'highly rated', 'with good reviews', 'stars', 'ratings'],
            'bn' => ['সেরা রেটিং', 'উচ্চ মূল্যায়িত', 'ভাল রিভিউ', 'স্টার', 'রেটিং']
        ],
        'availability_query' => [
            'en' => ['in stock', 'available', 'can be delivered', 'ready to ship', 'ready for delivery'],
            'bn' => ['স্টকে আছে', 'উপলব্ধ', 'ডেলিভারি করা যাবে', 'পাঠানোর জন্য প্রস্তুত']
        ],
        'brand_query' => [
            'en' => ['brand', 'made by', 'manufactured by', 'from the brand', 'products by'],
            'bn' => ['ব্র্যান্ড', 'নির্মিত', 'প্রস্তুতকারক', 'কোম্পানি', 'কোম্পানির পণ্য']
        ]
    ];

    /**
     * Process a voice search query
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function search(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'query' => 'required|string|min:2|max:255',
            'limit' => 'nullable|integer|min:1|max:50',
            'language' => 'nullable|string|in:en,bn'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide a valid voice search query',
                $validator->errors()->messages(),
                400
            );
        }

        $query = $request->input('query');
        $limit = min((int)$request->input('limit', 10), 50);
        $language = $request->input('language', 'en');

        // Process the query to determine intent
        $intent = $this->determineIntent($query, $language);
        $searchResults = $this->processIntentBasedSearch($intent, $query, $limit);
        
        // Record this search
        $this->recordSearch($query, $searchResults['products']->count());

        // Add intent and processed query to results
        $searchResults['intent'] = $intent['type'];
        $searchResults['processed_query'] = $intent['processed_query'];
        $searchResults['ai_suggestion'] = $this->generateAiSuggestion($intent, $searchResults);

        return $this->success($searchResults);
    }

    /**
     * Determine the intent of the voice query
     * 
     * @param string $query
     * @param string $language
     * @return array
     */
    protected function determineIntent($query, $language = 'en')
    {
        $query = strtolower(trim($query));
        $intentType = 'general_search';
        $extractedData = [];
        $processedQuery = $query;
        
        // Check for price query
        if ($this->matchesIntent($query, 'price_query', $language)) {
            $intentType = 'price_query';
            $processedQuery = $this->cleanIntentKeywords($query, 'price_query', $language);
            
            // Try to extract product name from price query
            $extractedData['product_name'] = $processedQuery;
        }
        
        // Check for category query
        elseif ($this->matchesIntent($query, 'category_query', $language)) {
            $intentType = 'category_query';
            $processedQuery = $this->cleanIntentKeywords($query, 'category_query', $language);
            
            // Try to extract category name
            $extractedData['category_name'] = $processedQuery;
        }
        
        // Check for comparison query
        elseif ($this->matchesIntent($query, 'comparison_query', $language)) {
            $intentType = 'comparison_query';
            $processedQuery = $this->cleanIntentKeywords($query, 'comparison_query', $language);
            
            // Try to extract products to compare
            $products = preg_split('/(and|vs|versus|\svs\s|\sversus\s|,)/i', $processedQuery);
            if (count($products) >= 2) {
                $extractedData['products'] = array_map('trim', $products);
            }
        }
        
        // Check for trending products query
        elseif ($this->matchesIntent($query, 'trending_query', $language)) {
            $intentType = 'trending_query';
            $processedQuery = $this->cleanIntentKeywords($query, 'trending_query', $language);
            
            // Check if category was specified
            if (!empty($processedQuery)) {
                $extractedData['category_name'] = $processedQuery;
            }
        }
        
        // Check for new products query
        elseif ($this->matchesIntent($query, 'new_products_query', $language)) {
            $intentType = 'new_products_query';
            $processedQuery = $this->cleanIntentKeywords($query, 'new_products_query', $language);
            
            // Check if category was specified
            if (!empty($processedQuery)) {
                $extractedData['category_name'] = $processedQuery;
            }
        }
        
        // Check for discount query
        elseif ($this->matchesIntent($query, 'discount_query', $language)) {
            $intentType = 'discount_query';
            $processedQuery = $this->cleanIntentKeywords($query, 'discount_query', $language);
            
            // Check if product or category was specified
            if (!empty($processedQuery)) {
                $extractedData['search_term'] = $processedQuery;
            }
        }
        
        // Check for price range query
        elseif ($this->matchesIntent($query, 'price_range_query', $language)) {
            $intentType = 'price_range_query';
            $processedQuery = $this->cleanIntentKeywords($query, 'price_range_query', $language);
            
            // Try to extract price range and product/category
            preg_match('/(\d+)/', $query, $matches);
            if (!empty($matches)) {
                $extractedData['price_point'] = (int)$matches[0];
                $extractedData['search_term'] = str_replace($matches[0], '', $processedQuery);
                
                // Determine if it's maximum or minimum price
                if (preg_match('/(under|less than|cheaper than|below|up to|not more than)/i', $query)) {
                    $extractedData['price_filter'] = 'max';
                } elseif (preg_match('/(over|more than|above|at least|minimum)/i', $query)) {
                    $extractedData['price_filter'] = 'min';
                } elseif (preg_match('/(between)/i', $query)) {
                    $extractedData['price_filter'] = 'between';
                    // Try to extract second number for range
                    preg_match_all('/(\d+)/', $query, $rangeMatches);
                    if (count($rangeMatches[0]) >= 2) {
                        $extractedData['price_range'] = [
                            (int)$rangeMatches[0][0],
                            (int)$rangeMatches[0][1]
                        ];
                    }
                }
            }
        }
        
        // Check for rating query
        elseif ($this->matchesIntent($query, 'rating_query', $language)) {
            $intentType = 'rating_query';
            $processedQuery = $this->cleanIntentKeywords($query, 'rating_query', $language);
            
            // Try to extract rating threshold
            preg_match('/(\d+)/', $query, $matches);
            if (!empty($matches)) {
                $extractedData['rating'] = min((int)$matches[0], 5); // Cap at 5 stars
                $extractedData['search_term'] = str_replace($matches[0], '', $processedQuery);
            } else {
                $extractedData['search_term'] = $processedQuery;
                $extractedData['rating'] = 4; // Default to 4+ stars if no number specified
            }
        }
        
        // Check for availability query
        elseif ($this->matchesIntent($query, 'availability_query', $language)) {
            $intentType = 'availability_query';
            $processedQuery = $this->cleanIntentKeywords($query, 'availability_query', $language);
            
            $extractedData['search_term'] = $processedQuery;
            $extractedData['in_stock'] = true;
        }
        
        // Check for brand query
        elseif ($this->matchesIntent($query, 'brand_query', $language)) {
            $intentType = 'brand_query';
            $processedQuery = $this->cleanIntentKeywords($query, 'brand_query', $language);
            
            $extractedData['brand_name'] = $processedQuery;
        }
        
        return [
            'type' => $intentType,
            'processed_query' => trim($processedQuery),
            'data' => $extractedData
        ];
    }

    /**
     * Check if a query matches a specific intent pattern
     * 
     * @param string $query
     * @param string $intentType
     * @param string $language
     * @return bool
     */
    protected function matchesIntent($query, $intentType, $language = 'en')
    {
        if (!isset($this->intentPatterns[$intentType][$language])) {
            return false;
        }
        
        foreach ($this->intentPatterns[$intentType][$language] as $pattern) {
            if (strpos($query, $pattern) !== false) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * Remove intent keywords from the query to extract the actual search term
     * 
     * @param string $query
     * @param string $intentType
     * @param string $language
     * @return string
     */
    protected function cleanIntentKeywords($query, $intentType, $language = 'en')
    {
        if (!isset($this->intentPatterns[$intentType][$language])) {
            return $query;
        }
        
        $cleanedQuery = $query;
        
        foreach ($this->intentPatterns[$intentType][$language] as $pattern) {
            $cleanedQuery = str_ireplace($pattern, '', $cleanedQuery);
        }
        
        // Clean up any extra spaces and common filler words
        $cleanedQuery = preg_replace('/\s+/', ' ', $cleanedQuery);
        $cleanedQuery = preg_replace('/^(the|a|an|some|me|all|any|of|for|about)\s+/i', '', $cleanedQuery);
        
        return trim($cleanedQuery);
    }

    /**
     * Process search based on determined intent
     * 
     * @param array $intent
     * @param string $originalQuery
     * @param int $limit
     * @return array
     */
    protected function processIntentBasedSearch($intent, $originalQuery, $limit)
    {
        $searchTerm = $intent['processed_query'];
        $intentType = $intent['type'];
        $extractedData = $intent['data'];
        
        // Base query for products
        $products = Product::with(['stocks', 'product_translations', 'brand', 'category', 'thumbnail'])
            ->where('published', '1')
            ->where('auction_product', 0)
            ->where('approved', '1');
            
        // Base query for categories
        $categories = Category::query();
        
        // Base query for brands
        $brands = Brand::query();
            
        // Process based on intent type
        switch ($intentType) {
            case 'price_query':
                // Search for specific product to show its price
                $productName = $extractedData['product_name'] ?? $searchTerm;
                $products->where(function ($q) use ($productName) {
                    $q->where('name', 'like', '%' . $productName . '%')
                      ->orWhere('tags', 'like', '%' . $productName . '%');
                });
                break;
                
            case 'category_query':
                // Find products in the specific category
                $categoryName = $extractedData['category_name'] ?? $searchTerm;
                
                // First find matching categories
                $matchingCategories = Category::where('name', 'like', '%' . $categoryName . '%')
                    ->orWhere('slug', 'like', '%' . $categoryName . '%')
                    ->pluck('id');
                    
                if ($matchingCategories->count() > 0) {
                    $products->whereIn('category_id', $matchingCategories);
                } else {
                    // Fallback to product name/tags if no matching category
                    $products->where(function ($q) use ($categoryName) {
                        $q->where('name', 'like', '%' . $categoryName . '%')
                          ->orWhere('tags', 'like', '%' . $categoryName . '%');
                    });
                }
                
                // Get more category data for display
                $categories->where('name', 'like', '%' . $categoryName . '%')
                    ->orWhere('slug', 'like', '%' . $categoryName . '%');
                break;
                
            case 'comparison_query':
                // Handle product comparison
                if (isset($extractedData['products']) && count($extractedData['products']) >= 2) {
                    $comparisonProducts = $extractedData['products'];
                    $products->where(function ($query) use ($comparisonProducts) {
                        foreach ($comparisonProducts as $product) {
                            $query->orWhere('name', 'like', '%' . $product . '%')
                                  ->orWhere('tags', 'like', '%' . $product . '%');
                        }
                    });
                } else {
                    // Fallback if we couldn't extract specific products
                    $products->where(function ($q) use ($searchTerm) {
                        $q->where('name', 'like', '%' . $searchTerm . '%')
                          ->orWhere('tags', 'like', '%' . $searchTerm . '%');
                    });
                }
                break;
                
            case 'trending_query':
                // Get trending/popular products
                $products->orderBy('num_of_sale', 'desc');
                
                if (!empty($extractedData['category_name'])) {
                    $categoryName = $extractedData['category_name'];
                    
                    // Find matching categories
                    $matchingCategories = Category::where('name', 'like', '%' . $categoryName . '%')
                        ->orWhere('slug', 'like', '%' . $categoryName . '%')
                        ->pluck('id');
                        
                    if ($matchingCategories->count() > 0) {
                        $products->whereIn('category_id', $matchingCategories);
                    }
                }
                break;
                
            case 'new_products_query':
                // Get newest products
                $products->orderBy('created_at', 'desc');
                
                if (!empty($extractedData['category_name'])) {
                    $categoryName = $extractedData['category_name'];
                    
                    // Find matching categories
                    $matchingCategories = Category::where('name', 'like', '%' . $categoryName . '%')
                        ->orWhere('slug', 'like', '%' . $categoryName . '%')
                        ->pluck('id');
                        
                    if ($matchingCategories->count() > 0) {
                        $products->whereIn('category_id', $matchingCategories);
                    }
                }
                break;
                
            case 'discount_query':
                // Get products on sale/discount
                $products->where('discount', '>', 0)
                         ->orderBy('discount', 'desc');
                
                if (!empty($extractedData['search_term'])) {
                    $searchTerm = $extractedData['search_term'];
                    $products->where(function ($q) use ($searchTerm) {
                        $q->where('name', 'like', '%' . $searchTerm . '%')
                          ->orWhere('tags', 'like', '%' . $searchTerm . '%');
                    });
                }
                break;
                
            case 'price_range_query':
                // Handle price range filtering
                if (isset($extractedData['price_point'])) {
                    $pricePoint = $extractedData['price_point'];
                    $priceFilter = $extractedData['price_filter'] ?? 'max';
                    
                    if ($priceFilter === 'max') {
                        $products->where('unit_price', '<=', $pricePoint);
                    } elseif ($priceFilter === 'min') {
                        $products->where('unit_price', '>=', $pricePoint);
                    } elseif ($priceFilter === 'between' && isset($extractedData['price_range'])) {
                        $range = $extractedData['price_range'];
                        $products->whereBetween('unit_price', [$range[0], $range[1]]);
                    }
                }
                
                if (!empty($extractedData['search_term'])) {
                    $searchTerm = $extractedData['search_term'];
                    $products->where(function ($q) use ($searchTerm) {
                        $q->where('name', 'like', '%' . $searchTerm . '%')
                          ->orWhere('tags', 'like', '%' . $searchTerm . '%');
                    });
                }
                break;
                
            case 'rating_query':
                // Handle rating-based filtering
                if (isset($extractedData['rating'])) {
                    $rating = $extractedData['rating'];
                    $products->where('rating', '>=', $rating);
                }
                
                if (!empty($extractedData['search_term'])) {
                    $searchTerm = $extractedData['search_term'];
                    $products->where(function ($q) use ($searchTerm) {
                        $q->where('name', 'like', '%' . $searchTerm . '%')
                          ->orWhere('tags', 'like', '%' . $searchTerm . '%');
                    });
                }
                break;
                
            case 'availability_query':
                // Get in-stock products
                $products->whereHas('stocks', function($query) {
                    $query->where('qty', '>', 0);
                });
                
                if (!empty($extractedData['search_term'])) {
                    $searchTerm = $extractedData['search_term'];
                    $products->where(function ($q) use ($searchTerm) {
                        $q->where('name', 'like', '%' . $searchTerm . '%')
                          ->orWhere('tags', 'like', '%' . $searchTerm . '%');
                    });
                }
                break;
                
            case 'brand_query':
                // Find products by a specific brand
                $brandName = $extractedData['brand_name'] ?? $searchTerm;
                
                // Find matching brands
                $matchingBrands = Brand::where('name', 'like', '%' . $brandName . '%')
                    ->orWhere('slug', 'like', '%' . $brandName . '%')
                    ->pluck('id');
                    
                if ($matchingBrands->count() > 0) {
                    $products->whereIn('brand_id', $matchingBrands);
                } else {
                    // Fallback to product name/tags if no matching brand
                    $products->where(function ($q) use ($brandName) {
                        $q->where('name', 'like', '%' . $brandName . '%')
                          ->orWhere('tags', 'like', '%' . $brandName . '%');
                    });
                }
                
                // Get more brand data for display
                $brands->where('name', 'like', '%' . $brandName . '%')
                    ->orWhere('slug', 'like', '%' . $brandName . '%');
                break;
                
            case 'general_search':
            default:
                // Default search behavior
                $products->where(function ($q) use ($originalQuery) {
                    $q->where('name', 'like', '%' . $originalQuery . '%')
                      ->orWhere('tags', 'like', '%' . $originalQuery . '%')
                      ->orWhere('unit_price', 'like', '%' . $originalQuery . '%');
                });
                
                // Prioritize exact matches
                $case1 = addslashes($originalQuery) . '%';
                $case2 = '%' . addslashes($originalQuery) . '%';

                $products->orderByRaw("CASE
                    WHEN name LIKE '$case1' THEN 1
                    WHEN name LIKE '$case2' THEN 3
                    ELSE 5
                    END");
                break;
        }
        
        // Fetch results
        $products = $products->take($limit)->get();
        $categories = $categories->take(5)->get(['id', 'name', 'slug', 'banner as image', DB::raw('(SELECT COUNT(*) FROM products WHERE category_id = categories.id) as product_count')]);
        $brands = $brands->take(5)->get(['id', 'name', 'slug', 'logo', DB::raw('(SELECT COUNT(*) FROM products WHERE brand_id = brands.id) as product_count')]);
        
        // Get related searches
        $related_searches = Search::where('query', 'like', '%' . $originalQuery . '%')
            ->orderBy('count', 'desc')
            ->take(5)
            ->get(['id', 'query as text']);

        // Get popular searches
        $popular_searches = Search::orderBy('count', 'desc')
            ->take(5)
            ->get(['id', 'query as text', 'count']);
        
        // Format products for response
        $formattedProducts = $products->map(function ($product) {
            return [
                'id' => $product->id,
                'title' => $product->name,
                'slug' => $product->slug,
                'price' => $product->unit_price,
                'original_price' => $product->unit_price + ($product->unit_price * ($product->discount / 100)),
                'discount_percent' => $product->discount,
                'image' => uploaded_asset($product->thumbnail_img),
                'category' => [
                    'id' => $product->category->id,
                    'name' => $product->category->name,
                    'slug' => $product->category->slug,
                ],
                'brand' => $product->brand ? [
                    'id' => $product->brand->id,
                    'name' => $product->brand->name,
                    'slug' => $product->brand->slug,
                ] : null,
            ];
        });
        
        return [
            'products' => $formattedProducts,
            'categories' => $categories,
            'brands' => $brands,
            'related_searches' => $related_searches,
            'popular_searches' => $popular_searches
        ];
    }

    /**
     * Generate AI suggestions based on intent and results
     *
     * @param array $intent
     * @param array $results
     * @return array
     */
    protected function generateAiSuggestion($intent, $results)
    {
        $intentType = $intent['type'];
        $productCount = count($results['products']);
        $suggestions = [];
        
        // Generate follow-up questions
        switch ($intentType) {
            case 'price_query':
                if ($productCount > 0) {
                    $suggestions[] = "Would you like to add this product to your cart?";
                    $suggestions[] = "Would you like to see similar products?";
                    $suggestions[] = "Should I show you other products in this price range?";
                } else {
                    $suggestions[] = "I couldn't find that exact product. Try describing it differently?";
                    $suggestions[] = "Would you like to browse similar products instead?";
                }
                break;
                
            case 'category_query':
                if ($productCount > 0) {
                    $suggestions[] = "Would you like to filter these results by price?";
                    $suggestions[] = "Would you like to see the newest products in this category?";
                    $suggestions[] = "Should I show you the bestsellers in this category?";
                } else {
                    $suggestions[] = "I couldn't find products in that category. Try a different category?";
                    $suggestions[] = "Would you like to see our popular categories instead?";
                }
                break;
                
            case 'comparison_query':
                if ($productCount >= 2) {
                    $suggestions[] = "Would you like to compare their specifications side by side?";
                    $suggestions[] = "Would you like to see reviews for these products?";
                    $suggestions[] = "Should I recommend which one is better for you?";
                } else {
                    $suggestions[] = "I couldn't find all the products to compare. Try naming them differently?";
                    $suggestions[] = "Would you like to see our product recommendations instead?";
                }
                break;
                
            case 'trending_query':
                $suggestions[] = "Would you like to see more details about any of these trending products?";
                $suggestions[] = "Would you like to filter these trending products by price?";
                $suggestions[] = "Should I show you the newest arrivals instead?";
                break;
                
            case 'new_products_query':
                $suggestions[] = "Would you like to see more details about any of these new products?";
                $suggestions[] = "Would you like to filter these new products by price?";
                $suggestions[] = "Should I show you trending products instead?";
                break;
                
            case 'discount_query':
                $suggestions[] = "Would you like to see products with bigger discounts?";
                $suggestions[] = "Would you like to filter these discounted products by price?";
                $suggestions[] = "Should I show you our special offers and promotions?";
                break;
                
            case 'price_range_query':
                $suggestions[] = "Would you like to adjust the price range?";
                $suggestions[] = "Would you like to see products from a specific brand in this price range?";
                $suggestions[] = "Should I show you the best-selling products in this price range?";
                break;
                
            case 'rating_query':
                $suggestions[] = "Would you like to see the reviews for these highly-rated products?";
                $suggestions[] = "Would you like to filter these by price as well?";
                $suggestions[] = "Should I show you our most popular products regardless of rating?";
                break;
                
            case 'availability_query':
                $suggestions[] = "Would you like to see products with the fastest delivery times?";
                $suggestions[] = "Would you like to filter these in-stock products by price?";
                $suggestions[] = "Should I show you our newest arrivals that are in stock?";
                break;
                
            case 'brand_query':
                $suggestions[] = "Would you like to see the bestsellers from this brand?";
                $suggestions[] = "Would you like to filter these products by price?";
                $suggestions[] = "Should I show you products on sale from this brand?";
                break;
                
            case 'general_search':
            default:
                if ($productCount > 0) {
                    $suggestions[] = "Would you like to filter these results by price?";
                    $suggestions[] = "Would you like to see similar products?";
                    $suggestions[] = "Should I show you more specific options?";
                } else {
                    $suggestions[] = "I couldn't find what you're looking for. Try describing it differently?";
                    $suggestions[] = "Would you like to browse our popular categories instead?";
                    $suggestions[] = "Should I show you our bestselling products?";
                }
                break;
        }
        
        // Add contextual suggestions based on result count
        if ($productCount > 10) {
            $suggestions[] = "Would you like me to narrow down these results?";
        } else if ($productCount == 0) {
            $suggestions[] = "Try searching with different keywords or broader terms.";
        }
        
        // Pick 3 random suggestions to avoid repetition
        shuffle($suggestions);
        return array_slice($suggestions, 0, 3);
    }

    /**
     * Save voice search to user's history
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveToHistory(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'query' => 'required|string|min:2|max:255',
            'result_count' => 'nullable|integer'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        // Ensure user is authenticated
        if (!Auth::check()) {
            return $this->error(
                'AUTHENTICATION_REQUIRED',
                'You must be logged in to save voice search history',
                null,
                401
            );
        }

        // Create voice search history record
        VoiceSearch::create([
            'user_id' => Auth::id(),
            'query' => $request->input('query'),
            'result_count' => $request->input('result_count', 0),
            'created_at' => now(),
            'updated_at' => now()
        ]);

        return $this->success(null, 'Voice search history saved successfully');
    }

    /**
     * Get user's voice search history
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getHistory(Request $request)
    {
        // Ensure user is authenticated
        if (!Auth::check()) {
            return $this->error(
                'AUTHENTICATION_REQUIRED',
                'You must be logged in to view voice search history',
                null,
                401
            );
        }

        $limit = min((int)$request->input('limit', 10), 50);

        // Get user's voice search history
        $history = VoiceSearch::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->take($limit)
            ->get()
            ->map(function($item) {
                return [
                    'id' => $item->id,
                    'query' => $item->query,
                    'timestamp' => $item->created_at->toIso8601String(),
                    'result_count' => $item->result_count
                ];
            });

        return $this->success($history);
    }

    /**
     * Clear user's voice search history
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function clearHistory()
    {
        // Ensure user is authenticated
        if (!Auth::check()) {
            return $this->error(
                'AUTHENTICATION_REQUIRED',
                'You must be logged in to clear voice search history',
                null,
                401
            );
        }

        // Delete all user's voice search history
        VoiceSearch::where('user_id', Auth::id())->delete();

        return $this->success(null, 'Voice search history cleared successfully');
    }

    /**
     * Record search to the database
     * 
     * @param string $query
     * @param int $resultCount
     * @return void
     */
    private function recordSearch($query, $resultCount = 0)
    {
        try {
            // Update search count
            $search = Search::firstOrCreate(
                ['query' => $query],
                ['count' => 0]
            );
            $search->increment('count');

            // Record in voice search history if user is logged in
            if (Auth::check()) {
                VoiceSearch::create([
                    'user_id' => Auth::id(),
                    'query' => $query,
                    'result_count' => $resultCount,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
        } catch (\Exception $e) {
            // Silent fail - don't disrupt the user experience if recording fails
            logger()->error('Failed to record voice search', ['error' => $e->getMessage()]);
        }
    }
} 