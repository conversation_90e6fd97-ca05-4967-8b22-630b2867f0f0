<?php

namespace App\Http\Controllers\Api\V3\Auth;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Models\EmailVerify;
use App\Models\User;
use App\Notifications\RegistrationOTPNotification;
use App\Services\EmailVerificationSecurityService;
use App\Services\EmailVerificationPerformanceService;
use App\Services\EmailVerificationMonitoringService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class EmailVerificationController extends ApiResponse
{
    /**
     * Email verification security service
     */
    protected $securityService;

    /**
     * Email verification performance service
     */
    protected $performanceService;

    /**
     * Email verification monitoring service
     */
    protected $monitoringService;

    /**
     * Constructor
     */
    public function __construct(
        EmailVerificationSecurityService $securityService,
        EmailVerificationPerformanceService $performanceService,
        EmailVerificationMonitoringService $monitoringService
    ) {
        $this->securityService = $securityService;
        $this->performanceService = $performanceService;
        $this->monitoringService = $monitoringService;
    }
    /**
     * Check if email exists and send OTP for registration with anti-enumeration protection
     */
    public function checkEmailAndSendOTP(Request $request)
    {
       
        // Add timing attack protection
        $startTime = microtime(true);

        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'user_type' => 'required|in:customer,seller,dropshipper'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'Validation Error',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            $email = $request->email;
            $userType = $request->user_type;

            // Log email verification attempt
            $this->monitoringService->logEmailVerificationEvent('email_check_initiated', [
                'email' =>  $email,
                'user_type' => $userType,
                'ip' => $request->ip()
            ]);

            // Use optimized email existence check with caching
            $existingUser = $this->performanceService->checkEmailExistsOptimized($email);
            $emailVerifyRecord = null;

            if ($existingUser) {
                // Log potential enumeration attempt
                Log::warning('Email enumeration attempt detected', [
                    'email' =>  $email,
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'existing_user_type' => $existingUser->user_type,
                    'requested_user_type' => $userType
                ]);

                // Still perform OTP generation to maintain consistent timing
                $this->performDummyOTPGeneration();

                // Determine redirect URL based on user type
                $redirectMessage = $this->getExistingUserMessage($existingUser->user_type, $userType);

                // Ensure consistent response time before returning
                $this->ensureConsistentTiming($startTime);

                return $this->error(
                    'Account already exists',
                    $redirectMessage,
                    409
                );
            }

            // Check if email is already verified for registration
            if (EmailVerify::isEmailVerified($email)) {
                return $this->success([
                    'email' => $email,
                    'already_verified' => true,
                    'message' => 'Email is already verified. You can proceed with registration.'
                ], 'Email already verified', 200);
            }

            // Check 5-minute resend cooldown
            $existingRecord = EmailVerify::where('email', $email)
                ->where('is_verify', false)
                ->first();

            if ($existingRecord && $existingRecord->isInCooldownPeriod()) {
                $remainingCooldown = $existingRecord->getRemainingCooldownMinutes();
                return $this->error(
                    'Resend cooldown active',
                    "Please wait {$remainingCooldown} minutes before requesting a new verification code.",
                    429
                );
            }

            // Check resend limit (max 5 attempts per email)
            if ($existingRecord && !$existingRecord->canResend()) {
                return $this->error(
                    'Resend limit exceeded',
                    'Maximum OTP attempts reached for this email. Please try again after 1 hour.',
                    429
                );
            }

            // Generate and send OTP
            $emailVerifyRecord = EmailVerify::generateOTP($email, 10); // 10 minutes expiry

            // Send notification
            $dummyUser = (object) ['name' => 'User', 'email' => $email];
            Notification::route('mail', $email)
                ->notify(new RegistrationOTPNotification($emailVerifyRecord));

            // Log successful OTP send
            $this->monitoringService->logEmailVerificationEvent('otp_sent_successfully', [
                'email' => $email,
                'user_type' => $userType,
                'resend_count' => $emailVerifyRecord->resend_count,
                'expires_in_minutes' => 10
            ]);

            return $this->success([
                'email' => $email,
                'otp_sent' => true,
                'expires_at' => $emailVerifyRecord->expire_time->toISOString(),
                'expires_in_minutes' => 10,
                'resend_count' => $emailVerifyRecord->resend_count,
                'max_resend_attempts' => 5,
                'next_resend_available_at' => $emailVerifyRecord->getNextResendTime()->toISOString()
            ], 'Verification code sent successfully to your email', 200);

        } catch (\Exception $exception) {
            Log::channel('api_registration')->error('Error in email verification: ' . $exception->getMessage());

            return $this->error(
                'Failed to send OTP',
                'Something went wrong while sending the verification code. Please try again later.',
                500
            );
        }
    }

    /**
     * Verify OTP for email verification with enhanced security
     */
    public function verifyOTP(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'otp' => 'required|string|size:6'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'Validation Error',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            $email = $request->email;
            $otp = $request->otp;
            $ip = $request->ip();

            // Security checks
            if ($this->securityService->isEmailBlocked($email)) {
                return $this->error(
                    'Account temporarily blocked',
                    'This email has been temporarily blocked due to too many failed attempts. Please try again later.',
                    429
                );
            }

            if ($this->securityService->isIpBlocked($ip)) {
                return $this->error(
                    'Access temporarily blocked',
                    'Your access has been temporarily blocked due to suspicious activity. Please try again later.',
                    429
                );
            }

            // Validate OTP format
            if (!$this->securityService->validateOtpFormat($otp)) {
                $this->securityService->trackFailedAttempt($email, $ip);
                return $this->error(
                    'Invalid verification code format',
                    'The verification code format is invalid. Please check and try again.',
                    400
                );
            }

            // Detect bot activity
            if ($this->securityService->detectBotActivity($request->userAgent(), $request->headers->all())) {
                $this->securityService->logSecurityEvent('bot_activity_detected', [
                    'email' => $email,
                    'ip' => $ip,
                    'user_agent' => $request->userAgent()
                ]);

                return $this->error(
                    'Verification failed',
                    'Automated requests are not allowed. Please try again from a web browser.',
                    403
                );
            }

            // Use optimized OTP record retrieval with caching
            $emailVerifyRecord = $this->performanceService->getOTPRecordOptimized($email, $otp);

            if (!$emailVerifyRecord) {
                $this->securityService->trackFailedAttempt($email, $ip);
                return $this->error(
                    'Invalid verification code',
                    'The verification code is incorrect. Please check and try again.',
                    400
                );
            }

            // Check if OTP is expired
            if ($emailVerifyRecord->isExpired()) {
                $this->securityService->trackFailedAttempt($email, $ip);
                return $this->error(
                    'Verification code expired',
                    'The verification code has expired. Please request a new one.',
                    400
                );
            }

            // Verify the OTP with enhanced security
            $verified = EmailVerify::verifyOTP($email, $otp);

            if ($verified) {
                // Clear failed attempts on successful verification
                $this->securityService->clearFailedAttempts($email, $ip);

                // Invalidate caches for this email
                $this->performanceService->invalidateEmailCaches($email);

                // Log successful verification
                $this->securityService->logSecurityEvent('otp_verification_success', [
                    'email' => $email,
                    'ip' => $ip
                ]);

                // Log monitoring event for successful verification
                $this->monitoringService->logEmailVerificationEvent('email_verification_completed', [
                    'email' => $email,
                    'ip' => $ip,
                    'verification_time' => Carbon::now()->toISOString()
                ]);

                return $this->success([
                    'email' => $email,
                    'verified' => true,
                    'verified_at' => Carbon::now()->toISOString()
                ], 'Email verified successfully', 200);
            } else {
                $this->securityService->trackFailedAttempt($email, $ip);
                return $this->error(
                    'Verification failed',
                    'Failed to verify the verification code. Please try again.',
                    400
                );
            }

        } catch (\Exception $exception) {
            Log::channel('api_registration')->error('Error in OTP verification: ' . $exception->getMessage());

            return $this->error(
                'Verification failed',
                'Something went wrong while verifying the code. Please try again later.',
                500
            );
        }
    }

    /**
     * Resend OTP for email verification
     */
    public function resendOTP(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'Validation Error',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            $email = $request->email;

            // Check if email is already verified
            if (EmailVerify::isEmailVerified($email)) {
                return $this->error(
                    'Already verified',
                    'This email is already verified. You can proceed with registration.',
                    400
                );
            }

            // Get existing record
            $existingRecord = EmailVerify::where('email', $email)
                ->where('is_verify', false)
                ->first();

            if (!$existingRecord) {
                return $this->error(
                    'No pending verification',
                    'No pending email verification found. Please start the verification process first.',
                    404
                );
            }

            // Check 5-minute resend cooldown
            if ($existingRecord->isInCooldownPeriod()) {
                $remainingCooldown = $existingRecord->getRemainingCooldownMinutes();
                return $this->error(
                    'Resend cooldown active',
                    "Please wait {$remainingCooldown} minutes before requesting a new verification code.",
                    429
                );
            }

            // Check resend limit (max 5 attempts)
            if (!$existingRecord->canResend()) {
                return $this->error(
                    'Resend limit exceeded',
                    'Maximum OTP attempts reached for this email. Please try again after 1 hour.',
                    429
                );
            }

            // Generate and send new OTP
            $emailVerifyRecord = EmailVerify::generateOTP($email, 10); // 10 minutes expiry

            // Invalidate caches for this email
            $this->performanceService->invalidateEmailCaches($email);

            // Send notification
            $dummyUser = (object) ['name' => 'User', 'email' => $email];
            Notification::route('mail', $email)
                ->notify(new RegistrationOTPNotification($emailVerifyRecord));

            return $this->success([
                'email' => $email,
                'otp_sent' => true,
                'expires_at' => $emailVerifyRecord->expire_time->toISOString(),
                'expires_in_minutes' => 10,
                'resend_count' => $emailVerifyRecord->resend_count,
                'max_resend_attempts' => 5,
                'next_resend_available_at' => $emailVerifyRecord->getNextResendTime()->toISOString()
            ], 'Verification code resent successfully', 200);

        } catch (\Exception $exception) {
            Log::channel('api_registration')->error('Error in OTP resend: ' . $exception->getMessage());

            return $this->error(
                'Failed to resend OTP',
                'Something went wrong while resending the verification code. Please try again later.',
                500
            );
        }
    }

    /**
     * Get appropriate message for existing user based on user types
     */
    private function getExistingUserMessage($existingUserType, $requestedUserType)
    {
        $messages = [
            'same_type' => "An account with this email already exists. Please login to your {$existingUserType} account.",
            'different_type' => "This email is registered as a {$existingUserType}. Please use a different email or login to your existing account."
        ];

        if ($existingUserType === $requestedUserType) {
            return $messages['same_type'];
        } else {
            return $messages['different_type'];
        }
    }

    /**
     * Check email verification status
     */
    public function checkVerificationStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'Validation Error',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            $email = $request->email;

            // Use optimized verification status check with caching
            $data = $this->performanceService->getVerificationStatusOptimized($email);

            return $this->success($data, 'Verification status retrieved successfully', 200);

        } catch (\Exception $exception) {
            Log::channel('api_registration')->error('Error in checking verification status: ' . $exception->getMessage());

            return $this->error(
                'Failed to check status',
                'Something went wrong while checking verification status. Please try again later.',
                500
            );
        }
    }

    /**
     * Securely check if user exists with timing attack protection
     */
    private function checkUserExistsSecurely($email)
    {
        // Always perform the same database query regardless of result
        $user = User::where('email', $email)->first();

        // Add consistent delay to prevent timing attacks
        usleep(rand(50000, 150000)); // Random delay between 50-150ms

        return $user;
    }

    /**
     * Perform dummy OTP generation to maintain consistent timing
     */
    private function performDummyOTPGeneration()
    {
        // Generate a dummy OTP but don't save it
        $dummyOtp = str_pad(mt_rand(100000, 999999), 6, '0', STR_PAD_LEFT);

        // Simulate database operations
        usleep(rand(10000, 50000)); // Random delay between 10-50ms

        return $dummyOtp;
    }

    /**
     * Ensure consistent response timing to prevent timing attacks
     */
    private function ensureConsistentTiming($startTime, $minTime = 0.2)
    {
        $elapsedTime = microtime(true) - $startTime;

        if ($elapsedTime < $minTime) {
            usleep(($minTime - $elapsedTime) * 1000000);
        }
    }
}
