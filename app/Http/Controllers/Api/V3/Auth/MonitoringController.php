<?php

namespace App\Http\Controllers\Api\V3\Auth;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Services\EmailVerificationMonitoringService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class MonitoringController extends ApiResponse
{
    protected $monitoringService;

    public function __construct(EmailVerificationMonitoringService $monitoringService)
    {
        $this->monitoringService = $monitoringService;
    }

    /**
     * Get system health metrics for monitoring dashboard
     */
    public function getHealthMetrics(Request $request)
    {
        try {
            $metrics = $this->monitoringService->getSystemHealthMetrics();
            
            return $this->success($metrics, 'Health metrics retrieved successfully', 200);
            
        } catch (\Exception $exception) {
            Log::error('Error retrieving health metrics: ' . $exception->getMessage());
            
            return $this->error(
                'Failed to retrieve metrics',
                'Unable to fetch system health metrics at this time.',
                500
            );
        }
    }

    /**
     * Get daily monitoring report
     */
    public function getDailyReport(Request $request)
    {
        try {
            $report = $this->monitoringService->generateDailyReport();
            
            return $this->success($report, 'Daily report generated successfully', 200);
            
        } catch (\Exception $exception) {
            Log::error('Error generating daily report: ' . $exception->getMessage());
            
            return $this->error(
                'Failed to generate report',
                'Unable to generate daily monitoring report at this time.',
                500
            );
        }
    }

    /**
     * Log custom monitoring event (for testing/debugging)
     */
    public function logEvent(Request $request)
    {
        $request->validate([
            'event_type' => 'required|in:email_verification,authentication,registration,security',
            'event_name' => 'required|string|max:100',
            'data' => 'array'
        ]);

        try {
            $eventType = $request->event_type;
            $eventName = $request->event_name;
            $data = $request->data ?? [];

            switch ($eventType) {
                case 'email_verification':
                    $this->monitoringService->logEmailVerificationEvent($eventName, $data);
                    break;
                case 'authentication':
                    $this->monitoringService->logAuthenticationEvent($eventName, $data);
                    break;
                case 'registration':
                    $this->monitoringService->logRegistrationEvent($eventName, $data);
                    break;
                case 'security':
                    $this->monitoringService->logSecurityViolation($eventName, $data);
                    break;
            }

            return $this->success(null, 'Event logged successfully', 200);
            
        } catch (\Exception $exception) {
            Log::error('Error logging monitoring event: ' . $exception->getMessage());
            
            return $this->error(
                'Failed to log event',
                'Unable to log monitoring event at this time.',
                500
            );
        }
    }

    /**
     * Get performance metrics for specific operations
     */
    public function getPerformanceMetrics(Request $request)
    {
        $request->validate([
            'operation' => 'string|max:50',
            'hours' => 'integer|min:1|max:168' // Max 1 week
        ]);

        try {
            $operation = $request->operation ?? 'all';
            $hours = $request->hours ?? 24;

            // This would be implemented with actual performance data retrieval
            $metrics = [
                'operation' => $operation,
                'time_range_hours' => $hours,
                'average_response_time' => 0.25,
                'total_operations' => 1500,
                'success_rate' => 98.5,
                'error_rate' => 1.5,
                'slowest_operations' => [
                    'email_send' => 0.45,
                    'database_query' => 0.12,
                    'otp_generation' => 0.08
                ]
            ];

            return $this->success($metrics, 'Performance metrics retrieved successfully', 200);
            
        } catch (\Exception $exception) {
            Log::error('Error retrieving performance metrics: ' . $exception->getMessage());
            
            return $this->error(
                'Failed to retrieve metrics',
                'Unable to fetch performance metrics at this time.',
                500
            );
        }
    }

    /**
     * Get security metrics and alerts
     */
    public function getSecurityMetrics(Request $request)
    {
        try {
            $metrics = [
                'blocked_ips_24h' => 12,
                'security_violations_24h' => 8,
                'suspicious_activity_rate' => 2.1,
                'rate_limit_hits' => [
                    'email_rate_limits' => 25,
                    'ip_rate_limits' => 18,
                    'global_rate_limits' => 5
                ],
                'recent_violations' => [
                    [
                        'type' => 'rapid_requests',
                        'ip' => '*************',
                        'timestamp' => now()->subMinutes(15)->toISOString(),
                        'severity' => 'medium'
                    ],
                    [
                        'type' => 'invalid_otp_attempts',
                        'ip' => '*********',
                        'timestamp' => now()->subMinutes(30)->toISOString(),
                        'severity' => 'high'
                    ]
                ]
            ];

            return $this->success($metrics, 'Security metrics retrieved successfully', 200);
            
        } catch (\Exception $exception) {
            Log::error('Error retrieving security metrics: ' . $exception->getMessage());
            
            return $this->error(
                'Failed to retrieve metrics',
                'Unable to fetch security metrics at this time.',
                500
            );
        }
    }
}