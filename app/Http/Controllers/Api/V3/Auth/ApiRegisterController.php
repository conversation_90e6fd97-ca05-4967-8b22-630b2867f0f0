<?php

namespace App\Http\Controllers\Api\V3\Auth;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\User\DropshipperProfileResource;
use App\Http\Resources\V3\User\SellerProfileResource;
use App\Http\Resources\V3\User\UserResource;
use App\Models\B2BProfile;
use App\Models\User;
use App\Models\EmailVerify;
use App\Notifications\OTPEmailVerificationNotification;
use App\Services\EnhancedCartService;
use App\Services\EmailVerificationMonitoringService;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ApiRegisterController extends ApiResponse
{
    protected $monitoringService;

    public function __construct(EmailVerificationMonitoringService $monitoringService)
    {
        $this->monitoringService = $monitoringService;
    }

    public function register(Request $request)
    {
        // Validate request data
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'email' => 'required|email|unique:users',
            'password' => 'required|min:6',
            'confirmPassword' => 'required|same:password',
            'user_type' => 'required|in:customer,seller,dropshipper'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        // Check if email is verified
        if (!EmailVerify::isEmailVerified($request->email)) {
            return $this->error(
                403,
                'Email not verified',
                'Please verify your email before completing registration. Check your email for the verification code.'
            );
        }
        // $phone =$request->phone;
        // $phoneValid = phoneNumberFormatValidation($phone);

        // if (!$phoneValid)
        // {
        //     return $this->error(400,
        //         'Invalid phone number.',
        //         $phone. ", is not a valid mobile number. Please correct and try again."
        //     );
        // }
        DB::beginTransaction();
        try {
            // Log registration attempt
            $this->monitoringService->logRegistrationEvent('registration_initiated', [
                'email' => $request->email,
                'user_type' => 'customer',
                'flow' => 'email_verified'
            ]);

            $user = new User();
            $user->name = $request->name;
            $user->email = $request->email;
            $user->password = bcrypt($request->password);
            $user->user_type = 'customer';
            $user->email_verified_at = Carbon::now(); // Auto-verify since email was already verified
            $user->save();

            // Auto-login the user after registration
            $authData = $this->performAutoLogin($user, $request);

            // Log successful registration with auto-login
            $this->monitoringService->logRegistrationEvent('registration_completed', [
                'user_id' => $user->id,
                'email' =>  $user->email,
                'user_type' => 'customer',
                'auto_login' => true,
                'cart_merge' => $request->has('temp_user_id'),
                'flow' => 'email_verified'
            ]);

            DB::commit();
            return $this->success(
                $authData,
                'Registration successful. You have been automatically logged in.',
                201
            );
        } catch (Exception $exception) {
            Log::channel('api_registration')->error('Error in user registration in : ApiRegisterController::register ' . print_r($exception->getMessage(),true));
            DB::rollBack();
            return $this->error(400,
                'Registration failed.',
                'Registration failed.Please try again later.'
            );
        }
    }
    public function register_seller(Request $request)
    {

        // Validate request data
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'email' => 'required|email|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'password_confirmation' => 'required|string|same:password',
            'phone' => 'required|regex:/^\+?\d{10,15}$/',
            'business_name' => 'required|string',
            'business_address' => 'required|array',
            'business_address.street' => 'required|string',
            'business_address.city' => 'required|string',
            'business_address.state' => 'required|string',
            'business_address.zipCode' => 'required|string',
            'business_address.country' => 'required|string',
            'tax_id' => 'required|string',
            'business_type' => 'required|string',
            'website' => 'nullable|url',
            'category' => 'required|string',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        // Check if email is verified
        if (!EmailVerify::isEmailVerified($request->email)) {
            return $this->error(
                403,
                'Email not verified',
                'Please verify your email before completing registration. Check your email for the verification code.'
            );
        }

      /*  $phone =$request->phone;
        $phoneValid = phoneNumberFormatValidation($phone);
        if (!$phoneValid)
        {
            return $this->error(400,
                'Invalid phone number.',
                $phone. ", is not a valid mobile number. Please correct and try again."
            );
        }*/

        DB::beginTransaction();
        try {
            // Log seller registration attempt
            $this->monitoringService->logRegistrationEvent('registration_initiated', [
                'email' => $request->email,
                'user_type' => 'seller',
                'flow' => 'email_verified',
                'business_name' => $request->business_name
            ]);

            $user = new User();
            $user->name = $request->name;
            $user->email = $request->email;
            $user->password = bcrypt($request->password);
            $user->user_type = 'seller';
            $user->phone = $request->phone;
            $user->email_verified_at = Carbon::now(); // Auto-verify since email was already verified
            $user->save();

            $seller = new B2BProfile();
            $seller->slug = preg_replace('/[^A-Za-z0-9\-]/', '', str_replace(' ', '-', $request->name)).'-'.Str::random(5);;
            $seller->user_id = $user->id;
            $seller->verification_status = 0;
            $seller->name = $request->business_name;
            $seller->business_address = json_encode($request->business_address);
            $seller->tax_id = $request->tax_id;
            $seller->business_type = $request->business_type;
            $seller->website = $request->website;
            $seller->business_category = $request->category;
            $seller->save();

            // Auto-login the user after registration
            $authData = $this->performAutoLogin($user, $request, $seller, 'seller');

            // Log successful seller registration with auto-login
            $this->monitoringService->logRegistrationEvent('registration_completed', [
                'user_id' => $user->id,
                'email' =>  $user->email,
                'user_type' => 'seller',
                'auto_login' => true,
                'cart_merge' => $request->has('temp_user_id'),
                'flow' => 'email_verified',
                'business_name' => $request->business_name,
                'business_type' => $request->business_type
            ]);

            DB::commit();
            return $this->success(
                $authData,
                'Registration successful. You have been automatically logged in.',
                201
            );
        } catch (Exception $exception) {
            Log::channel('api_registration')->error('Error in user registration in : ApiRegisterController::register ' . print_r($exception->getMessage(),true));
            DB::rollBack();
            return $this->error(400,
                'Registration failed.',
                'Registration failed.Please try again later.'
            );
        }
    }
    public function register_dropshipper(Request $request)
    {

        // Validate request data
        $validator = Validator::make($request->all(), [
            'name' => 'required|string',
            'email' => 'required|email|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'password_confirmation' => 'required|string|same:password',
            'phone' => 'required|regex:/^\+?\d{10,15}$/',
            'company_name' => 'required|string',

            // Business Address Validation
            'business_address' => 'required|array',
            'business_address.street' => 'required|string',
            'business_address.city' => 'required|string',
            'business_address.state' => 'required|string',
            'business_address.zipCode' => 'required|string',
            'business_address.country' => 'required|string',

            // Shipping Address Validation
            'shipping_address' => 'required|array',
            'shipping_address.street' => 'required|string',
            'shipping_address.city' => 'required|string',
            'shipping_address.state' => 'required|string',
            'shipping_address.zipCode' => 'required|string',
            'shipping_address.country' => 'required|string',

            'tax_id' => 'required|string',
            'business_type' => 'required|string',
            'website' => 'nullable|url',
        ]);
        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        // Check if email is verified
        if (!EmailVerify::isEmailVerified($request->email)) {
            return $this->error(
                403,
                'Email not verified',
                'Please verify your email before completing registration. Check your email for the verification code.'
            );
        }

        /*$phone =$request->phone;
        $phoneValid = phoneNumberFormatValidation($phone);
        if (!$phoneValid)
        {
            return $this->error(400,
                'Invalid phone number.',
                $phone. ", is not a valid mobile number. Please correct and try again."
            );
        }*/

        DB::beginTransaction();
        try {
            // Log dropshipper registration attempt
            $this->monitoringService->logRegistrationEvent('registration_initiated', [
                'email' => $request->email,
                'user_type' => 'dropshipper',
                'flow' => 'email_verified',
                'company_name' => $request->company_name
            ]);

            $user = new User();
            $user->name = $request->name;
            $user->email = $request->email;
            $user->password = bcrypt($request->password);
            $user->user_type = $request->user_type ?? 'b2b';
            $user->phone = $request->phone;
            $user->email_verified_at = Carbon::now(); // Auto-verify since email was already verified
            $user->save();

            $dropshipper = new B2BProfile();
            $dropshipper->slug = preg_replace('/[^A-Za-z0-9\-]/', '', str_replace(' ', '-', $request->name)).'-'.Str::random(5);;
            $dropshipper->user_id = $user->id;
            $dropshipper->verification_status = 0;
            $dropshipper->name = $request->company_name;
            $dropshipper->business_address = json_encode($request->business_address);
            $dropshipper->shipping_address = json_encode($request->shipping_address);
            $dropshipper->tax_id = $request->tax_id;
            $dropshipper->business_type = $request->business_type;
            $dropshipper->website = $request->website;
            $dropshipper->save();

            // Auto-login the user after registration
            $authData = $this->performAutoLogin($user, $request, $dropshipper, $request->user_type ?? 'b2b');

            // Log successful dropshipper registration with auto-login
            $this->monitoringService->logRegistrationEvent('registration_completed', [
                'user_id' => $user->id,
                'email' => $user->email,
                'user_type' => 'dropshipper',
                'auto_login' => true,
                'cart_merge' => $request->has('temp_user_id'),
                'flow' => 'email_verified',
                'company_name' => $request->company_name,
                'business_type' => $request->business_type
            ]);

            DB::commit();
            return $this->success(
                $authData,
                'Registration successful. You have been automatically logged in.',
                201
            );
        } catch (Exception $exception) {
            Log::channel('api_registration')->error('Error in user registration in : ApiRegisterController::register ' . print_r($exception->getMessage(),true));
            DB::rollBack();
            return $this->error(400,
                'Registration failed.',
                'Registration failed.Please try again later.'
            );
        }
    }
    protected function generateAuthData(User $user,$dropshipper_or_seller_data = null,$user_type = 'customer')
    {
        $token = $user->createToken('API Token');
        if($user_type == 'seller'){
            return [
                'user' => new UserResource($user),
                'seller' => new SellerProfileResource($dropshipper_or_seller_data),
                'token' => [
                    'access_token' => $token->plainTextToken,
                    'token_type' => 'Bearer',
                    'expires_in' => config('sanctum.expiration') * 60
                ]
            ];
        }else if ($user_type == 'b2b') {
            return [
                'user' => new UserResource($user),
                'dropshipper' => new DropshipperProfileResource($dropshipper_or_seller_data),
                'token' => [
                    'access_token' => $token->plainTextToken,
                    'token_type' => 'Bearer',
                    'expires_in' => config('sanctum.expiration') * 60
                ]
            ];
        }else{
            return [
                'user' => new UserResource($user),
                'token' => [
                    'access_token' => $token->plainTextToken,
                    'token_type' => 'Bearer',
                    'expires_in' => config('sanctum.expiration') * 60
                ]
            ];
        }
    }
    public function resendCode(Request $request){
        $user = auth()->user();
        if($user->email_verified_at != null){
            return $this->error('Already Verified','Your account is already verified',null,200);
        }
        $user->email_verification_token =  Str::uuid();
        $user->save();
        $user->notify(new OTPEmailVerificationNotification());
        return $this->success(new UserResource($user),'Verification code is sent again');
    }
    public function confirmCode(Request $request)
    {
        $messages = array(
            'verification_code.required' => translate('Verification Code is required'),
            'verification_code.min' => translate('Minimum 6 digits required for Verification Code')
        );
        $validator = Validator::make($request->all(), [
            'verification_code' => 'required|min:6',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }

        $user = auth()->user();
        if ($user->verification_code != $request->verification_code) {
            return $this->error(400,'Verification Code does not match, you can request for resending the code.');
        }
        $userTokenExpiry = Carbon::parse($user->verification_token_expire_at);
        if ($userTokenExpiry->isPast()) {
            return $this->error(400,'Verification Code Expired.Please Click on Resend verification email !');
        }
        $user->verification_token_expire_at = Null;
        $user->verification_code = null;
        $user->email_verification_token = null;
        $user->email_verified_at = Carbon::now();
        $user->save();
        return $this->success(new UserResource($user),'Your account is now verified');
    }

    public function verifyOtp(Request $request)
    {
        // $user = auth()->user();
        // if($user->email_verified_at != null){
        //     return $this->error('Already Verified','Your account is already verified',null,200);
        // }
        // return $this->success($request->all(),'Verification code');
        $messages = array(
            'verification_code.required' => translate('Verification Code is required'),
            'verification_code.min' => translate('Minimum 6 digits required for Verification Code')
        );
        $validator = Validator::make($request->all(), [
            'verification_code' => 'required|min:6',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }
        $user = auth()->user();
        if ($user->verification_code != $request->verification_code) {
            return $this->error(400,'Verification Code does not match, you can request for resending the code.');
        }
        $user->verification_code = null;
        $user->email_verification_token = null;
        $user->email_verified_at = Carbon::now();
        $user->save();
        return $this->success(new UserResource($user),'Your account is now verified');
    }

    public function resendOtp(Request $request)
    {
        $messages = array(
            'email.required' => translate('Email is required'),
            'email.email' => translate('Email must be a valid email address')
        );
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }
        $user = User::where('email', $request->email)->first();
        if (!$user) {
            return $this->error(400,'User not found');
        }
        $user->notify(new OTPEmailVerificationNotification());
        return $this->success(null,'Verification code sent successfully');
    }

    /**
     * Perform auto-login after successful registration with enhanced security
     * This includes secure token generation, cart merging, and user data updates
     */
    protected function performAutoLogin(User $user, Request $request, $additionalData = null, $userType = 'customer')
    {
        try {
            // Generate secure authentication token with enhanced security
            $tokenData = $this->generateSecureAuthToken($user, $request);

            // Create secure JWT cookie for web compatibility
            $cookie = $this->createSecureCookie($tokenData['token']);

            // Perform cart merge if temp_user_id is provided
            if ($request->has('temp_user_id') && !empty($request->temp_user_id)) {
                $this->mergeGuestCart($user->id, $request->temp_user_id, $request);
            }

            // Update user data that normally happens after login
            $this->updateUserDataAfterLogin($user, $request);

            // Log successful auto-login for security monitoring
            $this->logSecureAutoLogin($user, $request, $tokenData);

            // Prepare response data based on user type
            $responseData = $this->prepareAutoLoginResponse($user, $tokenData['token'], $additionalData, $userType);

            // Add security metadata to response
            $responseData['security'] = [
                'token_expires_at' => $tokenData['expires_at'],
                'login_timestamp' => now()->toISOString(),
                'requires_password_change' => false // New users don't need immediate password change
            ];

            return $responseData;

        } catch (Exception $e) {
            Log::error('Auto-login failed after registration: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

            // Fallback to basic auth data without cart merge
            return $this->generateAuthData($user, $additionalData, $userType);
        }
    }

    /**
     * Generate secure authentication token with enhanced security measures
     */
    protected function generateSecureAuthToken(User $user, Request $request)
    {
        try {
            // Generate cryptographically secure token name
            $tokenName = 'auth_' . bin2hex(random_bytes(8)) . '_' . time();

            // Create token with secure abilities and expiration
            $token = $user->createToken($tokenName, ['*'], now()->addDays(30));

            // Store additional security metadata
            $tokenModel = $token->accessToken;
            $tokenModel->update([
                'last_used_at' => now(),
                'created_by_ip' => $request->ip(),
                'created_by_user_agent' => $request->userAgent()
            ]);

            return [
                'token' => $token->plainTextToken,
                'token_id' => $tokenModel->id,
                'expires_at' => $tokenModel->expires_at,
                'created_at' => $tokenModel->created_at
            ];

        } catch (Exception $e) {
            Log::error('Secure token generation failed: ' . $e->getMessage());

            // Fallback to basic token generation
            $token = $user->createToken('auth_token_fallback');
            return [
                'token' => $token->plainTextToken,
                'token_id' => $token->accessToken->id,
                'expires_at' => $token->accessToken->expires_at,
                'created_at' => $token->accessToken->created_at
            ];
        }
    }

    /**
     * Create secure cookie with proper security flags
     */
    protected function createSecureCookie($token)
    {
        return cookie(
            'auth_token',
            $token,
            60 * 24 * 30, // 30 days
            '/', // path
            null, // domain
            true, // secure (HTTPS only)
            true, // httpOnly
            false, // raw
            'Strict' // sameSite
        );
    }

    /**
     * Log secure auto-login event for security monitoring
     */
    protected function logSecureAutoLogin(User $user, Request $request, $tokenData)
    {
        Log::info('Secure auto-login completed after registration', [
            'user_id' => $user->id,
            'email' => $user->email, // Hash email for privacy
            'user_type' => $user->user_type,
            'token_id' => $tokenData['token_id'],
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'timestamp' => now()->toISOString(),
            'security_flags' => [
                'email_verified' => !is_null($user->email_verified_at),
                'secure_token' => true,
                'auto_login' => true
            ]
        ]);
    }

    /**
     * Merge guest cart with user cart after registration
     */
    protected function mergeGuestCart($userId, $tempUserId, Request $request)
    {
        try {
            $cartService = app(EnhancedCartService::class);
            $result = $cartService->mergeCartsAfterLogin($userId, $tempUserId, $request);

            Log::info('Cart merge completed after registration', [
                'user_id' => $userId,
                'temp_user_id' => $tempUserId,
                'result' => $result
            ]);

        } catch (Exception $e) {
            Log::error('Cart merge failed after registration: ' . $e->getMessage(), [
                'user_id' => $userId,
                'temp_user_id' => $tempUserId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Update user data that normally happens after login
     */
    protected function updateUserDataAfterLogin(User $user, Request $request)
    {
        try {
            // Update last login timestamp if the field exists
            if (method_exists($user, 'updateLastLogin')) {
                $user->updateLastLogin();
            }

            // Update device token if provided
            if ($request->has('device_token') && !empty($request->device_token)) {
                $user->device_token = $request->device_token;
                $user->save();
            }

            // Log successful registration and auto-login
            Log::info('User auto-login completed after registration', [
                'user_id' => $user->id,
                'email' => $user->email,
                'user_type' => $user->user_type,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent()
            ]);

        } catch (Exception $e) {
            Log::error('Failed to update user data after auto-login: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Prepare auto-login response data based on user type
     */
    protected function prepareAutoLoginResponse(User $user, $token, $additionalData = null, $userType = 'customer')
    {
        $baseResponse = [
            'user' => new UserResource($user),
            'token' => [
                'access_token' => $token,
                'token_type' => 'Bearer',
                'expires_in' => 60 * 24 * 30 * 60 // 30 days in seconds
            ]
        ];

        // Add additional data based on user type
        if ($userType === 'seller' && $additionalData) {
            $baseResponse['seller'] = new SellerProfileResource($additionalData);
        } elseif ($userType === 'b2b' && $additionalData) {
            $baseResponse['dropshipper'] = new DropshipperProfileResource($additionalData);
        }

        return $baseResponse;
    }
}
