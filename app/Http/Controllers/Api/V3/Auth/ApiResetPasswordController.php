<?php

namespace App\Http\Controllers\Api\V3\Auth;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\User\UserResource;
use App\Models\EmailVerify;
use App\Models\User;
use App\Notifications\RegistrationOTPNotification;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Validator;

class ApiResetPasswordController extends ApiResponse
{
    /**
     * Request password reset - sends OTP to user's email
     */
    public function forgetRequest(Request $request)
    {
        $messages = array(
            'email.required' => 'Please enter your email address.',
            'email.email' => 'The email address must be in a valid format.',
            'email.exists' => 'We couldn\'t find an account with that email address.',
        );
        
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|exists:users,email',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error(
                'Validation Error',
                'Please provide valid credentials',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            $email = $request->email;
            $user = User::where('email', $email)->first();

            // Check 5-minute resend cooldown for password reset
            $existingRecord = EmailVerify::where('email', $email)
                ->where('is_verify', false)
                ->first();

            if ($existingRecord && $existingRecord->isInCooldownPeriod()) {
                $remainingCooldown = $existingRecord->getRemainingCooldownMinutes();
                return $this->error(
                    'Resend cooldown active',
                    "Please wait {$remainingCooldown} minutes before requesting a new password reset code.",
                    429
                );
            }

            // Check resend limit (max 5 attempts per email)
            if ($existingRecord && !$existingRecord->canResend()) {
                return $this->error(
                    'Resend limit exceeded',
                    'Maximum password reset attempts reached for this email. Please try again after 1 hour.',
                    429
                );
            }

            // Generate and send OTP using the new email verification system
            $emailVerifyRecord = EmailVerify::generateOTP($email, 10); // 10 minutes expiry for password reset
            
            // Send notification using the registration OTP notification (same format)
            Notification::route('mail', $email)
                ->notify(new RegistrationOTPNotification($emailVerifyRecord));

            return $this->success([
                'email' => $email,
                'otp_sent' => true,
                'expires_at' => $emailVerifyRecord->expire_time->toISOString(),
                'expires_in_minutes' => 10,
                'resend_count' => $emailVerifyRecord->resend_count,
                'max_resend_attempts' => 5,
                'next_resend_available_at' => $emailVerifyRecord->getNextResendTime()->toISOString()
            ], 'Password reset code sent successfully to your email', 200);

        } catch (\Exception $exception) {
            Log::channel('api_auth')->error('Error in password reset request: ' . $exception->getMessage());
            
            return $this->error(
                'Failed to send reset code',
                'Something went wrong while sending the password reset code. Please try again later.',
                500
            );
        }
    }

    /**
     * Reset password with OTP verification
     */
    public function confirmReset(Request $request)
    {
        $messages = array(
            'email.required' => 'Please enter your email address.',
            'email.email' => 'The email address must be in a valid format.',
            'email.exists' => 'We couldn\'t find an account with that email address.',
            'otp.required' => 'Verification code is required.',
            'otp.size' => 'Verification code must be 6 digits.',
            'password.required' => 'New password is required.',
            'password.min' => 'Password must be at least 6 characters.',
            'password.confirmed' => 'Password confirmation does not match.',
        );
        
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|exists:users,email',
            'otp' => 'required|string|size:6',
            'password' => 'required|string|min:6|confirmed',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error(
                'Validation Error',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            $email = $request->email;
            $otp = $request->otp;
            $newPassword = $request->password;

            // Find the OTP record
            $emailVerifyRecord = EmailVerify::where('email', $email)
                ->where('otp', $otp)
                ->where('is_verify', false)
                ->first();

            if (!$emailVerifyRecord) {
                return $this->error(
                    'Invalid verification code',
                    'The verification code is incorrect. Please check and try again.',
                    400
                );
            }

            // Check if OTP is expired
            if ($emailVerifyRecord->isExpired()) {
                return $this->error(
                    'Verification code expired',
                    'The verification code has expired. Please request a new one.',
                    400
                );
            }

            // Verify the OTP
            $verified = EmailVerify::verifyOTP($email, $otp);

            if (!$verified) {
                return $this->error(
                    'Verification failed',
                    'Failed to verify the verification code. Please try again.',
                    400
                );
            }

            // Update user password
            $user = User::where('email', $email)->first();
            $user->password = Hash::make($newPassword);
            $user->save();

            // Clean up - remove the verified OTP record
            EmailVerify::where('email', $email)->where('is_verify', true)->delete();

            return $this->success([
                'user' => new UserResource($user),
                'message' => 'Password reset successfully. You can now login with your new password.'
            ], 'Password reset successfully', 200);

        } catch (\Exception $exception) {
            Log::channel('api_auth')->error('Error in password reset confirmation: ' . $exception->getMessage());
            
            return $this->error(
                'Password reset failed',
                'Something went wrong while resetting your password. Please try again later.',
                500
            );
        }
    }

    /**
     * Resend password reset OTP
     */
    public function resendResetOTP(Request $request)
    {
        $messages = array(
            'email.required' => 'Please enter your email address.',
            'email.email' => 'The email address must be in a valid format.',
            'email.exists' => 'We couldn\'t find an account with that email address.',
        );
        
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|exists:users,email',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error(
                'Validation Error',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            $email = $request->email;

            // Get existing record
            $existingRecord = EmailVerify::where('email', $email)
                ->where('is_verify', false)
                ->first();

            if (!$existingRecord) {
                return $this->error(
                    'No pending reset request',
                    'No pending password reset found. Please start the password reset process first.',
                    404
                );
            }

            // Check 5-minute resend cooldown
            if ($existingRecord->isInCooldownPeriod()) {
                $remainingCooldown = $existingRecord->getRemainingCooldownMinutes();
                return $this->error(
                    'Resend cooldown active',
                    "Please wait {$remainingCooldown} minutes before requesting a new password reset code.",
                    429
                );
            }

            // Check resend limit (max 5 attempts)
            if (!$existingRecord->canResend()) {
                return $this->error(
                    'Resend limit exceeded',
                    'Maximum password reset attempts reached for this email. Please try again after 1 hour.',
                    429
                );
            }

            // Generate and send new OTP
            $emailVerifyRecord = EmailVerify::generateOTP($email, 10); // 10 minutes expiry

            // Send notification
            Notification::route('mail', $email)
                ->notify(new RegistrationOTPNotification($emailVerifyRecord));

            return $this->success([
                'email' => $email,
                'otp_sent' => true,
                'expires_at' => $emailVerifyRecord->expire_time->toISOString(),
                'expires_in_minutes' => 10,
                'resend_count' => $emailVerifyRecord->resend_count,
                'max_resend_attempts' => 5,
                'next_resend_available_at' => $emailVerifyRecord->getNextResendTime()->toISOString()
            ], 'Password reset code resent successfully', 200);

        } catch (\Exception $exception) {
            Log::channel('api_auth')->error('Error in password reset OTP resend: ' . $exception->getMessage());
            
            return $this->error(
                'Failed to resend reset code',
                'Something went wrong while resending the password reset code. Please try again later.',
                500
            );
        }
    }
}