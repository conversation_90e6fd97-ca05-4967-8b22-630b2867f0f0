<?php

namespace App\Http\Controllers\Api\V3\Upload;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use App\Utility\ApiAizUploadUtility;
use App\Models\Upload;
use Illuminate\Support\Str;

class ApiAvatarUploadController extends Controller
{
    /**
     * Test endpoint to verify routing is working
     */
    public function test()
    {
        return response()->json([
            'success' => true,
            'message' => 'Avatar upload controller is working!',
            'timestamp' => now()->toDateTimeString(),
            'php_version' => phpversion(),
        ]);
    }

    /**
     * Upload avatar using AizUploadUtility
     * Supports both base64 and file uploads
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadAvatar(Request $request)
    {
        try {
            \Log::info('Starting avatar upload process', [
                'has_avatar_field' => $request->has('avatar'),
                'is_string' => $request->has('avatar') ? is_string($request->avatar) : false,
                'is_file' => $request->hasFile('avatar'),
            ]);

            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated',
                ], 401);
            }

            \Log::info('User authenticated', ['user_id' => $user->id]);

            // Initialize AizUploadUtility
            $uploadUtility = new ApiAizUploadUtility();
            $uploadResult = null;

            // Check if it's a base64 upload
            if ($request->has('avatar') && is_string($request->avatar)) {
                \Log::info('Processing base64 upload');
                
                // Validate base64 image
                if (!preg_match('/^data:image\/(\w+);base64,/', $request->avatar, $matches)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid base64 image format',
                    ], 400);
                }

                $imageType = strtolower($matches[1]);
                $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                
                if (!in_array($imageType, $allowedTypes)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid image type. Allowed types: jpg, jpeg, png, gif, webp',
                    ], 400);
                }

                try {
                    // Extract and validate base64 data
                    $base64Data = substr($request->avatar, strpos($request->avatar, ',') + 1);
                    $decodedData = base64_decode($base64Data);

                    if ($decodedData === false) {
                        throw new \Exception('Invalid base64 data');
                    }

                    // Check file size (max 5MB)
                    if (strlen($decodedData) > 5 * 1024 * 1024) {
                        throw new \Exception('Image size must not exceed 5MB');
                    }

                    // Create temporary file from base64
                    $tempFile = tempnam(sys_get_temp_dir(), 'avatar_');
                    $tempFilename = $tempFile . '.' . $imageType;
                    rename($tempFile, $tempFilename);
                    
                    if (file_put_contents($tempFilename, $decodedData) === false) {
                        throw new \Exception('Failed to write temporary file');
                    }

                    \Log::info('Created temporary file', ['temp_file' => $tempFilename]);

                    // Create UploadedFile instance
                    $uploadedFile = new \Illuminate\Http\UploadedFile(
                        $tempFilename,
                        'avatar.' . $imageType,
                        mime_content_type($tempFilename),
                        null,
                        true
                    );

                    // Create a new request with the correct file field name
                    $newRequest = new Request();
                    $newRequest->files->set('aiz_file', $uploadedFile);
                    
                    // Upload using AizUploadUtility
                    $uploadResult = $uploadUtility->upload($newRequest, $user->id);
                    \Log::info('Upload result from base64', ['result' => $uploadResult]);
                    
                    // Clean up temp file
                    if (file_exists($tempFilename)) {
                        unlink($tempFilename);
                    }

                    if (!$uploadResult) {
                        throw new \Exception('Upload failed - no result returned');
                    }
                } catch (\Exception $e) {
                    \Log::error('Base64 upload processing failed', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    throw $e;
                }
            }
            // Check if it's a file upload
            elseif ($request->hasFile('avatar')) {
                \Log::info('Processing file upload');
                
                $validator = Validator::make($request->all(), [
                    'avatar' => 'required|file|image|mimes:jpg,jpeg,png,gif,webp|max:5120',
                ]);

                if ($validator->fails()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Please provide valid information',
                        'errors' => $validator->errors()->messages(),
                    ], 400);
                }

                try {
                    // Create a new request with the correct file field name
                    $newRequest = new Request();
                    $newRequest->files->set('aiz_file', $request->file('avatar'));
                    
                    $uploadResult = $uploadUtility->upload($newRequest, $user->id);
                    \Log::info('Upload result from file', ['result' => $uploadResult]);

                    if (!$uploadResult) {
                        throw new \Exception('Upload failed - no result returned');
                    }
                } catch (\Exception $e) {
                    \Log::error('File upload processing failed', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    throw $e;
                }
            }
            else {
                return response()->json([
                    'success' => false,
                    'message' => 'No valid avatar provided. Please provide either a base64 image or file upload.',
                ], 400);
            }

            // Process upload result
            if ($uploadResult && isset($uploadResult['file_path'])) {
                try {
                    // Delete old avatar if exists
                    if ($user->avatar_original) {
                        try {
                            // Get the old upload record
                            $oldUpload = Upload::find($user->avatar_original);
                            if ($oldUpload) {
                                // Delete the physical file
                                if ($oldUpload->file_name && Storage::disk('public')->exists($oldUpload->file_name)) {
                                    Storage::disk('public')->delete($oldUpload->file_name);
                                }
                                // Delete the database record
                                $oldUpload->delete();
                            }
                        } catch (\Exception $e) {
                            \Log::warning('Failed to delete old avatar:', ['error' => $e->getMessage()]);
                        }
                    }
                    // Delete old avatar if exists
                    if ($user->avatar) {
                        try {
                            // Get the old upload record
                            $oldUpload = Upload::find($user->avatar);
                            if ($oldUpload) {
                                // Delete the physical file
                                if ($oldUpload->file_name && Storage::disk('public')->exists($oldUpload->file_name)) {
                                    Storage::disk('public')->delete($oldUpload->file_name);
                                }
                                // Delete the database record
                                $oldUpload->delete();
                            }
                        } catch (\Exception $e) {
                            \Log::warning('Failed to delete old avatar:', ['error' => $e->getMessage()]);
                        }
                    }
                    // Update user avatar
                    $user->avatar_original = $uploadResult['file_path'];
                    $user->avatar = $uploadResult['file_path'];
                    $user->save();

                    \Log::info('Avatar updated successfully', [
                        'user_id' => $user->id,
                        'file_path' => $uploadResult['file_path']
                    ]);

                    return response()->json([
                        'success' => true,
                        'message' => 'Avatar updated successfully',
                        'data' => [
                            'avatar_url' => uploaded_asset($uploadResult['file_path']),
                            'user' => [
                                'id' => $user->id,
                                'name' => $user->name,
                                'email' => $user->email,
                                'avatar' => uploaded_asset($uploadResult['file_path']),
                                'avatar_original' => uploaded_asset($uploadResult['file_path']),
                            ]
                        ]
                    ]);
                } catch (\Exception $e) {
                    \Log::error('Failed to update user avatar', [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                        'upload_result' => $uploadResult
                    ]);
                    throw $e;
                }
            }

            \Log::error('Upload completed but no valid result', ['upload_result' => $uploadResult]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload avatar - no valid result returned',
            ], 400);

        } catch (\Exception $e) {
            \Log::error('Avatar upload failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => [
                    'has_avatar' => $request->has('avatar'),
                    'is_file' => $request->hasFile('avatar')
                ]
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload avatar: ' . $e->getMessage(),
                'debug' => [
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]
            ], 500);
        }
    }
    
    /**
     * Remove avatar
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function removeAvatar(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated',
                ], 401);
            }
            
            // Delete avatar file if exists
            if ($user->avatar_original) {
                $oldPath = str_replace(asset('storage/'), '', $user->avatar_original);
                try {
                    Storage::disk('public')->delete($oldPath);
                } catch (\Exception $e) {
                    // Continue even if deletion fails
                }
            }
            
            // Remove avatar from user record
            $user->avatar_original = null;
            $user->save();
            
            return response()->json([
                'success' => true,
                'message' => 'Avatar removed successfully',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'avatar_original' => null,
                    ]
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to remove avatar: ' . $e->getMessage(),
            ], 500);
        }
    }
    
    /**
     * Get current avatar
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAvatar(Request $request)
    {
        try {
            $user = Auth::user();
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not authenticated',
                ], 401);
            }
            
            return response()->json([
                'success' => true,
                'message' => 'Avatar retrieved successfully',
                'data' => [
                    'avatar_url' => $user->avatar_original,
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'avatar_original' => $user->avatar_original,
                    ]
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve avatar: ' . $e->getMessage(),
            ], 500);
        }
    }
} 