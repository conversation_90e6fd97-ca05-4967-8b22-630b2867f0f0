<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderCancel;
use App\Services\ActivityLogService;
use App\Notifications\order\OrderCancelRequestSendNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class ApiCancelRequestController extends Controller
{
    protected $activityLogService;

    public function __construct(ActivityLogService $activityLogService)
    {
        $this->activityLogService = $activityLogService;
    }

    /**
     * Get list of cancel requests with pagination and filters
     */
    public function index(Request $request)
    {
        try {
            $query = OrderCancel::with(['order', 'user'])
                ->when($request->status, function ($q) use ($request) {
                    return $q->where('status', $request->status);
                })
                ->when($request->orderNumber, function ($q) use ($request) {
                    return $q->whereHas('order', function ($q) use ($request) {
                        $q->where('code', 'like', '%' . $request->orderNumber . '%');
                    });
                })
                ->when($request->dateFrom, function ($q) use ($request) {
                    return $q->whereDate('created_at', '>=', $request->dateFrom);
                })
                ->when($request->dateTo, function ($q) use ($request) {
                    return $q->whereDate('created_at', '<=', $request->dateTo);
                });

            // Apply sorting
            $sortBy = $request->sortBy ?? 'created_at';
            $sortDirection = $request->sortDirection ?? 'desc';
            $query->orderBy($sortBy, $sortDirection);

            $cancelRequests = $query->paginate($request->perPage ?? 10);

            return $this->success($cancelRequests);
        } catch (\Exception $e) {
            Log::error('Error in ApiCancelRequestController@index: ' . $e->getMessage());
            return $this->error('SERVER_ERROR', 'Failed to fetch cancel requests', $e->getMessage(), 500);
        }
    }

    /**
     * Get details of a specific cancel request
     */
    public function show($id)
    {
        try {
            $cancelRequest = OrderCancel::with(['order', 'user'])
                ->findOrFail($id);

            return $this->success($cancelRequest);
        } catch (\Exception $e) {
            Log::error('Error in ApiCancelRequestController@show: ' . $e->getMessage());
            return $this->error('SERVER_ERROR', 'Failed to fetch cancel request details', $e->getMessage(), 500);
        }
    }

    /**
     * Process a cancel request (approve/reject)
     */
    public function process(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'status' => 'required|in:approved,rejected,processing',
                'processingNotes' => 'nullable|string|max:500',
                'refundAmount' => 'nullable|numeric|min:0',
                'refundMethod' => 'nullable|string|max:50'
            ]);

            if ($validator->fails()) {
                return $this->error('VALIDATION_ERROR', 'Validation failed', $validator->errors(), 422);
            }

            $cancelRequest = OrderCancel::findOrFail($id);
            $order = $cancelRequest->order;

            // Check if order is still in pending status
            if ($order->delivery_status !== 'pending') {
                return $this->error(
                    'INVALID_ORDER_STATUS',
                    'Cannot process cancellation request. Order is no longer in pending status.',
                    null,
                    400
                );
            }

            // Update cancel request
            $cancelRequest->status = $request->status === 'approved' ? 1 : ($request->status === 'rejected' ? 2 : 0);
            $cancelRequest->processing_notes = $request->processingNotes;
            $cancelRequest->refund_amount = $request->refundAmount;
            $cancelRequest->refund_method = $request->refundMethod;
            $cancelRequest->processed_by = Auth::id();
            $cancelRequest->processed_date = Carbon::now();
            $cancelRequest->save();

            // Update order status if approved
            if ($request->status === 'approved') {
                $order->delivery_status = 'cancelled';
                $order->save();

                // Log activity
                $this->activityLogService->log(
                    'order_status_changed',
                    'Order Cancelled',
                    $order->id,
                    Order::class,
                    Auth::id(),
                    get_class(Auth::user()),
                    $order->delivery_status,
                    'cancelled',
                    null,
                    null
                );
            }

            return $this->success($cancelRequest, 'Cancel request processed successfully');
        } catch (\Exception $e) {
            Log::error('Error in ApiCancelRequestController@process: ' . $e->getMessage());
            return $this->error('SERVER_ERROR', 'Failed to process cancel request', $e->getMessage(), 500);
        }
    }

    /**
     * Get cancel request statistics
     */
    public function getStats(Request $request)
    {
        try {
            $query = OrderCancel::query()
                ->whereHas('order', function ($q) {
                    $q->where('delivery_status', 'pending');
                })
                ->when($request->dateFrom, function ($q) use ($request) {
                    return $q->whereDate('created_at', '>=', $request->dateFrom);
                })
                ->when($request->dateTo, function ($q) use ($request) {
                    return $q->whereDate('created_at', '<=', $request->dateTo);
                });

            $stats = [
                'total' => $query->count(),
                'pending' => $query->where('status', 0)->count(),
                'approved' => $query->where('status', 1)->count(),
                'rejected' => $query->where('status', 2)->count(),
                'processing' => $query->where('status', 3)->count(),
                'averageProcessingTime' => $this->calculateAverageProcessingTime($query),
                'totalRefundAmount' => $query->where('status', 1)->sum('refund_amount')
            ];

            return $this->success($stats);
        } catch (\Exception $e) {
            Log::error('Error in ApiCancelRequestController@getStats: ' . $e->getMessage());
            return $this->error('SERVER_ERROR', 'Failed to fetch cancel request statistics', $e->getMessage(), 500);
        }
    }

    /**
     * Calculate average processing time in hours
     */
    private function calculateAverageProcessingTime($query)
    {
        $processedRequests = $query->whereNotNull('processed_date')->get();
        if ($processedRequests->isEmpty()) {
            return 0;
        }

        $totalHours = $processedRequests->sum(function ($request) {
            return Carbon::parse($request->created_at)
                ->diffInHours(Carbon::parse($request->processed_date));
        });

        return round($totalHours / $processedRequests->count(), 2);
    }

    /**
     * Store a new cancel request
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'orderId' => 'required|string|exists:orders,code',
                'reason' => 'required|string|in:changed_mind,found_better_price,ordered_by_mistake,taking_too_long,wrong_item,other',
                'comments' => 'nullable|string|max:500',
                'evidence' => 'nullable|array',
                'evidence.*' => 'file|mimes:jpeg,png,jpg,gif,mp4,mov,avi|max:10240' // max 10MB
            ]);

            if ($validator->fails()) {
                return $this->error('VALIDATION_ERROR', 'Validation failed', $validator->errors(), 422);
            }

            // Get the authenticated user
            $user = auth()->user();
            if (!$user) {
                return $this->error('UNAUTHORIZED', 'Authentication required', null, 401);
            }

            // Get the order
            $order = Order::where('code', $request->orderId)
                ->where('user_id', $user->id)
                ->first();

            if (!$order) {
                return $this->error('NOT_FOUND', 'Order not found or does not belong to the authenticated user', null, 404);
            }

            // Check if order is in pending status
            if ($order->delivery_status !== 'pending') {
                return $this->error(
                    'INVALID_ORDER_STATUS',
                    'Only pending orders can be cancelled',
                    null,
                    400
                );
            }

            // Check if there's already a cancel request for this order
            $existingCancelRequest = OrderCancel::where('order_id', $order->id)->first();
            if ($existingCancelRequest) {
                return $this->error(
                    'DUPLICATE_REQUEST',
                    'A cancellation request already exists for this order',
                    null,
                    400
                );
            }

            // Create the cancel request
            $cancelRequest = new OrderCancel();
            $cancelRequest->user_id = $user->id;
            $cancelRequest->order_id = $order->id;
            $cancelRequest->seller_id = $order->seller_id;
            $cancelRequest->seller_approval = 0;
            $cancelRequest->admin_approval = 0;
            $cancelRequest->cancel_amount = $order->grand_total;
            $cancelRequest->reason = $request->reason;
            $cancelRequest->cancel_comments = $request->comments;
            $cancelRequest->admin_seen = 0;
            $cancelRequest->status = 0; // pending
            $cancelRequest->save();

            // Handle evidence files
            if ($request->hasFile('evidence')) {
                $evidenceFiles = [];
                foreach ($request->file('evidence') as $file) {
                    $fileName = time() . '_' . $file->getClientOriginalName();
                    $filePath = $file->storeAs('public/cancel-requests/' . $cancelRequest->id, $fileName);
                    
                    $evidenceFiles[] = [
                        'file_name' => $fileName,
                        'file_path' => $filePath,
                        'file_type' => $file->getClientMimeType(),
                        'file_size' => $file->getSize(),
                        'created_at' => now()
                    ];
                }
                
                // Store evidence files in the database
                $cancelRequest->evidence_files = json_encode($evidenceFiles);
                $cancelRequest->save();
            }

            // Log the activity
            $this->activityLogService->log(
                'order_status_changed',
                'Customer Cancelled Order Request',
                $order->id,
                Order::class,
                $user->id,
                get_class($user),
                $order->delivery_status,
                'cancelled',
                null,
                null
            );

            // Send notification to customer
            try {
                $array = [
                    'order_code' => $order->code,
                    'user_name' => $order->user->name,
                    'subject' => translate('Confirmation of Your Order Cancellation Request ') . ' - ' . $order->code
                ];
                $order->user->notify(new OrderCancelRequestSendNotification($array));
                
                // Process email queue
                $command = 'php ' . base_path() . '/artisan queue:work --verbose --tries=1 --timeout=220 > /dev/null &';
                exec($command);
            } catch (\Exception $e) {
                Log::channel('email_logs')->error('Error sending OrderCancelRequestSendNotification email: ' . $e->getMessage());
            }

            // Prepare order items for response
            $orderItems = $order->orderDetails->map(function ($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->product->name,
                    'quantity' => $item->quantity,
                    'price' => $item->price
                ];
            });

            return $this->success([
                'data' => [
                    'orderId' => $order->code,
                    'orderNumber' => $order->code,
                    'date' => $order->created_at->format('Y-m-d'),
                    'formattedDate' => $order->created_at->format('F j, Y'),
                    'items' => $orderItems,
                    'subtotal' => $order->orderDetails->sum('price'),
                    'shipping' => $order->shipping_cost,
                    'tax' => $order->tax,
                    'discount' => $order->discount > 0 ? [
                        'amount' => $order->discount
                    ] : null,
                    'total' => $order->grand_total,
                    'cancellationStatus' => 'pending',
                    'estimatedRefundAmount' => $order->grand_total,
                    'estimatedRefundDate' => now()->addDays(3)->toDateString(),
                    'evidenceFiles' => $evidenceFiles ?? []
                ],
                'message' => 'Cancellation request submitted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Error in ApiCancelRequestController@store: ' . $e->getMessage());
            return $this->error('SERVER_ERROR', 'Failed to create cancel request', $e->getMessage(), 500);
        }
    }
} 