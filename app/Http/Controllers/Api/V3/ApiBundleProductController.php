<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Services\ApiProductService;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Http\Resources\ProductsResource;
use Illuminate\Support\Str;

class ApiBundleProductController extends Controller
{
    protected $productService;

    /**
     * Create a new controller instance.
     *
     * @param ApiProductService $productService
     */
    public function __construct(ApiProductService $productService)
    {
        $this->productService = $productService;
    }

    /**
     * Get all bundle products with filtering and pagination
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $params = $request->only([
                'category', 'search', 'sortBy', 'sortOrder', 'minSavings', 'page', 'limit'
            ]);

            $bundleProducts = $this->productService->getBundleProducts($params);

            return $this->success(
                new ProductsResource($bundleProducts)
            );
        } catch (\Exception $e) {
            return $this->error(
                'Failed to fetch bundle products',
                $e->getMessage()
            );
        }
    }

    /**
     * Get popular bundle products
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getPopularBundleProducts(Request $request): JsonResponse
    {
        try {
            $params = $request->only([
                'category', 'search', 'page', 'limit'
            ]);

            $bundleProducts = $this->productService->getPopularBundleProducts($params);

            return $this->success(
                new ProductsResource($bundleProducts)
            );
        } catch (\Exception $e) {
            return $this->error(
                'Failed to fetch popular bundle products',
                $e->getMessage()
            );
        }
    }

    /**
     * Get bundle products by category
     *
     * @param Request $request
     * @param string $categorySlug
     * @return JsonResponse
     */
    public function getBundleProductsByCategory(string $categorySlug, Request $request): JsonResponse
    {
        try {
            $params = $request->only([
                'search', 'sortBy', 'sortOrder', 'page', 'limit'
            ]);

            $bundleProducts = $this->productService->getBundleProductsByCategory($categorySlug, $params);

            return $this->success(
                new ProductsResource($bundleProducts)
            );
        } catch (\Exception $e) {
            return $this->error(
                'Failed to fetch bundle products for category',
                $e->getMessage()
            );
        }
    }

    /**
     * Get bundle product details
     *
     * @param string $idOrSlug
     * @return JsonResponse
     */
    public function getBundleProductDetails(string $idOrSlug): JsonResponse
    {
        try {
            $bundleProduct = Product::where(function ($query) use ($idOrSlug) {
                    $query->where('id', $idOrSlug)
                        ->orWhere('slug', $idOrSlug);
                })
                ->where('is_bundle', true)
                ->where('published', 1)
                ->where('approved', 1)
                ->first();

            if (!$bundleProduct) {
                return $this->error(
                    'Bundle product not found',
                    'No bundle product found with the provided ID or slug',
                    404
                );
            }

            // Increment view count
            $bundleProduct->increment('view_count');

            // Add bundle items details
            $bundleProduct->append('bundle_items_with_details');

            return $this->success([
                'data' => $bundleProduct,
            ]);
        } catch (\Exception $e) {
            return $this->error(
                'Failed to fetch bundle product details',
                $e->getMessage()
            );
        }
    }

    /**
     * Create a new bundle product
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'description' => 'nullable|string',
                'thumbnail' => 'nullable|string',
                'unit_price' => 'required|numeric|min:0',
                'category_id' => 'nullable|exists:categories,id',
                'bundle_items' => 'required|array|min:1',
                'bundle_items.*.product_id' => 'required|exists:products,id',
                'bundle_items.*.quantity' => 'nullable|integer|min:1',
                'bundle_items.*.price' => 'required|numeric|min:0',
                'tags' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return $this->validationError($validator);
            }

            // Create the bundle product
            $slug = Str::slug($request->name);
            $uniqueSlug = $this->generateUniqueSlug($slug);
            
            $bundleProduct = new Product();
            $bundleProduct->name = $request->name;
            $bundleProduct->slug = $uniqueSlug;
            $bundleProduct->description = $request->description;
            $bundleProduct->thumbnail = $request->thumbnail;
            $bundleProduct->unit_price = $request->unit_price;
            $bundleProduct->category_id = $request->category_id;
            $bundleProduct->tags = $request->tags;
            $bundleProduct->is_bundle = true;
            $bundleProduct->bundle_items = $request->bundle_items;
            $bundleProduct->published = 1;
            $bundleProduct->approved = 1;
            $bundleProduct->save();
            
            // Calculate and update savings
            $this->productService->calculateBundleFields($bundleProduct, $request->bundle_items);
            
            // Refresh the product with updated fields
            $bundleProduct->refresh();
            
            // Add bundle items details for response
            $bundleProduct->append('bundle_items_with_details');

            return $this->success(
                ['data' => $bundleProduct],
                'Bundle product created successfully',
                201
            );
        } catch (\Exception $e) {
            return $this->error(
                'Failed to create bundle product',
                $e->getMessage()
            );
        }
    }

    /**
     * Update a bundle product
     *
     * @param Request $request
     * @param string $id
     * @return JsonResponse
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $bundleProduct = Product::where('id', $id)
                ->where('is_bundle', true)
                ->first();

            if (!$bundleProduct) {
                return $this->error(
                    'Bundle product not found',
                    'No bundle product found with the provided ID',
                    404
                );
            }

            $validator = Validator::make($request->all(), [
                'name' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'thumbnail' => 'nullable|string',
                'unit_price' => 'nullable|numeric|min:0',
                'category_id' => 'nullable|exists:categories,id',
                'bundle_items' => 'nullable|array',
                'bundle_items.*.product_id' => 'required_with:bundle_items|exists:products,id',
                'bundle_items.*.quantity' => 'nullable|integer|min:1',
                'bundle_items.*.price' => 'required_with:bundle_items|numeric|min:0',
                'tags' => 'nullable|string',
                'published' => 'nullable|boolean',
            ]);

            if ($validator->fails()) {
                return $this->validationError($validator);
            }

            // Update name and slug if provided
            if ($request->has('name')) {
                $bundleProduct->name = $request->name;
                if ($bundleProduct->slug != Str::slug($request->name)) {
                    $bundleProduct->slug = $this->generateUniqueSlug(Str::slug($request->name));
                }
            }
            
            // Update other fields if provided
            if ($request->has('description')) {
                $bundleProduct->description = $request->description;
            }
            
            if ($request->has('thumbnail')) {
                $bundleProduct->thumbnail = $request->thumbnail;
            }
            
            if ($request->has('unit_price')) {
                $bundleProduct->unit_price = $request->unit_price;
            }
            
            if ($request->has('category_id')) {
                $bundleProduct->category_id = $request->category_id;
            }
            
            if ($request->has('tags')) {
                $bundleProduct->tags = $request->tags;
            }
            
            if ($request->has('published')) {
                $bundleProduct->published = $request->published ? 1 : 0;
            }
            
            // Update bundle items if provided
            if ($request->has('bundle_items')) {
                $bundleProduct->bundle_items = $request->bundle_items;
            }
            
            $bundleProduct->save();
            
            // Recalculate savings if price or bundle items changed
            if ($request->has('unit_price') || $request->has('bundle_items')) {
                $this->productService->calculateBundleFields(
                    $bundleProduct, 
                    $request->has('bundle_items') ? $request->bundle_items : $bundleProduct->bundle_items
                );
                
                // Refresh the product with updated fields
                $bundleProduct->refresh();
            }
            
            // Add bundle items details for response
            $bundleProduct->append('bundle_items_with_details');

            return $this->success(
                ['data' => $bundleProduct],
                'Bundle product updated successfully'
            );
        } catch (\Exception $e) {
            return $this->error(
                'Failed to update bundle product',
                $e->getMessage()
            );
        }
    }

    /**
     * Delete a bundle product
     *
     * @param string $id
     * @return JsonResponse
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $bundleProduct = Product::where('id', $id)
                ->where('is_bundle', true)
                ->first();

            if (!$bundleProduct) {
                return $this->error(
                    'Bundle product not found',
                    'No bundle product found with the provided ID',
                    404
                );
            }

            // Delete the bundle product
            $bundleProduct->delete();

            return $this->success(
                null,
                'Bundle product deleted successfully'
            );
        } catch (\Exception $e) {
            return $this->error(
                'Failed to delete bundle product',
                $e->getMessage()
            );
        }
    }

    /**
     * Generate a unique slug for a product
     *
     * @param string $slug
     * @return string
     */
    private function generateUniqueSlug(string $slug): string
    {
        $originalSlug = $slug;
        $count = 0;
        
        while (Product::where('slug', $slug)->exists()) {
            $count++;
            $slug = $originalSlug . '-' . $count;
        }
        
        return $slug;
    }
}
