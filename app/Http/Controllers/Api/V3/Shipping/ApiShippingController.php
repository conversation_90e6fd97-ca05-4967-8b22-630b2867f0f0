<?php

namespace App\Http\Controllers\Api\V3\Shipping;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\ShippingMethod;
use App\Models\ShippingZone;
use App\Models\ShippingTracking;
use Pusher\Pusher;

class ApiShippingController extends Controller
{
    /**
     * Get all available shipping methods
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getShippingMethods(Request $request)
    {
        // Filter by country/region if provided
        $countryId = $request->query('country_id');
        $methods = ShippingMethod::where('is_active', true)
            ->when($countryId, function ($query, $countryId) {
                return $query->whereHas('zones', function ($q) use ($countryId) {
                    $q->where('country_id', $countryId);
                });
            })
            ->get();
            
        return response()->json([
            'status' => true,
            'message' => 'Shipping methods retrieved successfully',
            'data' => $methods->map(function ($method) {
                return [
                    'id' => $method->id,
                    'name' => $method->name,
                    'description' => $method->description,
                    'base_cost' => (float) $method->base_cost,
                    'estimated_days' => $method->estimated_days,
                    'icon' => $method->icon ? asset('storage/' . $method->icon) : null,
                    'is_free_shipping_eligible' => (bool) $method->is_free_shipping_eligible,
                    'min_free_shipping_amount' => (float) $method->min_free_shipping_amount,
                ];
            })
        ]);
    }

    /**
     * Calculate shipping cost based on order details
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function calculateShippingCost(Request $request)
    {
        $request->validate([
            'shipping_method_id' => 'required|exists:shipping_methods,id',
            'country_id' => 'required|exists:countries,id',
            'state_id' => 'nullable|exists:states,id',
            'city' => 'nullable|string',
            'postal_code' => 'nullable|string',
            'weight' => 'required|numeric|min:0',
            'subtotal' => 'required|numeric|min:0',
            'items' => 'required|array',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
        ]);
        
        $shippingMethod = ShippingMethod::findOrFail($request->shipping_method_id);
        $weight = $request->weight;
        $subtotal = $request->subtotal;
        
        // Check if eligible for free shipping
        $freeShipping = false;
        if ($shippingMethod->is_free_shipping_eligible && $subtotal >= $shippingMethod->min_free_shipping_amount) {
            $freeShipping = true;
        }
        
        if ($freeShipping) {
            $cost = 0;
        } else {
            // Base cost
            $cost = $shippingMethod->base_cost;
            
            // Add weight-based cost
            if ($weight > $shippingMethod->free_weight_limit) {
                $extraWeight = $weight - $shippingMethod->free_weight_limit;
                $cost += ($extraWeight * $shippingMethod->cost_per_weight_unit);
            }
            
            // Check if there are zone-specific rates
            $zone = ShippingZone::where('shipping_method_id', $shippingMethod->id)
                ->where('country_id', $request->country_id)
                ->first();
                
            if ($zone) {
                // Apply zone multiplier
                $cost *= $zone->rate_multiplier;
                
                // Add zone-specific fee
                $cost += $zone->additional_fee;
            }
        }
        
        return response()->json([
            'status' => true,
            'message' => 'Shipping cost calculated successfully',
            'data' => [
                'shipping_cost' => round($cost, 2),
                'is_free_shipping' => $freeShipping,
                'estimated_delivery_days' => $shippingMethod->estimated_days,
                'shipping_method' => [
                    'id' => $shippingMethod->id,
                    'name' => $shippingMethod->name,
                    'description' => $shippingMethod->description,
                ]
            ]
        ]);
    }

    /**
     * Get all shipping zones
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getShippingZones()
    {
        $zones = ShippingZone::with(['country', 'shippingMethod'])->get();
        
        return response()->json([
            'status' => true,
            'message' => 'Shipping zones retrieved successfully',
            'data' => $zones
        ]);
    }

    /**
     * Get tracking information for a shipping
     *
     * @param Request $request
     * @param string $trackingNumber
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTrackingInfo(Request $request, $trackingNumber)
    {
        $tracking = ShippingTracking::where('tracking_number', $trackingNumber)->first();
        
        if (!$tracking) {
            return response()->json([
                'status' => false,
                'message' => 'Tracking information not found',
            ], 404);
        }
        
        // Get the related order if it exists
        $order = $tracking->order;
        
        // Prepare tracking data
        $trackingData = [
            'tracking_number' => $tracking->tracking_number,
            'carrier' => $tracking->carrier,
            'current_status' => $tracking->current_status,
            'estimated_delivery' => $tracking->estimated_delivery,
            'last_updated' => $tracking->updated_at->toIso8601String(),
            'origin' => $tracking->origin,
            'destination' => $tracking->destination,
        ];
        
        // Get tracking events
        $events = $tracking->events()->orderBy('timestamp', 'desc')->get();
        $trackingData['events'] = $events->map(function ($event) {
            return [
                'status' => $event->status,
                'location' => $event->location,
                'timestamp' => $event->timestamp->toIso8601String(),
                'message' => $event->message,
                'latitude' => $event->latitude,
                'longitude' => $event->longitude,
            ];
        });
        
        // Set up pusher channel for real-time updates if authenticated
        $pusherInfo = null;
        if (Auth::check() && $order && $order->user_id === Auth::id()) {
            $channelName = 'private-shipping-tracking-' . $trackingNumber;
            $eventName = 'tracking-update';
            
            $appId = config('broadcasting.connections.pusher.app_id');
            $appKey = config('broadcasting.connections.pusher.key');
            $appSecret = config('broadcasting.connections.pusher.secret');
            $options = [
                'cluster' => config('broadcasting.connections.pusher.options.cluster'),
                'useTLS' => true
            ];
            
            // Include Pusher channel information in the response
            $pusherInfo = [
                'channel' => $channelName,
                'event' => $eventName,
                'key' => $appKey,
                'cluster' => $options['cluster']
            ];
        }
        
        return response()->json([
            'status' => true,
            'message' => 'Tracking information retrieved successfully',
            'data' => [
                'tracking' => $trackingData,
                'pusher' => $pusherInfo
            ]
        ]);
    }
} 