<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\UserNotification;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;
use App\Enums\NotificationType;

class NotificationController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
        $this->middleware('auth:sanctum');
    }

    /**
     * Get user notifications with pagination
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $perPage = $request->input('per_page', 15);
            $type = $request->input('type');
            $read = $request->input('read');

            $query = UserNotification::where('user_id', $user->id)
                ->orderBy('created_at', 'desc');

            // Filter by type if provided
            if ($type) {
                $query->where('type', $type);
            }
            $isRead = $read === 'true' ? 1 : 0;
            // Filter by read status if provided
            if ($read !== null) {
                $query->where('read', $isRead);
            }

            $notifications = $query->paginate($perPage);

            // Add formatted data
            $notifications->getCollection()->transform(function ($notification) {
                $notification->time_ago = $notification->created_at->diffForHumans();
                $notification->formatted_date = $notification->created_at->format('M d, Y h:i A');
                return $notification;
            });

            return response()->json([
                'success' => true,
                'data' => $notifications,
                'unread_count' => UserNotification::where('user_id', $user->id)
                    ->where('read', false)
                    ->count()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch notifications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get unread notifications count
     *
     * @return JsonResponse
     */
    public function unreadCount(): JsonResponse
    {
        try {
            $user = Auth::user();
            $count = UserNotification::where('user_id', $user->id)
                ->where('read', false)
                ->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'unread_count' => $count
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch unread count',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get recent notifications (last 10)
     *
     * @return JsonResponse
     */
    public function recent(): JsonResponse
    {
        try {
            $user = Auth::user();
            $notifications = UserNotification::where('user_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            $notifications->transform(function ($notification) {
                $notification->time_ago = $notification->created_at->diffForHumans();
                $notification->formatted_date = $notification->created_at->format('M d, Y h:i A');
                return $notification;
            });

            return response()->json([
                'success' => true,
                'data' => $notifications
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch recent notifications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark notification as read
     *
     * @param int $id
     * @return JsonResponse
     */
    public function markAsRead($id): JsonResponse
    {
        try {
            $user = Auth::user();
            $notification = UserNotification::where('user_id', $user->id)
                ->where('id', $id)
                ->first();

            if (!$notification) {
                return response()->json([
                    'success' => false,
                    'message' => 'Notification not found'
                ], 404);
            }

            $notification->read = true;
            $notification->read_at = Carbon::now();
            $notification->save();

            return response()->json([
                'success' => true,
                'message' => 'Notification marked as read',
                'data' => $notification
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark notification as read',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark notification as unread
     *
     * @param int $id
     * @return JsonResponse
     */
    public function markAsUnread($id): JsonResponse
    {
        try {
            $user = Auth::user();
            $notification = UserNotification::where('user_id', $user->id)
                ->where('id', $id)
                ->first();

            if (!$notification) {
                return response()->json([
                    'success' => false,
                    'message' => 'Notification not found'
                ], 404);
            }

            $notification->read = false;
            $notification->read_at = null;
            $notification->save();

            return response()->json([
                'success' => true,
                'message' => 'Notification marked as unread',
                'data' => $notification
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark notification as unread',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mark all notifications as read
     *
     * @return JsonResponse
     */
    public function markAllAsRead(): JsonResponse
    {
        try {
            $user = Auth::user();
            $updated = UserNotification::where('user_id', $user->id)
                ->where('read', false)
                ->update([
                    'read' => true,
                    'read_at' => Carbon::now()
                ]);

            return response()->json([
                'success' => true,
                'message' => "Marked {$updated} notifications as read",
                'data' => [
                    'updated_count' => $updated
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to mark all notifications as read',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a notification
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy($id): JsonResponse
    {
        try {
            $user = Auth::user();
            $notification = UserNotification::where('user_id', $user->id)
                ->where('id', $id)
                ->first();

            if (!$notification) {
                return response()->json([
                    'success' => false,
                    'message' => 'Notification not found'
                ], 404);
            }

            $notification->delete();

            return response()->json([
                'success' => true,
                'message' => 'Notification deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete notification',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete all read notifications
     *
     * @return JsonResponse
     */
    public function clearRead(): JsonResponse
    {
        try {
            $user = Auth::user();
            $deleted = UserNotification::where('user_id', $user->id)
                ->where('read', true)
                ->delete();

            return response()->json([
                'success' => true,
                'message' => "Deleted {$deleted} read notifications",
                'data' => [
                    'deleted_count' => $deleted
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear read notifications',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get notification statistics
     *
     * @return JsonResponse
     */
    public function stats(): JsonResponse
    {
        try {
            $user = Auth::user();
            
            $totalCount = UserNotification::where('user_id', $user->id)->count();
            $unreadCount = UserNotification::where('user_id', $user->id)
                ->where('read', false)
                ->count();
            $readCount = $totalCount - $unreadCount;

            // Count by type
            $typeStats = UserNotification::where('user_id', $user->id)
                ->selectRaw('type, COUNT(*) as count')
                ->groupBy('type')
                ->get()
                ->pluck('count', 'type')
                ->toArray();

            // Count by priority
            $priorityStats = UserNotification::where('user_id', $user->id)
                ->selectRaw('priority, COUNT(*) as count')
                ->groupBy('priority')
                ->get()
                ->pluck('count', 'priority')
                ->toArray();

            // Recent activity (last 7 days)
            $recentActivity = UserNotification::where('user_id', $user->id)
                ->where('created_at', '>=', Carbon::now()->subDays(7))
                ->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'total_count' => $totalCount,
                    'unread_count' => $unreadCount,
                    'read_count' => $readCount,
                    'recent_activity' => $recentActivity,
                    'by_type' => $typeStats,
                    'by_priority' => $priorityStats
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch notification statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a single notification by ID
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show($id): JsonResponse
    {
        try {
            $user = Auth::user();
            $notification = UserNotification::where('user_id', $user->id)
                ->where('id', $id)
                ->first();

            if (!$notification) {
                return response()->json([
                    'success' => false,
                    'message' => 'Notification not found'
                ], 404);
            }

            // Add formatted data
            $notification->time_ago = $notification->created_at->diffForHumans();
            $notification->formatted_date = $notification->created_at->format('M d, Y h:i A');

            return response()->json([
                'success' => true,
                'data' => $notification
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch notification',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send a test notification (for testing purposes)
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function sendTestNotification(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'message' => 'required|string',
                'type' => 'string',
                'priority' => 'string',
                'link' => 'string|nullable',
                'link_text' => 'string|nullable'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $user = Auth::user();
            
            $notification = $this->notificationService->sendNotification(
                $user,
                $request->input('title'),
                $request->input('message'),
                $request->input('type', 'system'),
                $request->input('priority', 'medium'),
                $request->input('link'),
                $request->input('link_text')
            );

            return response()->json([
                'success' => true,
                'message' => 'Test notification sent successfully',
                'data' => $notification
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send test notification',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get notification settings/preferences
     *
     * @return JsonResponse
     */
    public function getPreferences(): JsonResponse
    {
        try {
            $user = Auth::user();
            $preferences = $user->notificationPreferences()
                ->pluck('value', 'key')
                ->toArray();

            // Default preferences if none exist
            $defaultPreferences = [
                'order_updates' => true,
                'product_changes' => true,
                'system_announcements' => true,
                'commission_notifications' => true,
                'support_messages' => true,
                'stock_alerts' => true,
            ];

            $preferences = array_merge($defaultPreferences, $preferences);

            return response()->json([
                'success' => true,
                'data' => $preferences
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch notification preferences',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update notification preferences
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function updatePreferences(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $preferences = $request->all();

            foreach ($preferences as $key => $value) {
                $user->notificationPreferences()->updateOrCreate(
                    ['key' => $key],
                    ['value' => (bool) $value]
                );
            }

            return response()->json([
                'success' => true,
                'message' => 'Notification preferences updated successfully',
                'data' => $preferences
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update notification preferences',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a price drop alert notification
     *
     * @param int $userId
     * @param array $alertData
     * @param float $newPrice
     * @return void
     */
    public function createPriceAlertNotification($userId, $alertData, $newPrice)
    {
        $priceReduction = $alertData['currentPrice'] - $newPrice;
        $percentageOff = ($priceReduction / $alertData['currentPrice']) * 100;
        
        // Format prices with currency symbol
        $formattedOldPrice = '$' . number_format($alertData['currentPrice'], 2);
        $formattedNewPrice = '$' . number_format($newPrice, 2);
        $formattedReduction = '$' . number_format($priceReduction, 2);
        
        // Create notification
        UserNotification::create([
            'user_id' => $userId,
            'title' => 'Price Drop Alert',
            'message' => "Price for {$alertData['productName']} has dropped from {$formattedOldPrice} to {$formattedNewPrice} (saving {$formattedReduction} or " . round($percentageOff, 1) . "%).",
            'type' => NotificationType::PriceAlert,
            'link' => route('product', $alertData['productId']),
            'link_text' => 'View Product',
            'read' => false,
        ]);
    }
} 