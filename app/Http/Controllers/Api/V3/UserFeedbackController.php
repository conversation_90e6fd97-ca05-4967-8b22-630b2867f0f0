<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\UserFeedback;
use App\Models\Wishlist;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class UserFeedbackController extends ApiResponse
{
    public function save(Request $request)
    {
        $messages = array(
            'name.required' => translate('Name is required'),
            'email.required' => translate('Email is required'),
            'email.email' => translate('Valid email is required'),
            'feedback.required' => translate('Feedback  is required'),
            'rate.required' => translate('Rate  is required'),
            'rate.integer' => translate('Rate must be integer'),
        );
        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'email' => 'required',
            'feedback' => 'required',
            'rate' => 'required|integer',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error('Validation Error', 'Please provide valid credentials', $validator->errors()->messages(), 400);
        }
        try {
            $user = auth()->user();

                $wishlist = new UserFeedback();
                $wishlist->user_id = $user?$user->id:Null;
                $wishlist->name = $request->name;
                $wishlist->email = $request->email;
                $wishlist->feedback = $request->feedback;
                $wishlist->rate = $request->rate;
                $wishlist->save();

            return $this->success(Null,'Your feedback has been submitted successfully');
        } catch (\Throwable $th) {
            return $this->error(400,'Failed to Submit feedback', $th->getMessage());
        }
    }
}
