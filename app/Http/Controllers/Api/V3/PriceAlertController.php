<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductMeta;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class PriceAlertController extends Controller
{
    /**
     * Create a new price alert
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'productId' => 'required|exists:products,id',
            'alertType' => 'required|in:any,percentage,specific',
            'percentageThreshold' => 'required_if:alertType,percentage|numeric|min:1|max:99',
            'targetPrice' => 'required_if:alertType,specific|numeric|min:0.01',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        $productId = $request->productId;
        $product = Product::findOrFail($productId);
        
        // Check if product exists and is published
        if (!$product || $product->published != 1) {
            return response()->json([
                'success' => false,
                'message' => 'Product not found or unavailable'
            ], 404);
        }

        // Check if user already has an alert for this product
        $existingAlert = ProductMeta::where([
            'product_id' => $productId,
            'user_id' => $user->id,
            'meta_key' => 'price_alert'
        ])->first();

        if ($existingAlert) {
            return response()->json([
                'success' => false,
                'message' => 'You already have a price alert for this product',
                'alert' => json_decode($existingAlert->meta_value)
            ], 409);
        }

        // Create alert data
        $alertData = [
            'id' => md5($user->id . '_' . $productId . '_' . time()),
            'productId' => $productId,
            'productName' => $product->name,
            'productImage' => $product->thumbnail ? asset($product->thumbnail) : null,
            'currentPrice' => $product->unit_price,
            'alertType' => $request->alertType,
            'createdAt' => Carbon::now()->toISOString(),
            'status' => 'active'
        ];

        if ($request->alertType === 'percentage') {
            $alertData['percentageThreshold'] = $request->percentageThreshold;
        } elseif ($request->alertType === 'specific') {
            $alertData['targetPrice'] = $request->targetPrice;
            
            // Validate target price is less than current price
            if ($request->targetPrice >= $product->unit_price) {
                return response()->json([
                    'success' => false,
                    'message' => 'Target price must be less than current price'
                ], 422);
            }
        }

        // Save the alert
        ProductMeta::create([
            'product_id' => $productId,
            'user_id' => $user->id,
            'meta_key' => 'price_alert',
            'meta_value' => json_encode($alertData)
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Price alert created successfully',
            'alert' => $alertData
        ], 201);
    }

    /**
     * Get all price alerts for the authenticated user
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function list()
    {
        $user = Auth::user();
        
        $alerts = ProductMeta::where([
            'user_id' => $user->id,
            'meta_key' => 'price_alert'
        ])->get();

        $alertData = [];
        
        foreach ($alerts as $alert) {
            $data = json_decode($alert->meta_value, true);
            
            // Get current product price to check if alert should be triggered
            $product = Product::find($alert->product_id);
            
            if ($product) {
                $currentPrice = $product->unit_price;
                $data['currentPrice'] = $currentPrice;
                
                // Update alert status if price has dropped
                if ($data['status'] === 'active') {
                    $triggered = false;
                    
                    if ($data['alertType'] === 'any' && $currentPrice < $data['currentPrice']) {
                        $triggered = true;
                    } elseif ($data['alertType'] === 'percentage') {
                        $threshold = $data['percentageThreshold'];
                        $originalPrice = $data['currentPrice'];
                        $percentageOff = (($originalPrice - $currentPrice) / $originalPrice) * 100;
                        
                        if ($percentageOff >= $threshold) {
                            $triggered = true;
                        }
                    } elseif ($data['alertType'] === 'specific' && $currentPrice <= $data['targetPrice']) {
                        $triggered = true;
                    }
                    
                    if ($triggered) {
                        $data['status'] = 'triggered';
                        
                        // Update the status in the database
                        $alert->meta_value = json_encode($data);
                        $alert->save();
                    }
                }
                
                $alertData[] = $data;
            }
        }

        return response()->json([
            'success' => true,
            'data' => $alertData
        ], 200);
    }

    /**
     * Delete a price alert
     *
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($id)
    {
        $user = Auth::user();
        
        $alerts = ProductMeta::where([
            'user_id' => $user->id,
            'meta_key' => 'price_alert'
        ])->get();
        
        $deleted = false;
        
        foreach ($alerts as $alert) {
            $data = json_decode($alert->meta_value, true);
            
            if (isset($data['id']) && $data['id'] === $id) {
                $alert->delete();
                $deleted = true;
                break;
            }
        }
        
        if ($deleted) {
            return response()->json([
                'success' => true,
                'message' => 'Price alert deleted successfully'
            ], 200);
        }
        
        return response()->json([
            'success' => false,
            'message' => 'Price alert not found'
        ], 404);
    }

    /**
     * Check if a product has a price alert
     *
     * @param Request $request
     * @param string $productId
     * @return \Illuminate\Http\JsonResponse
     */
    public function check(Request $request, $productId)
    {
        $user = Auth::user();
        
        $alert = ProductMeta::where([
            'product_id' => $productId,
            'user_id' => $user->id,
            'meta_key' => 'price_alert'
        ])->first();
        
        if ($alert) {
            $alertData = json_decode($alert->meta_value, true);
            
            // Get current product price to check if alert should be triggered
            $product = Product::find($productId);
            
            if ($product) {
                $currentPrice = $product->unit_price;
                $alertData['currentPrice'] = $currentPrice;
                
                // Update alert status if price has dropped
                if ($alertData['status'] === 'active') {
                    $triggered = false;
                    
                    if ($alertData['alertType'] === 'any' && $currentPrice < $alertData['currentPrice']) {
                        $triggered = true;
                    } elseif ($alertData['alertType'] === 'percentage') {
                        $threshold = $alertData['percentageThreshold'];
                        $originalPrice = $alertData['currentPrice'];
                        $percentageOff = (($originalPrice - $currentPrice) / $originalPrice) * 100;
                        
                        if ($percentageOff >= $threshold) {
                            $triggered = true;
                        }
                    } elseif ($alertData['alertType'] === 'specific' && $currentPrice <= $alertData['targetPrice']) {
                        $triggered = true;
                    }
                    
                    if ($triggered) {
                        $alertData['status'] = 'triggered';
                        
                        // Update the status in the database
                        $alert->meta_value = json_encode($alertData);
                        $alert->save();
                    }
                }
            }
            
            return response()->json([
                'success' => true,
                'hasAlert' => true,
                'alert' => $alertData
            ], 200);
        }
        
        return response()->json([
            'success' => true,
            'hasAlert' => false
        ], 200);
    }
} 