<?php

namespace App\Http\Controllers\Api\V3;


use App\Http\Resources\V3\ProductResource;
use App\Http\Resources\V3\ProductsResource;
use App\Models\Product;
use App\Services\Product\ApiCompareProductService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ApiCompareProductController extends ApiResponse
{
    protected ApiCompareProductService $compareProductService;

    public function __construct(ApiCompareProductService $compareProductService)
    {
        parent::__construct();
        $this->compareProductService = $compareProductService;
    }

    /**
     * Add a product to the comparison list
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function addToCompare(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'productId' => 'required|string|exists:products,slug',
        ]);

        if ($validator->fails()) {
            return $this->error(
                'VALIDATION_ERROR',
                'Validation failed',
                $validator->errors(),
                422
            );
        }
        try {
            // Validate the request


            // Get the authenticated user
            $user = Auth::user();
            $product = Product::where('slug', $request->productId)->first();
            // Add the product to the comparison list
            $compareProduct = $this->compareProductService->addToCompare(
                $user->id,
                $product->id
            );
            $result = [
                'id' => $compareProduct->id,
                'products' => new ProductResource($compareProduct->product),
            ];
            return $this->success(
                $result,
                'Product added to comparison list successfully'
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to add product to comparison list',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Remove a product from the comparison list
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function removeFromCompare(Request $request, string $slug): JsonResponse
    {
        $product = Product::where('slug', $slug)->first();
        if (!$product) {
            return $this->error(
                'VALIDATION_ERROR',
                'Please provide valid credentials',
                'Product not found',
                422
            );
        }

        try {
            // Get the authenticated user
            $user = Auth::user();

            // Remove the product from the comparison list
            $result = $this->compareProductService->removeFromCompare(
                $user->id,
                $product->id
            );

            if ($result) {

                $compareList = $this->compareProductService->getCompareList($user->id);

                // Extract the products from the comparison list
                $products = $compareList->map(function ($item) {
                    return $item->product;
                });

                return $this->success(
                    [
                        'products' => new ProductsResource($products),
                        'count' => $products->count(),
                    ],
                    'Product removed from comparison list successfully'
                );
            } else {
                return $this->error(
                    'NOT_FOUND',
                    'Product not found in comparison list',
                    null,
                    404
                );
            }
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to remove product from comparison list',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get all products in the comparison list
     *
     * @return JsonResponse
     */
    public function getCompareList(): JsonResponse
    {
        try {
            // Get the authenticated user
            $user = Auth::user();

            // Get the comparison list
            $compareList = $this->compareProductService->getCompareList($user->id);

            // Extract the products from the comparison list
            $products = $compareList->map(function ($item) {
                return $item->product;
            });

            return $this->success([
                'products' => new ProductsResource($products),
                'count' => $products->count(),
            ]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to get comparison list',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Clear the comparison list
     *
     * @return JsonResponse
     */
    public function clearCompareList(): JsonResponse
    {
        try {
            // Get the authenticated user
            $user = Auth::user();

            // Clear the comparison list
            $result = $this->compareProductService->clearCompareList($user->id);

            return $this->success(
                null,
                'Comparison list cleared successfully'
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to clear comparison list',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get comparable attributes for a category
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getCompareAttributes(Request $request): JsonResponse
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'category' => 'required|exists:categories,id',
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            // Get the attributes for the category
            $attributes = $this->compareProductService->getCompareAttributes($request->category);

            return $this->success($attributes, 'Comparable attributes fetched successfully');
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to get comparable attributes',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get similar products for comparison
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getSimilarProductsForComparison(Request $request): JsonResponse
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'productId' => 'required|exists:products,id',
                'limit' => 'nullable|integer|min:1|max:20',
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            // Get the authenticated user
            $user = Auth::user();

            // Get the limit parameter (default: 5)
            $limit = $request->input('limit', 5);

            // Get similar products for comparison
            $similarProducts = $this->compareProductService->getSimilarProductsForComparison(
                $request->productId,
                $user->id,
                $limit
            );

          

            return $this->success( new ProductsResource($similarProducts));
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to get similar products for comparison',
                $e->getMessage(),
                500
            );
        }
    }
}
