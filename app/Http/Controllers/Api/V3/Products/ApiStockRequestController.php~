<?php

namespace App\Http\Controllers\Api\V3\Products;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\ProductStockRequests\RequestResource;
use App\Models\Product;
use App\Models\StockRequest;
use App\Services\ProductStockRequestService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
class ApiStockRequestController extends ApiResponse
{
    protected ProductStockRequestService $productStockRequestService;
    public function __construct(ProductStockRequestService $productStockRequestService)
    {
        parent::__construct();
        $this->productStockRequestService = $productStockRequestService;
    }
    public function createStockRequest(Request $request): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'product_slug' => 'required|exists:products,slug',
            'quantity' => 'required|integer|min:1',
            'note' => 'string',
            'priority_level' => 'required|in:low,medium,high',
            'notification_email' => 'email',
            'notification_phone' => 'string',
            'notification_method' => 'required|in:email,sms,push_notification,all',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        $user = auth()->user();
        $product = Product::where('slug', $request->product_slug)->firstOrFail();
       if($this->productStockRequestService->chackProductStockRequest($product->id,$user->id)){
           return $this->error(
               'AlREADY_EXISTS',
                'You have already requested this product',
               '',
               400
           );
       }


        try {
            $stockRequest = $this->productStockRequestService->createStockRequest($request,$user->id,$product->id);
            return $this->success(
                new RequestResource($stockRequest),
                'Stock request created successfully',
                201
            );
        }catch (\Exception $e) {
            return $this->error(
                'ERROR_OCCURRED',
                'Please try again later',
                $e,
                500
            );
        }
    }

    public function getUserStockRequestDetails(Request $request, int $id): \Illuminate\Http\JsonResponse{

        $stockRequest= StockRequest::where('id', $id)->first();
        dd($stockRequest);
        if ($stockRequest) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                [],
                400
            );
        }
        $user = auth()->user();

        if (!$stockRequest || $stockRequest->user_id !== $user->id ) {
            return $this->error(
                'not_authorized',
                'You are not authorized',
                '',
                403
            );
        }
        try {
            return $this->success(
                new RequestResource($stockRequest),
                'Stock request details fetched successfully',
                200
            );
        }catch (\Exception $e) {
            return $this->error(
                'ERROR_OCCURRED',
                'Please try again later',
                $e,
                500
            );
        }

    }
    public function updateStockRequestStatus(Request $request): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'stock_request_id' => 'required|exists:stock_requests,id',
            'status' => 'required|in:pending,rejected,restock',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }


        try {
            $stockRequest = $this->productStockRequestService->updateStockRequestStatus($request->stock_request_id);
            return $this->success(
                new RequestResource($stockRequest),
                'Stock request Status updated successfully',
                201
            );
        }catch (\Exception $e) {
            return $this->error(
                'ERROR_OCCURRED',
                'Please try again later',
                $e,
                500
            );
        }
    }
    public function removeStockRequest(Request $request): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'stock_request_id' => 'required|exists:stock_requests,id',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        $user = auth()->user();
        $stockRequest= StockRequest::where('id', $request->stock_request_id)->first();
        if (!$stockRequest || $stockRequest->user_id !== $user->id ) {
            return $this->error(
                'not_authorized',
                'You are not authorized to update this review',
                '',
                403
            );
        }
        try {
            $stockRequest = $this->productStockRequestService->removeStockRequest($request);
            return $this->success(
                null,
                'Stock request Removed successfully',
                202
            );
        }catch (\Exception $e) {
            return $this->error(
                'ERROR_OCCURRED',
                'Please try again later',
                $e,
                500
            );
        }
    }
    public function getUserStockRequests(Request $request): \Illuminate\Http\JsonResponse
    {
        $user = auth()->user();
        $sort = in_array($request->input('sort_by'), ['newest', 'oldest']) ? $request->input('sort_by') : 'newest';
        $per_page = min((int)$request->input('per_page', 20), 50);
        $page = max((int)$request->input('page', 1), 1);
        $status = in_array($request->input('status'), ['pending', 'rejected', 'restock']) ? $request->input('status') : 'pending';
        $product_id = (int)$request->input('product_id', '');
        return $this->productStockRequestService->getStockRequests($per_page,$page,$status,$product_id,$user->id,$sort);
    }
    public function getProductStockRequests(Request $request): \Illuminate\Http\JsonResponse
    {
        $sort = in_array($request->input('sort'), ['newest', 'oldest']) ? $request->input('sort') : 'newest';
        $per_page = min((int)$request->input('per_page', 3), 50);
        $page = max((int)$request->input('page', 1), 1);
        $status = in_array($request->input('status'), ['pending', 'rejected', 'restock']) ? $request->input('sort') : 'pending';
        $product_id = max((int)$request->input('product_id', 1), 1);
        return $this->productStockRequestService->getStockRequests($per_page,$page,$status,$product_id,'',$sort);
    }
}
