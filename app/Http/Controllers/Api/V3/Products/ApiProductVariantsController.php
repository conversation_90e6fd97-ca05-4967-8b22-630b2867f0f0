<?php

namespace App\Http\Controllers\Api\V3\Products;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\ProductResource;
use App\Models\Product;
use App\Models\ProductStock;
use App\Models\Color;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ApiProductVariantsController extends ApiResponse
{
    /**
     * Get product colors
     *
     * @param Request $request
     * @param string $id
     * @return JsonResponse
     */
    public function getProductColors(Request $request, $id): JsonResponse
    {
        try {
            $productId = $id;

            if (!$productId) {
                return $this->error('VALIDATION_ERROR', 'Product ID is required');
            }

            // Find the product
            $product = Product::findOrFail($productId);
            
            // Get colors from the product
            $colorIds = is_string($product->colors) ? json_decode($product->colors) ?? [] : $product->colors;
            $colorData = [];
            
            // If product has colors defined
            if (!empty($colorIds)) {
                $colorData = Color::whereIn('id', $colorIds)
                    ->select('id', 'name', 'code as image')
                    ->get()
                    ->map(function ($color) {
                        // Map the color code to an image representation
                        $color->image = $color->image ?? '#FFFFFF';
                        return $color;
                    })
                    ->toArray();
            }
            
            // If product has choice options with 'color' attribute
            $choiceOptions = is_string($product->choice_options) ? json_decode($product->choice_options, true) ?? [] : ($product->choice_options ?? []);
            foreach ($choiceOptions as $option) {
                if (strtolower($option['title']) === 'color' || strtolower($option['title']) === 'colors') {
                    foreach ($option['options'] as $colorName) {
                        $colorData[] = [
                            'id' => strtolower(str_replace(' ', '-', $colorName)),
                            'name' => $colorName,
                            'image' => '/assets/img/placeholder.jpg'
                        ];
                    }
                }
            }

            // If we still have no colors, return at least one default color
            if (empty($colorData)) {
                $colorData[] = [
                    'id' => 'default',
                    'name' => 'Default',
                    'image' => '/assets/img/placeholder.jpg'
                ];
            }

            return $this->success($colorData);
        } catch (\Exception $e) {
            Log::error('Failed to get product colors: ' . $e->getMessage());
            return $this->error('SERVER_ERROR', 'Failed to get product colors', $e->getMessage(), 500);
        }
    }

    /**
     * Get product packages
     *
     * @param Request $request
     * @param string $id
     * @return JsonResponse
     */
    public function getProductPackages(Request $request, $id): JsonResponse
    {
        try {
            $productId = $id;

            if (!$productId) {
                return $this->error('VALIDATION_ERROR', 'Product ID is required');
            }

            // Find the product
            $product = Product::findOrFail($productId);
            
            // Check if product has any packages defined
            $packages = [];
            
            // Check for product bundles in choice options
            $choiceOptions = is_string($product->choice_options) ? json_decode($product->choice_options, true) ?? [] : ($product->choice_options ?? []);
            foreach ($choiceOptions as $option) {
                if (in_array(strtolower($option['title']), ['package', 'bundle', 'packaging'])) {
                    foreach ($option['options'] as $index => $packageName) {
                        $packages[] = [
                            'id' => 'package-' . ($index + 1),
                            'name' => $packageName,
                            'price' => $product->unit_price,
                            'image' => $this->getImageUrl($product->thumbnail_img),
                            'description' => 'Standard package option',
                            'in_stock' => true
                        ];
                    }
                    break;
                }
            }
            
            // If no packages found, check product variations
            if (empty($packages)) {
                $variations = is_string($product->variations) ? json_decode($product->variations, true) ?? [] : ($product->variations ?? []);
                if (!empty($variations)) {
                    foreach ($variations as $index => $variation) {
                        if (isset($variation['type']) && 
                            in_array(strtolower($variation['type']), ['package', 'bundle', 'packaging'])) {
                            
                            $packages[] = [
                                'id' => 'variation-' . ($index + 1),
                                'name' => $variation['name'] ?? 'Package ' . ($index + 1),
                                'price' => $variation['price'] ?? $product->unit_price,
                                'original_price' => $product->unit_price,
                                'discount' => $product->unit_price - ($variation['price'] ?? $product->unit_price),
                                'image' => $variation['image'] ?? $this->getImageUrl($product->thumbnail_img),
                                'description' => $variation['description'] ?? 'Package option',
                                'in_stock' => $variation['in_stock'] ?? true
                            ];
                        }
                    }
                }
            }

            // If still no packages found, check if there are related products that could be bundled
            if (empty($packages)) {
                $relatedProducts = Product::where('category_id', $product->category_id)
                    ->where('id', '!=', $product->id)
                    ->where('published', 1)
                    ->where('approved', 1)
                    ->take(2)
                    ->get();
                
                if ($relatedProducts->isNotEmpty()) {
                    foreach ($relatedProducts as $index => $relatedProduct) {
                        $packages[] = [
                            'id' => 'related-' . $relatedProduct->id,
                            'name' => 'Bundle with ' . $relatedProduct->name,
                            'price' => $relatedProduct->unit_price,
                            'image' => $this->getImageUrl($relatedProduct->thumbnail_img),
                            'description' => 'Add this item to your purchase',
                            'in_stock' => $relatedProduct->current_stock > 0
                        ];
                    }
                }
            }

            return $this->success($packages);
        } catch (\Exception $e) {
            Log::error('Failed to get product packages: ' . $e->getMessage());
            return $this->error('SERVER_ERROR', 'Failed to get product packages', $e->getMessage(), 500);
        }
    }

    /**
     * Get product discounts
     *
     * @param string $id
     * @return JsonResponse
     */
    public function getProductDiscounts(string $id): JsonResponse
    {
        try {
            // Find the product
            $product = Product::findOrFail($id);
            
            $discounts = [];
            
            // Base discount from the product
            if ($product->discount > 0) {
                $discounts[] = [
                    'id' => 'product-base',
                    'name' => 'Product Discount',
                    'discount_type' => $product->discount_type,
                    'discount_value' => $product->discount,
                    'is_active' => true,
                    'start_date' => null,
                    'end_date' => null
                ];
            }
            
            // Check for flash deals
            $flashDeals = DB::table('flash_deal_products')
                ->join('flash_deals', 'flash_deals.id', '=', 'flash_deal_products.flash_deal_id')
                ->where('flash_deal_products.product_id', $id)
                ->where('flash_deals.status', 1)
                ->where('flash_deals.start_date', '<=', now())
                ->where('flash_deals.end_date', '>=', now())
                ->select(
                    'flash_deals.id',
                    'flash_deals.title as name',
                    'flash_deal_products.discount as discount_value',
                    'flash_deal_products.discount_type',
                    'flash_deals.start_date',
                    'flash_deals.end_date'
                )
                ->get();
            
            foreach ($flashDeals as $deal) {
                $discounts[] = [
                    'id' => 'flash-' . $deal->id,
                    'name' => $deal->name,
                    'discount_type' => $deal->discount_type,
                    'discount_value' => $deal->discount_value,
                    'is_active' => true,
                    'start_date' => $deal->start_date,
                    'end_date' => $deal->end_date
                ];
            }
            
            // Check for club point discounts if applicable
            if (addon_is_activated('club_point')) {
                $clubPointDiscount = [
                    'id' => 'club-point',
                    'name' => 'Club Point Reward',
                    'discount_type' => 'point',
                    'discount_value' => $product->earn_point ?? 0,
                    'is_active' => $product->earn_point > 0
                ];
                
                if ($clubPointDiscount['is_active']) {
                    $discounts[] = $clubPointDiscount;
                }
            }

            return $this->success($discounts);
        } catch (\Exception $e) {
            Log::error('Failed to get product discounts: ' . $e->getMessage());
            return $this->error('SERVER_ERROR', 'Failed to get product discounts', $e->getMessage(), 500);
        }
    }

    /**
     * Get frequently bought together products
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getFrequentlyBoughtTogether(Request $request, $id): JsonResponse
    {
        try {
            // Get parameters directly from the request query parameters
            $productId = $id;
            $limit = $request->query('limit', 4);
            
            if (!$productId) {
                return $this->error('VALIDATION_ERROR', 'Product ID is required');
            }

            // Find the product
            $product = Product::findOrFail($productId);
            
            // Get related products from the same category
            $relatedProducts = Product::where('published', 1)
                ->where('approved', 1)
                ->where('id', '!=', $productId)
                ->where('category_id', $product->category_id)
                ->orderBy('num_of_sale', 'desc')
                ->take($limit)
                ->get();
            
            $frequentlyBoughtTogether = $relatedProducts->map(function($item) {
                return new ProductResource($item);
            });

            return $this->success($frequentlyBoughtTogether);
        } catch (\Exception $e) {
            Log::error('Failed to get frequently bought together products: ' . $e->getMessage());
            return $this->error('SERVER_ERROR', 'Failed to get frequently bought together products', $e->getMessage(), 500);
        }
    }

    /**
     * Get image URL safely
     * 
     * @param string|null $imageId
     * @return string
     */
    private function getImageUrl($imageId): string
    {
        if (!$imageId) {
            return '/assets/img/placeholder.jpg';
        }
        
        if (function_exists('uploaded_asset')) {
            return uploaded_asset($imageId);
        }
        
        // Fallback if uploaded_asset function is not available
        return '/uploads/' . $imageId;
    }
} 