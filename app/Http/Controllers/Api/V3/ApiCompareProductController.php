<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Resources\V3\ProductResource;
use App\Http\Resources\V3\ProductsResource;
use App\Models\Product;
use App\Services\Product\ApiCompareProductService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ApiCompareProductController extends ApiResponse
{
    protected ApiCompareProductService $compareProductService;

    public function __construct(ApiCompareProductService $compareProductService)
    {
        parent::__construct();
        $this->compareProductService = $compareProductService;
    }

    /**
     * Add a product to the comparison list
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function addToCompare(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'productId' => 'required', // Accept both string slugs and numeric IDs
        ]);

        if ($validator->fails()) {
            return $this->error(
                'VALIDATION_ERROR',
                'Validation failed',
                $validator->errors(),
                422
            );
        }
        try {
            // Get the authenticated user
            $user = Auth::user();
            
            $productId = $request->productId;
            $product = null;
            
            // Try to find product by different methods based on input type
            if (is_numeric($productId)) {
                // If numeric, try as ID first, then as slug
                $product = Product::where('id', $productId)
                    ->where('published', 1)
                    ->where('approved', 1)
                    ->first();
                
                if (!$product) {
                    $product = Product::where('slug', $productId)
                        ->where('published', 1)
                        ->where('approved', 1)
                        ->first();
                }
            } else {
                // If string, try as slug first, then as numeric ID
                $product = Product::where('slug', $productId)
                    ->where('published', 1)
                    ->where('approved', 1)
                    ->first();
                
                if (!$product && is_numeric($productId)) {
                    $product = Product::where('id', $productId)
                        ->where('published', 1)
                        ->where('approved', 1)
                        ->first();
                }
            }
            
            // If still not found, return error
            if (!$product) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Product not found',
                    'The specified product does not exist or is not available',
                    404
                );
            }
            
            // Add the product to the comparison list
            $compareProduct = $this->compareProductService->addToCompare(
                $user->id,
                $product->id
            );
            
            // Get the updated compare list to return formatted like regular products
            $compareList = $this->compareProductService->getCompareList($user->id);
            
            // Extract the products from the comparison list
            $products = $compareList->map(function ($item) {
                return $item->product;
            });
            
            return $this->success([
                'data' => ProductResource::collection($products),
                'count' => $products->count(),
            ], 'Product added to comparison list successfully');
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to add product to comparison list',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Remove a product from the comparison list
     *
     * @param string $slug
     * @return JsonResponse
     */
    public function removeFromCompare(string $slug): JsonResponse
    {
        try {
            // Get the authenticated user
            $user = Auth::user();
            
            // Find product by slug or ID
            $product = Product::where('slug', $slug)->first();
            
            // If not found by slug, try as ID
            if (!$product) {
                $product = Product::find($slug);
            }
            
            // If still not found, return error
            if (!$product) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Product not found',
                    'The specified product does not exist',
                    404
                );
            }

            // Remove the product from the comparison list
            $this->compareProductService->removeFromCompare(
                $user->id,
                $product->id
            );

            // Always return the updated list, even if removal might have failed
            $compareList = $this->compareProductService->getCompareList($user->id);
            
            // Extract the products from the comparison list
            $products = $compareList->map(function ($item) {
                return $item->product;
            });

            return $this->success([
                'data' => ProductResource::collection($products),
                'count' => $products->count(),
            ], 'Product removed from comparison list successfully');
        } catch (\Exception $e) {
            // Even in case of an error, try to return the current list
            try {
                $user = Auth::user();
                $compareList = $this->compareProductService->getCompareList($user->id);
                
                // Extract the products from the comparison list
                $products = $compareList->map(function ($item) {
                    return $item->product;
                });
                
                return $this->error(
                    'SERVER_ERROR',
                    'Failed to remove product from comparison list',
                    $e->getMessage(),
                    500,
                    [
                        'data' => ProductResource::collection($products),
                        'count' => $products->count(),
                    ]
                );
            } catch (\Exception $innerE) {
                return $this->error(
                    'SERVER_ERROR',
                    'Failed to remove product from comparison list',
                    $e->getMessage(),
                    500
                );
            }
        }
    }

    /**
     * Get all products in the comparison list
     *
     * @return JsonResponse
     */
    public function getCompareList(): JsonResponse
    {
        try {
            // Get the authenticated user
            $user = Auth::user();

            // Get the comparison list
            $compareList = $this->compareProductService->getCompareList($user->id);

            // Extract the products from the comparison list
            $products = $compareList->map(function ($item) {
                return $item->product;
            });

            return $this->success([
                'data' => ProductResource::collection($products),
                'count' => $products->count(),
            ]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to get comparison list',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Clear the comparison list
     *
     * @return JsonResponse
     */
    public function clearCompareList(): JsonResponse
    {
        try {
            // Get the authenticated user
            $user = Auth::user();

            // Clear the comparison list
            $this->compareProductService->clearCompareList($user->id);

            return $this->success([
                'data' => [],
                'count' => 0,
            ], 'Comparison list cleared successfully');
        } catch (\Exception $e) {
            // Even in case of error, try to return the current list state
            try {
                $user = Auth::user();
                $compareList = $this->compareProductService->getCompareList($user->id);
                
                // Extract the products from the comparison list
                $products = $compareList->map(function ($item) {
                    return $item->product;
                });
                
                return $this->error(
                    'SERVER_ERROR',
                    'Failed to clear comparison list',
                    $e->getMessage(),
                    500,
                    [
                        'data' => ProductResource::collection($products),
                        'count' => $products->count(),
                    ]
                );
            } catch (\Exception $innerE) {
                return $this->error(
                    'SERVER_ERROR',
                    'Failed to clear comparison list',
                    $e->getMessage(),
                    500
                );
            }
        }
    }

    /**
     * Get comparable attributes for a category
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getCompareAttributes(Request $request): JsonResponse
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'category' => 'required|exists:categories,id',
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            // Get the attributes for the category
            $attributes = $this->compareProductService->getCompareAttributes($request->category);

            return $this->success($attributes, 'Comparable attributes fetched successfully');
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to get comparable attributes',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get similar products for comparison
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getSimilarProductsForComparison(Request $request): JsonResponse
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'productId' => 'required|exists:products,id',
                'limit' => 'nullable|integer|min:1|max:20',
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            // Get the authenticated user
            $user = Auth::user();

            // Get the limit parameter (default: 5)
            $limit = $request->input('limit', 5);

            // Get similar products for comparison
            $similarProducts = $this->compareProductService->getSimilarProductsForComparison(
                $request->productId,
                $user->id,
                $limit
            );

            return $this->success([
                'data' => ProductResource::collection($similarProducts),
                'count' => $similarProducts->count(),
            ]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to get similar products for comparison',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Share comparison list
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function shareComparisonList(Request $request): JsonResponse
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'expires_in_hours' => 'nullable|integer|min:1|max:168', // Max 1 week
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            // Get the authenticated user
            $user = Auth::user();

            // Get the expires in hours (default: 24 hours)
            $expiresInHours = $request->input('expires_in_hours', 24);

            // Share the comparison list
            $sharedList = $this->compareProductService->shareComparisonList($user->id, $expiresInHours);

            return $this->success([
                'share_id' => $sharedList->share_id,
                'share_url' => url("/compare/shared/{$sharedList->share_id}"),
                'expires_at' => $sharedList->expires_at->toIso8601String(),
            ], 'Comparison list shared successfully');
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to share comparison list',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get shared comparison list
     *
     * @param string $shareId
     * @return JsonResponse
     */
    public function getSharedComparisonList(string $shareId): JsonResponse
    {
        try {
            // Get the shared comparison list
            $sharedList = $this->compareProductService->getSharedComparisonList($shareId);

            // If the shared list doesn't exist or has expired, return an error
            if (!$sharedList) {
                return $this->error(
                    'SHARED_LIST_NOT_FOUND',
                    'The shared comparison list does not exist or has expired',
                    null,
                    404
                );
            }

            // Get the products from the shared list
            $productIds = $sharedList->product_ids;
            $products = Product::whereIn('id', $productIds)
                ->where('published', 1)
                ->where('approved', 1)
                ->get();

            // Get the category of the first product (if any) to fetch attributes
            $categoryId = null;
            if ($products->isNotEmpty()) {
                $categoryId = $products->first()->category_id;
            }

            // Get comparable attributes if a category is available
            $attributes = null;
            if ($categoryId) {
                $attributes = $this->compareProductService->getCompareAttributes($categoryId);
            }

            // Format the response
            $data = [
                'data' => ProductResource::collection($products),
                'count' => $products->count(),
                'attributes' => $attributes,
                'expiresAt' => $sharedList->expires_at->toIso8601String(),
            ];

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to get shared comparison list',
                $e->getMessage(),
                500
            );
        }
    }
}
