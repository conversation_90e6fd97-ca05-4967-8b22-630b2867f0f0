<?php

namespace App\Http\Controllers\Api\V3\Security;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Models\User;
use App\Models\LoginAttempt;
use App\Models\SecurityLog;
use App\Models\ApiKey;
use App\Models\UserToken;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Password;

class ApiSecurityController extends ApiResponse
{
    /**
     * Get security settings
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getSecuritySettings(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $settings = [
            'twoFactorAuthRequired' => (bool)$user->two_factor_enabled,
            'twoFactorAuthRequiredSince' => $user->two_factor_enabled_at ? $user->two_factor_enabled_at->toISOString() : null,
            'twoFactorAuthLastUsed' => $user->two_factor_last_used_at ? $user->two_factor_last_used_at->toISOString() : null,
            'passwordExpiryDays' => 90, // Default password expiry period
            'sessionTimeoutMinutes' => 30, // Default session timeout
            'maxLoginAttempts' => 5, // Default max login attempts
            'allowedIpAddresses' => [], // Empty by default
            'notifyOnNewLogin' => true,
            'notifyOnPasswordChange' => true,
            'notifyOnSecurityAlert' => true,
        ];
        
        return $this->success($settings);
    }
    
    /**
     * Update security settings
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function updateSecuritySettings(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'notifyOnNewLogin' => 'sometimes|boolean',
            'notifyOnPasswordChange' => 'sometimes|boolean',
            'notifyOnSecurityAlert' => 'sometimes|boolean',
            'sessionTimeoutMinutes' => 'sometimes|integer|min:5|max:1440',
            'allowedIpAddresses' => 'sometimes|array',
            'allowedIpAddresses.*' => 'ip',
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid security settings',
                $validator->errors()->messages(),
                422
            );
        }
        
        $user = $request->user();
        
        // Update user preferences for security notifications
        if ($request->has('notifyOnNewLogin')) {
            $user->notify_on_new_login = $request->notifyOnNewLogin;
        }
        
        if ($request->has('notifyOnPasswordChange')) {
            $user->notify_on_password_change = $request->notifyOnPasswordChange;
        }
        
        if ($request->has('notifyOnSecurityAlert')) {
            $user->notify_on_security_alert = $request->notifyOnSecurityAlert;
        }
        
        if ($request->has('sessionTimeoutMinutes')) {
            $user->session_timeout = $request->sessionTimeoutMinutes;
        }
        
        if ($request->has('allowedIpAddresses')) {
            $user->allowed_ips = json_encode($request->allowedIpAddresses);
        }
        
        $user->save();
        
        // Log security settings update
        $this->logSecurityEvent($user->id, 'security_settings_update', $request->ip(), $request->userAgent());
        
        return $this->getSecuritySettings($request);
    }
    
    /**
     * Change user password
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function changePassword(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'currentPassword' => 'required|string',
            'newPassword' => ['required', 'string', 'confirmed', Password::min(8)->mixedCase()->numbers()->symbols()],
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid password information',
                $validator->errors()->messages(),
                422
            );
        }
        
        $user = $request->user();
        
        if (!Hash::check($request->currentPassword, $user->password)) {
            return $this->error(
                'INVALID_PASSWORD',
                'The current password is incorrect',
                'Please provide your correct current password',
                401
            );
        }
        
        // Check if new password is same as current
        if (Hash::check($request->newPassword, $user->password)) {
            return $this->error(
                'SAME_PASSWORD',
                'The new password cannot be the same as your current password',
                'Please choose a different password',
                422
            );
        }
        
        // Update password
        $user->password = Hash::make($request->newPassword);
        $user->password_changed_at = now();
        $user->save();
        
        // Log password change
        $this->logSecurityEvent($user->id, 'password_change', $request->ip(), $request->userAgent());
        
        // Revoke all other tokens except the current one
        if ($user->tokens && count($user->tokens) > 1) {
            $currentTokenId = $request->user()->currentAccessToken()->id;
            $user->tokens()->where('id', '!=', $currentTokenId)->delete();
        }
        
        return $this->success(null, 'Password changed successfully');
    }
    
    /**
     * Generate 2FA secret
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function generate2FASecret(Request $request): JsonResponse
    {
        $user = $request->user();
        
        // Generate a secret for 2FA
        $secret = Str::random(32);
        
        // In a real implementation, we would use a library like pragmarx/google2fa
        // to generate a proper secret and QR code URL
        $qrCodeUrl = "https://chart.googleapis.com/chart?chs=200x200&chld=M|0&cht=qr&chl=otpauth://totp/Buzfi:{$user->email}?secret={$secret}&issuer=Buzfi";
        
        // Store the secret temporarily
        $user->two_factor_secret = $secret;
        $user->two_factor_recovery_codes = json_encode([
            Str::random(10),
            Str::random(10),
            Str::random(10),
            Str::random(10),
            Str::random(10),
        ]);
        $user->save();
        
        return $this->success([
            'secret' => $secret,
            'qrCodeUrl' => $qrCodeUrl,
            'manualEntryCode' => $secret,
        ]);
    }
    
    /**
     * Enable 2FA
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function enable2FA(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'token' => 'required|string|size:6|regex:/^[0-9]+$/',
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide a valid verification token',
                $validator->errors()->messages(),
                422
            );
        }
        
        $user = $request->user();
        
        // In a real implementation, we would verify the token using a library like pragmarx/google2fa
        // For now, we'll just check if the token is '123456' as a placeholder
        if ($request->token !== '123456') {
            return $this->error(
                'INVALID_TOKEN',
                'The verification token is invalid',
                'Please check your authentication app and try again',
                401
            );
        }
        
        // Enable 2FA
        $user->two_factor_enabled = true;
        $user->two_factor_enabled_at = now();
        $user->save();
        
        // Log 2FA enable
        $this->logSecurityEvent($user->id, 'two_factor_enable', $request->ip(), $request->userAgent());
        
        return $this->success([
            'success' => true,
            'recoveryCodes' => json_decode($user->two_factor_recovery_codes),
        ], 'Two-factor authentication has been enabled');
    }
    
    /**
     * Disable 2FA
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function disable2FA(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'password' => 'required|string',
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide your password',
                $validator->errors()->messages(),
                422
            );
        }
        
        $user = $request->user();
        
        if (!Hash::check($request->password, $user->password)) {
            return $this->error(
                'INVALID_PASSWORD',
                'The password is incorrect',
                'Please provide your correct password to disable 2FA',
                401
            );
        }
        
        // Disable 2FA
        $user->two_factor_enabled = false;
        $user->two_factor_secret = null;
        $user->two_factor_recovery_codes = null;
        $user->save();
        
        // Log 2FA disable
        $this->logSecurityEvent($user->id, 'two_factor_disable', $request->ip(), $request->userAgent());
        
        return $this->success(['success' => true], 'Two-factor authentication has been disabled');
    }
    
    /**
     * Get active sessions
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getActiveSessions(Request $request): JsonResponse
    {
        $user = $request->user();
        $currentTokenId = $request->user()->currentAccessToken()->id;
        
        $sessions = $user->tokens()->orderBy('created_at', 'desc')->get()->map(function ($token) use ($currentTokenId) {
            return [
                'id' => $token->id,
                'userAgent' => $token->user_agent ?? 'Unknown',
                'ipAddress' => $token->ip_address ?? 'Unknown',
                'location' => $token->location ?? 'Unknown',
                'device' => $this->parseUserAgent($token->user_agent ?? '', 'device'),
                'browser' => $this->parseUserAgent($token->user_agent ?? '', 'browser'),
                'os' => $this->parseUserAgent($token->user_agent ?? '', 'os'),
                'lastActive' => $token->last_used_at ? $token->last_used_at->toISOString() : $token->created_at->toISOString(),
                'createdAt' => $token->created_at->toISOString(),
                'isCurrent' => $token->id === $currentTokenId,
            ];
        });
        
        return $this->success($sessions);
    }
    
    /**
     * Revoke a session
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function revokeSession(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'sessionId' => 'required|string',
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide a valid session ID',
                $validator->errors()->messages(),
                422
            );
        }
        
        $user = $request->user();
        $currentTokenId = $request->user()->currentAccessToken()->id;
        
        if ($request->sessionId == $currentTokenId) {
            return $this->error(
                'CURRENT_SESSION',
                'Cannot revoke the current session',
                'Use the logout function to end your current session',
                422
            );
        }
        
        $token = $user->tokens()->find($request->sessionId);
        
        if (!$token) {
            return $this->error(
                'SESSION_NOT_FOUND',
                'The specified session was not found',
                'Please provide a valid session ID',
                404
            );
        }
        
        $token->delete();
        
        // Log session revocation
        $this->logSecurityEvent($user->id, 'session_revoke', $request->ip(), $request->userAgent());
        
        return $this->success(null, 'Session revoked successfully');
    }
    
    /**
     * Revoke all sessions except current
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function revokeAllSessions(Request $request): JsonResponse
    {
        $user = $request->user();
        $currentTokenId = $request->user()->currentAccessToken()->id;
        
        $user->tokens()->where('id', '!=', $currentTokenId)->delete();
        
        // Log all sessions revocation
        $this->logSecurityEvent($user->id, 'all_sessions_revoke', $request->ip(), $request->userAgent());
        
        return $this->success(null, 'All other sessions revoked successfully');
    }
    
    /**
     * Get security logs
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getSecurityLogs(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'page' => 'sometimes|integer|min:1',
            'pageSize' => 'sometimes|integer|min:1|max:100',
            'eventType' => 'sometimes|string',
            'startDate' => 'sometimes|date',
            'endDate' => 'sometimes|date|after_or_equal:startDate',
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid parameters',
                $validator->errors()->messages(),
                422
            );
        }
        
        $user = $request->user();
        $page = $request->input('page', 1);
        $pageSize = $request->input('pageSize', 10);
        
        $query = SecurityLog::where('user_id', $user->id)->orderBy('created_at', 'desc');
        
        if ($request->has('eventType')) {
            $query->where('event_type', $request->eventType);
        }
        
        if ($request->has('startDate')) {
            $query->where('created_at', '>=', $request->startDate);
        }
        
        if ($request->has('endDate')) {
            $query->where('created_at', '<=', $request->endDate);
        }
        
        $total = $query->count();
        $logs = $query->skip(($page - 1) * $pageSize)->take($pageSize)->get()->map(function ($log) {
            return [
                'id' => $log->id,
                'eventType' => $log->event_type,
                'ipAddress' => $log->ip_address,
                'userAgent' => $log->user_agent,
                'timestamp' => $log->created_at->toISOString(),
                'details' => json_decode($log->details),
                'location' => $log->location,
                'status' => $log->status,
            ];
        });
        
        return $this->success([
            'logs' => $logs,
            'meta' => [
                'current_page' => $page,
                'per_page' => $pageSize,
                'total' => $total,
                'last_page' => ceil($total / $pageSize),
            ],
        ]);
    }
    
    /**
     * Check password strength
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function checkPasswordStrength(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'password' => 'required|string',
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide a password to check',
                $validator->errors()->messages(),
                422
            );
        }
        
        $password = $request->password;
        $length = strlen($password);
        $strength = 0;
        $feedback = [];
        
        // Check length
        if ($length < 8) {
            $strength = 0;
            $feedback[] = 'Password is too short';
        } else if ($length < 12) {
            $strength += 1;
        } else if ($length < 16) {
            $strength += 2;
        } else {
            $strength += 3;
        }
        
        // Check for mixed case
        if (preg_match('/[a-z]/', $password) && preg_match('/[A-Z]/', $password)) {
            $strength += 1;
        } else {
            $feedback[] = 'Password should contain both uppercase and lowercase letters';
        }
        
        // Check for numbers
        if (preg_match('/[0-9]/', $password)) {
            $strength += 1;
        } else {
            $feedback[] = 'Password should contain at least one number';
        }
        
        // Check for special characters
        if (preg_match('/[^a-zA-Z0-9]/', $password)) {
            $strength += 1;
        } else {
            $feedback[] = 'Password should contain at least one special character';
        }
        
        // Normalize strength to 0-100
        $strengthPercent = min(100, round(($strength / 6) * 100));
        
        // Define strength rating
        $rating = 'weak';
        if ($strengthPercent >= 80) {
            $rating = 'strong';
        } else if ($strengthPercent >= 50) {
            $rating = 'medium';
        }
        
        return $this->success([
            'strength' => $strengthPercent,
            'rating' => $rating,
            'feedback' => $feedback,
        ]);
    }
    
    /**
     * Get login attempts
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getLoginAttempts(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'page' => 'sometimes|integer|min:1',
            'pageSize' => 'sometimes|integer|min:1|max:100',
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid parameters',
                $validator->errors()->messages(),
                422
            );
        }
        
        $user = $request->user();
        $page = $request->input('page', 1);
        $pageSize = $request->input('pageSize', 10);
        
        $query = LoginAttempt::where('user_id', $user->id)->orderBy('created_at', 'desc');
        
        $total = $query->count();
        $attempts = $query->skip(($page - 1) * $pageSize)->take($pageSize)->get()->map(function ($attempt) {
            return [
                'id' => $attempt->id,
                'ipAddress' => $attempt->ip_address,
                'userAgent' => $attempt->user_agent,
                'timestamp' => $attempt->created_at->toISOString(),
                'success' => (bool)$attempt->success,
                'location' => $attempt->location,
                'failureReason' => $attempt->failure_reason,
            ];
        });
        
        return $this->success([
            'loginAttempts' => $attempts,
            'meta' => [
                'current_page' => $page,
                'per_page' => $pageSize,
                'total' => $total,
                'last_page' => ceil($total / $pageSize),
            ],
        ]);
    }
    
    /**
     * Helper method to log security events
     * 
     * @param int $userId
     * @param string $eventType
     * @param string $ipAddress
     * @param string $userAgent
     * @param array $details
     * @param string $status
     * @return void
     */
    private function logSecurityEvent($userId, $eventType, $ipAddress, $userAgent, $details = [], $status = 'success'): void
    {
        SecurityLog::create([
            'user_id' => $userId,
            'event_type' => $eventType,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'details' => json_encode($details),
            'status' => $status,
        ]);
    }
    
    /**
     * Helper method to parse user agent string
     * 
     * @param string $userAgent
     * @param string $part
     * @return string
     */
    private function parseUserAgent($userAgent, $part): string
    {
        // In a real implementation, we would use a proper user agent parser library
        // For now, we'll just do a very basic parsing
        switch ($part) {
            case 'device':
                if (strpos($userAgent, 'Mobile') !== false) {
                    return 'Mobile';
                } elseif (strpos($userAgent, 'Tablet') !== false) {
                    return 'Tablet';
                } else {
                    return 'Desktop';
                }
            
            case 'browser':
                if (strpos($userAgent, 'Chrome') !== false) {
                    return 'Chrome';
                } elseif (strpos($userAgent, 'Firefox') !== false) {
                    return 'Firefox';
                } elseif (strpos($userAgent, 'Safari') !== false) {
                    return 'Safari';
                } elseif (strpos($userAgent, 'MSIE') !== false || strpos($userAgent, 'Trident') !== false) {
                    return 'Internet Explorer';
                } elseif (strpos($userAgent, 'Edge') !== false) {
                    return 'Edge';
                } else {
                    return 'Unknown';
                }
            
            case 'os':
                if (strpos($userAgent, 'Windows') !== false) {
                    return 'Windows';
                } elseif (strpos($userAgent, 'Mac') !== false) {
                    return 'macOS';
                } elseif (strpos($userAgent, 'Linux') !== false) {
                    return 'Linux';
                } elseif (strpos($userAgent, 'Android') !== false) {
                    return 'Android';
                } elseif (strpos($userAgent, 'iOS') !== false || strpos($userAgent, 'iPhone') !== false || strpos($userAgent, 'iPad') !== false) {
                    return 'iOS';
                } else {
                    return 'Unknown';
                }
            
            default:
                return 'Unknown';
        }
    }
} 