<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Api\V3\ApiResponse;
use Illuminate\Http\Request;

class ApiReturnReasonsController extends ApiResponse
{
    /**
     * Get all return reasons
     */
    public function index()
    {
        try {
            $returnReasons = [
                [
                    'id' => 1,
                    'name' => translate('Defective or damaged item'),
                    'description' => translate('The item received was defective or damaged'),
                ],
                [
                    'id' => 2,
                    'name' => translate('Wrong item received'),
                    'description' => translate('The item received was different from what was ordered'),
                ],
                [
                    'id' => 3,
                    'name' => translate('Size/fit issues'),
                    'description' => translate('The item does not fit properly or is the wrong size'),
                ],
                [
                    'id' => 4,
                    'name' => translate('Quality not as expected'),
                    'description' => translate('The quality of the item did not meet expectations'),
                ],
                [
                    'id' => 5,
                    'name' => translate('Changed mind'),
                    'description' => translate('No longer want or need the item'),
                ],
                [
                    'id' => 6,
                    'name' => translate('Item not as described'),
                    'description' => translate('The item description was inaccurate or misleading'),
                ],
                [
                    'id' => 7,
                    'name' => translate('Late delivery'),
                    'description' => translate('The item was delivered later than expected'),
                ],
                [
                    'id' => 8,
                    'name' => translate('Better price found elsewhere'),
                    'description' => translate('Found the same item at a better price'),
                ],
                [
                    'id' => 9,
                    'name' => translate('Ordered by mistake'),
                    'description' => translate('The item was ordered accidentally'),
                ],
                [
                    'id' => 10,
                    'name' => translate('Other'),
                    'description' => translate('Other reason not listed above'),
                ],
            ];

            return $this->success(
                $returnReasons,
                translate('Return reasons retrieved successfully')
            );
        } catch (\Exception $e) {
            return $this->error(
                'INTERNAL_SERVER_ERROR',
                translate('Failed to retrieve return reasons'),
                500
            );
        }
    }
} 