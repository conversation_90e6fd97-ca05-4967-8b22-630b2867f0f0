<?php

namespace App\Http\Controllers\Api\V3;

use App\Enums\RefundStatus;
use App\Enums\ReturnStatus;
use App\Http\Controllers\Controller;
use App\Http\Resources\V3\Refund\RefundFeedbackResource;
use App\Http\Resources\V3\Refund\RefundRequestResource;
use App\Http\Resources\V3\Rerurn\RerurnRequestResource;
use App\Http\Resources\V3\Refund\RefundRequestsResource;
use App\Http\Resources\V3\Rerurn\RerurnRequestsResource;
use App\Http\Resources\V3\ReturnMethods\ReturnEligibilityResource;
use App\Http\Resources\V3\ReturnMethods\ReturnFeedbackResource;
use App\Models\Order;
use App\Models\RefundRequestInfo;
use App\Models\RefundRequestProduct;
use App\Models\ReturnRequestInfo;
use App\Models\ReturnRequestProduct;
use App\Services\ActivityLogService;
use App\Services\ApiRefundService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class ApiRefundController extends ApiResponse
{
    /**
     * @var \App\Services\ApiRefundService
     */
    protected ApiRefundService $apiRefundService;
    protected ActivityLogService $activityLogService;
    public function __construct(
        ApiRefundService $apiRefundService,
        ActivityLogService $activityLogService,
    )
    {
        parent::__construct();
        $this->apiRefundService = $apiRefundService;
        $this->activityLogService = $activityLogService;
    }
    public function index(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'sort' => 'nullable|in:newest,oldest',
            'date_from' => 'nullable|date_format:Y-m-d',
            'date_to' => 'nullable|date_format:Y-m-d|after_or_equal:date_from',
            'search' => 'nullable|string|max:255',
            'status' => 'nullable|string|in:' . implode(',', ReturnStatus::getValues())
        ]);
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        try {
            $per_page = min((int) $request->input('per_page', 20), 50);
            $page = max((int) $request->input('page', 1), 1);
            $sort_by = in_array($request->input('sort_by'), ['newest', 'oldest']) ? $request->input('sort') : 'newest';
            $start_date = $request->input('date_from');
            $end_date = $request->input('date_to');
            $search = $request->input('search');
            $status = $request->input('status');

            $query = RefundRequestInfo::with('order', 'refund_request_products', 'refund_request_products.product')
                ->where('user_id', auth()->user()->id);

            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->whereHas('order', function ($q) use ($search) {
                        $q->where('code', 'like', '%' . $search . '%');
                    })->orWhereHas('refund_request_products', function ($q) use ($search) {
                        $q->whereHas('product', function ($q) use ($search) {
                            $q->where('name', 'like', '%' . $search . '%');
                        });
                    });
                });
            }

            if ($status) {
                $query->where('refund_requests.status', $status);
            }

            if ($start_date && $end_date) {
                $query->whereBetween('refund_requests.created_at', [$start_date, $end_date]);
            }

            switch ($sort_by) {
                case 'oldest':
                    $query->orderBy('created_at', 'asc');
                    break;
                default: // date_desc
                    $query->orderBy('created_at', 'desc');
            }
            $returnRequests = $query->paginate($per_page);
            $total_items = $returnRequests->total();
            $total_pages = $returnRequests->lastPage();

            $data = [
                'orders' => new RefundRequestsResource($returnRequests),
                'pagination' => [
                    'currentPage' => (int) $page,
                    'totalPages' => $total_pages,
                    'totalItems' => $total_items,
                    'itemsPerPage' => (int) $per_page,
                ]
            ];
            return $this->success($data);
        } catch (QueryException $e) {
            Log::channel('api_order')->error('Unexpected error while fetching orders list : ApiOrdersController ' . print_r($e->getMessage(), true));
            return $this->error('A database error occurred. Please try again later.', 500);
        } catch (ModelNotFoundException $e) {
            Log::channel('api_order')->error('Unexpected error while fetching orders list : ApiOrdersController ' . print_r($e->getMessage(), true));
            return $this->error('Requested resource not found.', 404);
        } catch (\Exception $e) {
            Log::channel('api_order')->error('Unexpected error while fetching orders list : ApiOrdersController ' . print_r($e->getMessage(), true));
            return $this->error('Something went wrong. Please try again later.', 500);
        }
    }
    public function refund_request_details(Request $request, $return_code)
    {
        $returnRequestInfo = RefundRequestInfo::with('order', 'refund_request_products', 'refund_request_products.product')
            ->where('refund_code', $return_code)->first();
        if (!$returnRequestInfo) {
            return $this->error(
                'VALIDATION_ERROR.',
                'Return Request not found.',
                '',
                404
            );
        }
        return $this->success(
            new RefundRequestResource($returnRequestInfo),
            'Success Fully fetched return request details',
        );
    }
    /**
     * Get available return methods for an order
     *
     * @param Request $request
     * @param string $orderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getRefundMethods(Request $request, $orderId)
    {
        try {
            $order = Order::where('code', $orderId)->firstOrFail();

            // Check if the order belongs to the authenticated user
            if ($order->user_id !== auth()->id()) {
                return $this->error(
                    'UNAUTHORIZED',
                    'You are not authorized to access this order',
                    '',
                    403
                );
            }

            return $this->apiRefundService->getRefundMethodsForOrder($order);

        } catch (ModelNotFoundException $e) {
            return $this->error(
                'NOT_FOUND',
                'Order not found',
                '',
                404
            );
        } catch (\Exception $e) {
            Log::channel('api_order')->error('Error in getReturnMethods: ' . $e->getMessage());
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while retrieving return methods',
                $e->getMessage(),
                500
            );
        }
    }
    public function save_order_return_request(Request $request, Order $order)
    {
        // Convert JSON string to array if it's a string
        $itemIds = is_string($request->input('itemIds')) ? json_decode($request->input('itemIds'), true) : $request->input('itemIds');

        $messages = array(
            'itemIds.required' => translate('Please provide at least one item ID'),
            'itemIds.array' => translate('Item IDs must be in an array'),
            'itemIds.*.exists' => translate('Some item IDs are invalid or not found in order details'),
        );


        $request->merge([
            'itemIds' => $itemIds
        ]);

        $validator = Validator::make($request->all(), [
            'itemIds' => 'required|array',
            'itemIds.*' => [
                'required',
                'integer',
                Rule::exists('order_details', 'product_id')
                    ->where(function ($query) use ($order) {
                        $query->where('order_id', $order->id);
                    })
            ],
            'reason_for_return' => 'required|string',
            'acceptTerms' => 'accepted',
            'attachments' => 'nullable|array',
            'attachments.*.filename' => 'required_with:attachments|string',
            'attachments.*.base64Content' => 'required_with:attachments|string|starts_with:data:image/',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid credentials',
                $validator->errors()->messages(),
                400
            );
        }

        $reason_for_refund = $request->input('reason_for_refund');

        $refundMethod = $request->input('refundMethod');
        $order_details = $order->orderDetails()->whereIn('product_id', $itemIds)
            ->where('order_id', $order->id)->get();
        $checkExists = RefundRequestProduct::whereIn('order_detail_id', $order_details->pluck('id')->toArray())->get();
        /*if($checkExists->count() > 0){
            return $this->error(
                'INVALID_REQUEST',
                'This order is already requested for refund',
                400
            );
        }*/
        try {
            DB::beginTransaction();
            $attachments = '';
            if ($request->hasFile('attachments')) {
                $upload = new \App\Utility\ApiAizUploadUtility();
                $result = $upload->multipleFileUpload($request->file('attachments'), auth()->user()->id);
                if ($result['success'] == true) {
                    $attachments = $result['files'];
                }
            }
            $return_refund_request = new RefundRequestInfo();
            $return_refund_request->refund_code = 'REF-' . date('Ymd-His') . rand(10, 99);
            $return_refund_request->user_id = auth()->user()->id;
            $return_refund_request->order_id = $order->id;
            $return_refund_request->request_date = strtotime('now');
            $return_refund_request->amount = $order_details->sum('price');
            $return_refund_request->admin_note = Null;
            $return_refund_request->reason_for_refund = $reason_for_refund;
            $return_refund_request->refund_status = RefundStatus::PENDING;
            $return_refund_request->attachments = $attachments;
            $return_refund_request->refundMethod = $refundMethod;
            $return_refund_request->save();

            foreach ($itemIds as $item_id) {
                $order_detail = $order->orderDetails()->where('product_id', $item_id)->first();
                $return_refund_request->refund_request_products()->create([
                    'refund_request_info_id' => $return_refund_request->id,
                    'order_detail_id' => $order_detail->id,
                    'product_id' => $order_detail->product_id,
                    'quantity' => $order_detail->quantity,
                    'unit_price' => $order_detail->unit_price,
                    'amount' => $order_detail->quantity * $order_detail->unit_price,
                    'refund_status' => RefundStatus::PENDING,
                    'seller_id' => $order_detail->seller_id,
                    'seller_approval' => 0,
                    'admin_note_for_product' => Null,
                    'seller_note_for_product' => Null,
                ]);
            }

            $this->activityLogService->log(
                'return_request',
                'Return Request Placed',
                $return_refund_request->id,
                RefundRequestInfo::class,
                auth()->user()->id,
                get_class(auth()->user()),
                '',
                RefundStatus::PENDING,
                $reason_for_refund,
                email_end_time: null,
            );


            DB::commit();

            return $this->success(
                new RefundRequestResource($return_refund_request),
                'Return Request Placed',
            );
        } catch (\Exception $e) {
            dd($e);
            Log::channel('api_order')->error('Error in ApiOrdersController : AuthController ' . print_r($e->getMessage(), true));
            DB::rollBack();
            return $this->error(
                'INTERNAL_SERVER_ERROR',
                $e->getMessage(),
                500
            );
        }
    }
    /**
     * Check if an order is eligible for return
     *
     * @param Request $request
     * @param string $orderId
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkRefundEligibility(Request $request, $orderId)
    {
        try {
            $order = Order::where('code', $orderId)->firstOrFail();

            // Check if the order belongs to the authenticated user
            if ($order->user_id !== auth()->id()) {
                return $this->error(
                    'UNAUTHORIZED',
                    'You are not authorized to access this order',
                    '',
                    403
                );
            }

            // Get return eligibility details
            $eligibilityData = $this->apiRefundService->getRefundEligibility($order);

            return $this->success(
                new ReturnEligibilityResource($eligibilityData),
                'Return eligibility details retrieved successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->error(
                'NOT_FOUND',
                'Order not found',
                '',
                404
            );
        } catch (\Exception $e) {
            Log::channel('api_order')->error('Error in checkReturnEligibility: ' . $e->getMessage());
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while checking return eligibility',
                $e->getMessage(),
                500
            );
        }
    }
    public function refund_request_update(Request $request, $return_code)
    {
        $returnRequestInfo = RefundRequestInfo::with('order', 'refund_request_products', 'refund_request_products.product')
            ->where('refund_code', $return_code)->first();
        if (!$returnRequestInfo) {
            return $this->error(
                'VALIDATION_ERROR.',
                'Return Request not found.',
                '',
                404
            );
        }
        if($returnRequestInfo->refund_status !== RefundStatus::PENDING){
            return $this->error(
                'INVALID_REQUEST',
                'This return request is already processed',
                400
            );
        }
        $validator = Validator::make($request->all(), [
            'refundMethod' => 'required|string',
            'additionalInformation' => 'nullable|string',
            'attachments' => 'nullable|array',
            'attachments.*.filename' => 'required_with:attachments|string',
            'attachments.*.base64Content' => 'required_with:attachments|string|starts_with:data:image/',
        ]);
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid credentials',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            DB::beginTransaction();
            $attachments = '';
            if ($request->hasFile('attachments')) {
                $upload = new \App\Utility\ApiAizUploadUtility();
                $result = $upload->multipleFileUpload($request->file('attachments'), auth()->user()->id);
                if ($result['success'] == true) {
                    $attachments = $result['files'];
                    $returnRequestInfo->attachments = $attachments;
                }
            }

            $description = $request->input('additionalInformation');
            $refundMethod = $request->input('refundMethod');
            if($description !== null ||$description !== '' ){
                $returnRequestInfo->user_note = $description;
            }
            $returnRequestInfo->refundMethod = $refundMethod;
            $returnRequestInfo->save();

            DB::commit();

            return $this->success(
                new RefundRequestResource($returnRequestInfo),
                'Return Request Updated',
            );
        } catch (\Exception $e) {
            Log::channel('api_order')->error('Error in ApiOrdersController : AuthController ' . print_r($e->getMessage(), true));
            DB::rollBack();
            return $this->error(
                'INTERNAL_SERVER_ERROR',
                $e->getMessage(),
                500
            );
        }
    }
    public function refund_request_cancel(Request $request, $return_code)
    {
        $returnRequestInfo = RefundRequestInfo::with('order', 'refund_request_products', 'refund_request_products.product')
            ->where('refund_code', $return_code)->first();
        if (!$returnRequestInfo) {
            return $this->error(
                'VALIDATION_ERROR.',
                'Return Request not found.',
                '',
                404
            );
        }
        if($returnRequestInfo->refund_status !== RefundStatus::PENDING){
            return $this->error(
                'INVALID_REQUEST',
                'This return request is already processed',
                400
            );
        }
        $description = $request->input('note');
        if($description !== null ||$description !== '' ){
            $returnRequestInfo->user_note = $description;
        }
        $returnRequestInfo->refund_status = RefundStatus::CANCELLED_BY_USER;
        $returnRequestInfo->save();
        return $this->success(
            new RefundRequestResource($returnRequestInfo),
            'Return Request Cancelled',
        );
    }
    /**
     * Submit feedback for a completed return
     *
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function submitFeedback(Request $request, $id)
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'satisfactionRating' => 'required|integer|min:1|max:5',
                'processRating' => 'required|integer|min:1|max:5',
                'serviceRating' => 'required|integer|min:1|max:5',
                'comments' => 'nullable|string|max:1000',
                'wouldRecommend' => 'required|boolean',
            ]);

            if ($validator->fails()) {
                return $this->validation_error(
                    'VALIDATION_ERROR',
                    'Please provide valid information',
                    $validator->errors()->messages(),
                    400
                );
            }

            // Find the return request
            $returnRequest = RefundRequestInfo::where('refund_code', $id)->firstOrFail();

            // Check if the return request belongs to the authenticated user
            if ($returnRequest->user_id !== auth()->id()) {
                return $this->error(
                    'UNAUTHORIZED',
                    'You are not authorized to access this return request',
                    '',
                    403
                );
            }

            // Check if the return request is completed
            if ($returnRequest->refund_status !== ReturnStatus::COMPLETED) {
                return $this->error(
                    'NOT_COMPLETED',
                    'Feedback can only be submitted for completed returns',
                    '',
                    400
                );
            }

            // Check if feedback has already been submitted
            if (!empty($returnRequest->feedback)) {
                return $this->error(
                    'FEEDBACK_ALREADY_SUBMITTED',
                    'Feedback has already been submitted for this return',
                    '',
                    400
                );
            }

            // Submit the feedback
            $feedbackData = $this->apiRefundService->submitFeedback($returnRequest, $request->all());

            if (!$feedbackData) {
                return $this->error(
                    'FEEDBACK_SUBMISSION_FAILED',
                    'Failed to submit feedback',
                    '',
                    500
                );
            }

            return $this->success(
                new RefundFeedbackResource($feedbackData),
                'Feedback submitted successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->error(
                'NOT_FOUND',
                'Return request not found',
                '',
                404
            );
        } catch (\Exception $e) {
            Log::channel('api_order')->error('Error in submitFeedback: ' . $e->getMessage());
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while submitting feedback',
                $e->getMessage(),
                500
            );
        }
    }
}
