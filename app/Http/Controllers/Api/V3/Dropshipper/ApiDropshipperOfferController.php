<?php

namespace App\Http\Controllers\Api\V3\Dropshipper;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\Dropshipper\DropshipperOfferResource;
use App\Models\DropshipperOffer;
use App\Services\DropshipperOfferService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class ApiDropshipperOfferController extends ApiResponse
{
    protected DropshipperOfferService $offerService;

    /**
     * Create a new controller instance.
     *
     * @param DropshipperOfferService $offerService
     * @return void
     */
    public function __construct(DropshipperOfferService $offerService)
    {
        parent::__construct();
        $this->offerService = $offerService;
    }

    /**
     * Get a paginated list of all offers with optional filtering
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // Validate request parameters
            $validator = Validator::make($request->all(), [
                'page' => 'nullable|integer|min:1',
                'perPage' => 'nullable|integer|min:1|max:100',
                'search' => 'nullable|string|max:255',
                'isActive' => 'nullable|boolean',
                'isExclusive' => 'nullable|boolean',
                'isSeasonal' => 'nullable|boolean',
                'isPersonalized' => 'nullable|boolean',
                'isdropshipperOnly' => 'nullable|boolean',
                'sortBy' => 'nullable|string|in:priority,start_date,end_date,created_at',
                'sortDirection' => 'nullable|string|in:asc,desc',
                'userType' => 'nullable|string|in:all,customer,seller,dropshipper'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            // Get current user type
            $userType = $this->getUserType();
            
            // Get offers with filters
            $filters = $request->only([
                'page',
                'perPage',
                'search',
                'isActive',
                'isExclusive',
                'isSeasonal',
                'isPersonalized',
                'isdropshipperOnly',
                'sortBy',
                'sortDirection'
            ]);
            
            // Filter by user type if not specified in request
            if (!$request->has('userType')) {
                $filters['userType'] = $userType;
            } else {
                $filters['userType'] = $request->userType;
            }

            $offers = $this->offerService->getOffers($filters);

            // Return paginated response
            return $this->success([
                'offers' => DropshipperOfferResource::collection($offers->items()),
                'meta' => [
                    'currentPage' => $offers->currentPage(),
                    'from' => $offers->firstItem(),
                    'lastPage' => $offers->lastPage(),
                    'perPage' => $offers->perPage(),
                    'to' => $offers->lastItem(),
                    'total' => $offers->total()
                ]
            ]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to retrieve offers',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get a single offer by ID
     *
     * @param string $id
     * @return JsonResponse
     */
    public function show(string $id): JsonResponse
    {
        try {
            $offer = $this->offerService->getOfferById($id);

            if (!$offer) {
                return $this->error(
                    'NOT_FOUND',
                    'Offer not found',
                    null,
                    404
                );
            }
            
            // Check if offer is applicable for current user type
            $userType = $this->getUserType();
            if (!$offer->isApplicableForUserType($userType) && $userType !== 'admin') {
                return $this->error(
                    'FORBIDDEN',
                    'You do not have access to this offer',
                    null,
                    403
                );
            }

            return $this->success(
                new DropshipperOfferResource($offer)
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to retrieve offer',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get offers by filter
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getByFilter(Request $request): JsonResponse
    {
        try {
            // Validate request parameters
            $validator = Validator::make($request->all(), [
                'filter' => 'required|string|in:all,personalized,seasonal,expiring,exclusive',
                'limit' => 'nullable|integer|min:1|max:100',
                'userType' => 'nullable|string|in:all,customer,seller,dropshipper'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            $filter = $request->input('filter');
            $limit = $request->input('limit', 10);
            
            // Get current user type if not specified
            $userType = $request->input('userType', $this->getUserType());

            $offers = $this->offerService->getOffersByFilter($filter, $limit, $userType);

            return $this->success(
                DropshipperOfferResource::collection($offers)
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to retrieve filtered offers',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get top priority offers
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getTopPriority(Request $request): JsonResponse
    {
        try {
            // Validate request parameters
            $validator = Validator::make($request->all(), [
                'limit' => 'nullable|integer|min:1|max:20',
                'userType' => 'nullable|string|in:all,customer,seller,dropshipper'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            $limit = $request->input('limit', 3);
            $userType = $request->input('userType', $this->getUserType());

            $offers = $this->offerService->getTopPriorityOffers($limit, $userType);

            return $this->success(
                DropshipperOfferResource::collection($offers)
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to retrieve top priority offers',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get exclusive offers
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getExclusive(Request $request): JsonResponse
    {
        try {
            // Validate request parameters
            $validator = Validator::make($request->all(), [
                'limit' => 'nullable|integer|min:1|max:20',
                'userType' => 'nullable|string|in:all,customer,seller,dropshipper'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            $limit = $request->input('limit', 10);
            $userType = $request->input('userType', $this->getUserType());

            $offers = $this->offerService->getExclusiveOffers($limit, $userType);

            return $this->success(
                DropshipperOfferResource::collection($offers)
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to retrieve exclusive offers',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get expiring offers
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getExpiring(Request $request): JsonResponse
    {
        try {
            // Validate request parameters
            $validator = Validator::make($request->all(), [
                'days' => 'nullable|integer|min:1|max:30',
                'limit' => 'nullable|integer|min:1|max:20',
                'userType' => 'nullable|string|in:all,customer,seller,dropshipper'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            $days = $request->input('days', 7);
            $limit = $request->input('limit', 10);
            $userType = $request->input('userType', $this->getUserType());

            $offers = $this->offerService->getExpiringOffers($days, $limit, $userType);

            return $this->success(
                DropshipperOfferResource::collection($offers)
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to retrieve expiring offers',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Search offers
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function search(Request $request): JsonResponse
    {
        try {
            // Validate request parameters
            $validator = Validator::make($request->all(), [
                'query' => 'required|string|min:2|max:100',
                'limit' => 'nullable|integer|min:1|max:50',
                'userType' => 'nullable|string|in:all,customer,seller,dropshipper'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }

            $query = $request->input('query');
            $limit = $request->input('limit', 10);
            $userType = $request->input('userType', $this->getUserType());

            $offers = $this->offerService->searchOffers($query, $limit, $userType);

            return $this->success(
                DropshipperOfferResource::collection($offers)
            );
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to search offers',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Apply promo code
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function applyPromoCode(Request $request): JsonResponse
    {
        try {
            // Validate request parameters
            $validator = Validator::make($request->all(), [
                'promo_code' => 'required|string|max:50',
                'cart_total' => 'required|numeric|min:0',
                'product_ids' => 'nullable|array',
                'product_ids.*' => 'integer|exists:products,id',
                'category_ids' => 'nullable|array',
                'category_ids.*' => 'integer|exists:categories,id'
            ]);

            if ($validator->fails()) {
                return $this->error(
                    'VALIDATION_ERROR',
                    'Validation failed',
                    $validator->errors(),
                    422
                );
            }
            
            $userType = $this->getUserType();
            $promoCode = $request->input('promo_code');
            $cartTotal = $request->input('cart_total');
            $productIds = $request->input('product_ids', []);
            $categoryIds = $request->input('category_ids', []);

            $result = $this->offerService->applyPromoCode($promoCode, $cartTotal, $productIds, $categoryIds, $userType);

            if (!$result['valid']) {
                return $this->error(
                    'INVALID_PROMO_CODE',
                    $result['message'],
                    null,
                    400
                );
            }

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to apply promo code',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Determine the current user type
     * 
     * @return string
     */
    protected function getUserType(): string
    {
        if (!Auth::check()) {
            return 'customer'; // Default for guests
        }
        
        $user = Auth::user();
        
        if ($user->user_type == 'admin' || $user->user_type == 'staff') {
            return 'admin';
        } elseif ($user->user_type == 'seller') {
            return 'seller';
        } elseif ($user->user_type == 'dropshipper') {
            return 'dropshipper';
        } else {
            return 'customer';
        }
    }
} 