<?php

namespace App\Http\Controllers\Api\V3\Dropshipper;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Wishlist;
use App\Models\Product;
use App\Models\User;
use Auth;
use App\Http\Controllers\Api\V3\ApiResponse;

class ApiDropshipperWishlistController extends Controller
{
    protected $apiResponse;

    public function __construct()
    {
        $this->apiResponse = new ApiResponse();
    }

    /**
     * Get dropshipper's wishlist
     */
    public function getWishlist(Request $request)
    {
        try {
            $user = Auth::user();
            
            if (!$user || !$user->isDropshipper()) {
                return $this->apiResponse->notAuthorizedResponse();
            }

            $page = $request->input('page', 1);
            $limit = $request->input('perPage', 10);
            $search = $request->input('search', '');
            $sortBy = $request->input('sortBy', 'created_at');
            $sortOrder = $request->input('sortOrder', 'desc');
            $category = $request->input('category', '');
            $minPrice = $request->input('minPrice', 0);
            $maxPrice = $request->input('maxPrice', 0);
            $inStock = $request->input('inStock', null);

            $query = Wishlist::with(['product'])
                ->where('user_id', $user->id);

            // Apply filters
            if (!empty($search)) {
                $query->whereHas('product', function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%");
                });
            }
            
            if (!empty($category)) {
                $query->whereHas('product', function ($q) use ($category) {
                    $q->where('category_id', $category);
                });
            }
            
            if ($minPrice > 0) {
                $query->whereHas('product', function ($q) use ($minPrice) {
                    $q->where('unit_price', '>=', $minPrice);
                });
            }
            
            if ($maxPrice > 0) {
                $query->whereHas('product', function ($q) use ($maxPrice) {
                    $q->where('unit_price', '<=', $maxPrice);
                });
            }
            
            if ($inStock !== null) {
                $query->whereHas('product', function ($q) use ($inStock) {
                    $q->where('current_stock', '>', 0);
                });
            }
            
            // Apply sorting
            if ($sortBy === 'price_asc') {
                $query->join('products', 'wishlists.product_id', '=', 'products.id')
                      ->orderBy('products.unit_price', 'asc');
            } elseif ($sortBy === 'price_desc') {
                $query->join('products', 'wishlists.product_id', '=', 'products.id')
                      ->orderBy('products.unit_price', 'desc');
            } elseif ($sortBy === 'name_asc') {
                $query->join('products', 'wishlists.product_id', '=', 'products.id')
                      ->orderBy('products.name', 'asc');
            } elseif ($sortBy === 'name_desc') {
                $query->join('products', 'wishlists.product_id', '=', 'products.id')
                      ->orderBy('products.name', 'desc');
            } elseif ($sortBy === 'rating') {
                $query->join('products', 'wishlists.product_id', '=', 'products.id')
                      ->orderBy('products.rating', 'desc');
            } elseif ($sortBy === 'profit_margin') {
                // Calculate profit margin for dropshippers
                $query->join('products', 'wishlists.product_id', '=', 'products.id')
                      ->orderBy('products.dropshipper_price', 'desc');
            } else {
                $query->orderBy($sortBy, $sortOrder);
            }

            $totalItems = $query->count();
            $wishlistItems = $query->paginate($limit, ['*'], 'page', $page);
            
            $formattedItems = $this->formatWishlistItems($wishlistItems);
            
            return $this->apiResponse->successResponse([
                'wishlist' => $formattedItems,
                'pagination' => [
                    'currentPage' => $wishlistItems->currentPage(),
                    'lastPage' => $wishlistItems->lastPage(),
                    'perPage' => $wishlistItems->perPage(),
                    'total' => $wishlistItems->total(),
                    'totalPages' => ceil($totalItems / $limit),
                    'itemsPerPage' => $limit
                ]
            ]);
        } catch (\Exception $e) {
            return $this->apiResponse->errorResponse('Error fetching wishlist: ' . $e->getMessage());
        }
    }
    
    /**
     * Format wishlist items for API response with dropshipper-specific fields
     */
    protected function formatWishlistItems($wishlistItems)
    {
        $formattedItems = [];
        
        foreach ($wishlistItems as $item) {
            if ($item->product) {
                // Calculate profit margin for dropshippers
                $retailPrice = home_discounted_price($item->product->id);
                $dropshipperPrice = $item->product->dropshipper_price ?? $retailPrice;
                $profitMargin = $retailPrice > 0 ? (($retailPrice - $dropshipperPrice) / $retailPrice) * 100 : 0;
                
                $formattedItems[] = [
                    'id' => $item->id,
                    'productId' => $item->product_id,
                    'name' => $item->product->name,
                    'slug' => $item->product->slug,
                    'retailPrice' => (float) $item->product->unit_price,
                    'discountedPrice' => (float) home_discounted_price($item->product->id),
                    'dropshipperPrice' => (float) $dropshipperPrice,
                    'profitMargin' => round($profitMargin, 2),
                    'image' => api_asset($item->product->thumbnail_img),
                    'category' => optional($item->product->category)->name,
                    'categoryId' => $item->product->category_id,
                    'rating' => (float) $item->product->rating,
                    'inStock' => $item->product->current_stock > 0,
                    'stock' => (int) $item->product->current_stock,
                    'dateAdded' => $item->created_at->format('Y-m-d H:i:s'),
                    'supplier' => optional($item->product->user)->name,
                    'availableForDropship' => $item->product->is_dropship_enabled ?? false
                ];
            }
        }
        
        return $formattedItems;
    }
    
    /**
     * Add product to wishlist
     */
    public function addToWishlist(Request $request)
    {
        try {
            $user = Auth::user();
            
            if (!$user || !$user->isDropshipper()) {
                return $this->apiResponse->notAuthorizedResponse();
            }
            
            $productId = $request->input('product_id');
            
            // Validate product ID
            if (!$productId) {
                return $this->apiResponse->validationErrorResponse('Product ID is required');
            }
            
            // Check if product exists
            $product = Product::find($productId);
            if (!$product) {
                return $this->apiResponse->notFoundResponse('Product not found');
            }
            
            // Check if product is available for dropshipping
            if (!($product->is_dropship_enabled ?? false)) {
                return $this->apiResponse->validationErrorResponse('This product is not available for dropshipping');
            }
            
            // Check if product is already in wishlist
            $existingWishlistItem = Wishlist::where('user_id', $user->id)
                ->where('product_id', $productId)
                ->first();
                
            if ($existingWishlistItem) {
                return $this->apiResponse->successResponse([
                    'message' => 'Product is already in your wishlist',
                    'wishlistItemId' => $existingWishlistItem->id
                ]);
            }
            
            // Add to wishlist
            $wishlistItem = new Wishlist();
            $wishlistItem->user_id = $user->id;
            $wishlistItem->product_id = $productId;
            $wishlistItem->save();
            
            return $this->apiResponse->successResponse([
                'message' => 'Product added to wishlist successfully',
                'wishlistItemId' => $wishlistItem->id
            ]);
        } catch (\Exception $e) {
            return $this->apiResponse->errorResponse('Error adding product to wishlist: ' . $e->getMessage());
        }
    }
    
    /**
     * Delete item from wishlist
     */
    public function removeFromWishlist($id)
    {
        try {
            $user = Auth::user();
            
            if (!$user || !$user->isDropshipper()) {
                return $this->apiResponse->notAuthorizedResponse();
            }
            
            $wishlistItem = Wishlist::where('id', $id)
                ->where('user_id', $user->id)
                ->first();
                
            if (!$wishlistItem) {
                return $this->apiResponse->notFoundResponse('Wishlist item not found');
            }
            
            $wishlistItem->delete();
            
            return $this->apiResponse->successResponse([
                'message' => 'Item removed from wishlist successfully'
            ]);
        } catch (\Exception $e) {
            return $this->apiResponse->errorResponse('Error removing item from wishlist: ' . $e->getMessage());
        }
    }
    
    /**
     * Clear entire wishlist
     */
    public function clearWishlist()
    {
        try {
            $user = Auth::user();
            
            if (!$user || !$user->isDropshipper()) {
                return $this->apiResponse->notAuthorizedResponse();
            }
            
            Wishlist::where('user_id', $user->id)->delete();
            
            return $this->apiResponse->successResponse([
                'message' => 'Wishlist cleared successfully'
            ]);
        } catch (\Exception $e) {
            return $this->apiResponse->errorResponse('Error clearing wishlist: ' . $e->getMessage());
        }
    }
    
    /**
     * Check if product is in wishlist
     */
    public function checkWishlistStatus(Request $request)
    {
        try {
            $user = Auth::user();
            
            if (!$user || !$user->isDropshipper()) {
                return $this->apiResponse->notAuthorizedResponse();
            }
            
            $productId = $request->input('product_id');
            
            if (!$productId) {
                return $this->apiResponse->validationErrorResponse('Product ID is required');
            }
            
            $wishlistItem = Wishlist::where('user_id', $user->id)
                ->where('product_id', $productId)
                ->first();
                
            return $this->apiResponse->successResponse([
                'inWishlist' => !!$wishlistItem,
                'wishlistItemId' => $wishlistItem ? $wishlistItem->id : null
            ]);
        } catch (\Exception $e) {
            return $this->apiResponse->errorResponse('Error checking wishlist status: ' . $e->getMessage());
        }
    }
    
    /**
     * Get wishlist statistics
     */
    public function getWishlistStats()
    {
        try {
            $user = Auth::user();
            
            if (!$user || !$user->isDropshipper()) {
                return $this->apiResponse->notAuthorizedResponse();
            }
            
            $wishlistItems = Wishlist::with('product')
                ->where('user_id', $user->id)
                ->get();
                
            $totalItems = $wishlistItems->count();
            $totalRetailValue = 0;
            $totalDropshipperValue = 0;
            $totalPotentialProfit = 0;
            
            $categoryCount = [];
            $supplierCount = [];
            
            foreach ($wishlistItems as $item) {
                if ($item->product) {
                    // Calculate values and potential profit
                    $retailPrice = home_discounted_price($item->product->id);
                    $dropshipperPrice = $item->product->dropshipper_price ?? $retailPrice;
                    $potentialProfit = $retailPrice - $dropshipperPrice;
                    
                    $totalRetailValue += $retailPrice;
                    $totalDropshipperValue += $dropshipperPrice;
                    $totalPotentialProfit += $potentialProfit;
                    
                    // Count categories
                    if ($item->product->category_id) {
                        $categoryId = $item->product->category_id;
                        $categoryName = optional($item->product->category)->name ?? 'Unknown Category';
                        
                        if (!isset($categoryCount[$categoryId])) {
                            $categoryCount[$categoryId] = [
                                'categoryId' => $categoryId,
                                'categoryName' => $categoryName,
                                'count' => 0,
                                'potentialProfit' => 0
                            ];
                        }
                        
                        $categoryCount[$categoryId]['count']++;
                        $categoryCount[$categoryId]['potentialProfit'] += $potentialProfit;
                    }
                    
                    // Count suppliers
                    if ($item->product->user_id) {
                        $supplierId = $item->product->user_id;
                        $supplierName = optional($item->product->user)->name ?? 'Unknown Supplier';
                        
                        if (!isset($supplierCount[$supplierId])) {
                            $supplierCount[$supplierId] = [
                                'supplierId' => $supplierId,
                                'supplierName' => $supplierName,
                                'count' => 0,
                                'potentialProfit' => 0
                            ];
                        }
                        
                        $supplierCount[$supplierId]['count']++;
                        $supplierCount[$supplierId]['potentialProfit'] += $potentialProfit;
                    }
                }
            }
            
            // Sort categories and suppliers by potential profit
            usort($categoryCount, function($a, $b) {
                return $b['potentialProfit'] - $a['potentialProfit'];
            });
            
            usort($supplierCount, function($a, $b) {
                return $b['potentialProfit'] - $a['potentialProfit'];
            });
            
            // Get top categories and suppliers
            $mostProfitableCategories = array_values(array_slice($categoryCount, 0, 5));
            $mostProfitableSuppliers = array_values(array_slice($supplierCount, 0, 5));
            
            return $this->apiResponse->successResponse([
                'totalItems' => $totalItems,
                'totalRetailValue' => round($totalRetailValue, 2),
                'totalDropshipperValue' => round($totalDropshipperValue, 2),
                'totalPotentialProfit' => round($totalPotentialProfit, 2),
                'averageProfitMargin' => $totalRetailValue > 0 ? 
                    round(($totalPotentialProfit / $totalRetailValue) * 100, 2) : 0,
                'mostProfitableCategories' => $mostProfitableCategories,
                'mostProfitableSuppliers' => $mostProfitableSuppliers
            ]);
        } catch (\Exception $e) {
            return $this->apiResponse->errorResponse('Error fetching wishlist stats: ' . $e->getMessage());
        }
    }
    
    /**
     * Move item to compare list
     */
    public function moveToCompare($id)
    {
        try {
            $user = Auth::user();
            
            if (!$user || !$user->isDropshipper()) {
                return $this->apiResponse->notAuthorizedResponse();
            }
            
            $wishlistItem = Wishlist::where('id', $id)
                ->where('user_id', $user->id)
                ->first();
                
            if (!$wishlistItem) {
                return $this->apiResponse->notFoundResponse('Wishlist item not found');
            }
            
            // Add to compare logic here
            // This is a stub - you would integrate with your compare list system
            // For example: $compare->addItem($wishlistItem->product_id);
            
            return $this->apiResponse->successResponse([
                'message' => 'Item moved to compare list successfully',
                'productId' => $wishlistItem->product_id
            ]);
        } catch (\Exception $e) {
            return $this->apiResponse->errorResponse('Error moving item to compare list: ' . $e->getMessage());
        }
    }
} 