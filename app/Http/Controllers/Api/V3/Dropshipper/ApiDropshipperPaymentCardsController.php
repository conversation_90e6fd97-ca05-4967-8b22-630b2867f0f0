<?php

namespace App\Http\Controllers\Api\V3\Dropshipper;

use Illuminate\Http\Request;
use App\Models\StripeCard;
use App\Http\Controllers\Api\V3\ApiResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Stripe\Stripe;
use Stripe\Token;
use Stripe\Customer;
use Stripe\PaymentMethod;
use Stripe\Exception\CardException;
use Stripe\Exception\ApiErrorException;

class ApiDropshipperPaymentCardsController extends ApiResponse
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get all payment cards for the authenticated user
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $user = auth()->user();
            
            $cards = StripeCard::where('user_id', $user->id)
                              ->orderBy('isDefault', 'desc')
                              ->orderBy('created_at', 'desc')
                              ->get()
                              ->map(function($card) {
                                  return [
                                      'id' => (string)$card->id,
                                      'last4' => $card->last4,
                                      'brand' => $card->brand,
                                      'expMonth' => (int)$card->exp_month,
                                      'expYear' => (int)$card->exp_year,
                                      'isDefault' => (bool)$card->isDefault,
                                  ];
                              });
            
            return $this->success($cards);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve payment cards: ' . $e->getMessage());
            return $this->error('RETRIEVAL_ERROR', 'Failed to retrieve payment cards', null, 500);
        }
    }
    
    /**
     * Get details of a specific payment card
     * 
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Request $request, $id)
    {
        try {
            $user = auth()->user();
            
            $card = StripeCard::where('id', $id)
                             ->where('user_id', $user->id)
                             ->first();
            
            if (!$card) {
                return $this->error('NOT_FOUND', 'Payment card not found', null, 404);
            }
            
            $cardData = [
                'id' => (string)$card->id,
                'last4' => $card->last4,
                'brand' => $card->brand,
                'expMonth' => (int)$card->exp_month,
                'expYear' => (int)$card->exp_year,
                'isDefault' => (bool)$card->isDefault,
            ];
            
            return $this->success($cardData);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve payment card: ' . $e->getMessage());
            return $this->error('RETRIEVAL_ERROR', 'Failed to retrieve payment card', null, 500);
        }
    }
    
    /**
     * Add a new payment card
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        try {
            $user = auth()->user();
            
            // Validate input
            $validator = Validator::make($request->all(), [
                'cardNumber' => 'required|string',
                'cardholderName' => 'required|string',
                'expMonth' => 'required|integer|min:1|max:12',
                'expYear' => 'required|integer|min:' . date('Y'),
                'cvc' => 'required|string|min:3|max:4',
            ]);
            
            if ($validator->fails()) {
                return $this->error('VALIDATION_ERROR', $validator->errors()->first(), null, 422);
            }
            
            // Initialize Stripe
            Stripe::setApiKey(env('STRIPE_SECRET'));
            
            // Create a token
            try {
                $token = Token::create([
                    'card' => [
                        'number' => $request->input('cardNumber'),
                        'exp_month' => $request->input('expMonth'),
                        'exp_year' => $request->input('expYear'),
                        'cvc' => $request->input('cvc'),
                        'name' => $request->input('cardholderName'),
                    ],
                ]);
            } catch (CardException $e) {
                return $this->error('CARD_VALIDATION_ERROR', 'Card validation failed: ' . $e->getMessage(), null, 422);
            } catch (ApiErrorException $e) {
                return $this->error('CARD_PROCESSING_ERROR', 'Failed to process card: ' . $e->getMessage(), null, 500);
            }
            
            // Get the card details from the token
            $cardDetails = $token->card;
            
            // Check if the user already has a Stripe customer ID
            if (!$user->stripe_id) {
                // Create a customer in Stripe
                $customer = Customer::create([
                    'email' => $user->email,
                    'name' => $user->name,
                    'source' => $token->id,
                    'metadata' => [
                        'user_id' => $user->id,
                    ],
                ]);
                
                // Store the customer ID
                $user->stripe_id = $customer->id;
                $user->save();
            } else {
                // Add the card to the existing customer
                $customer = Customer::retrieve($user->stripe_id);
                $card = $customer->sources->create(['source' => $token->id]);
            }
            
            // Check if this is the first card (make it default)
            $isFirstCard = StripeCard::where('user_id', $user->id)->count() === 0;
            
            // Prepare billing details
            $billingDetails = [
                'name' => $request->input('cardholderName'),
            ];
            
            // Save the card details to the database
            $paymentCard = new StripeCard();
            $paymentCard->user_id = $user->id;
            $paymentCard->stripe_card_id = $cardDetails->id;
            $paymentCard->last4 = $cardDetails->last4;
            $paymentCard->brand = $cardDetails->brand;
            $paymentCard->exp_month = $cardDetails->exp_month;
            $paymentCard->exp_year = $cardDetails->exp_year;
            $paymentCard->isDefault = $isFirstCard || $request->input('makeDefault', false);
            $paymentCard->type = 'card';
            $paymentCard->billing_details = $billingDetails;
            $paymentCard->save();
            
            // If this card is set as default, unset other default cards
            if ($paymentCard->isDefault) {
                StripeCard::where('user_id', $user->id)
                         ->where('id', '!=', $paymentCard->id)
                         ->update(['isDefault' => false]);
            }
            
            // Return the card data
            $cardData = [
                'id' => (string)$paymentCard->id,
                'last4' => $paymentCard->last4,
                'brand' => $paymentCard->brand,
                'expMonth' => (int)$paymentCard->exp_month,
                'expYear' => (int)$paymentCard->exp_year,
                'isDefault' => (bool)$paymentCard->isDefault,
            ];
            
            return $this->success($cardData, 'Payment card added successfully');
        } catch (\Exception $e) {
            Log::error('Failed to add payment card: ' . $e->getMessage());
            return $this->error('STORAGE_ERROR', 'Failed to add payment card: ' . $e->getMessage(), null, 500);
        }
    }
    
    /**
     * Update a payment card
     * 
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        try {
            $user = auth()->user();
            
            // Validate input
            $validator = Validator::make($request->all(), [
                'cardholderName' => 'required|string',
                'expMonth' => 'required|integer|min:1|max:12',
                'expYear' => 'required|integer|min:' . date('Y'),
            ]);
            
            if ($validator->fails()) {
                return $this->error('VALIDATION_ERROR', $validator->errors()->first(), null, 422);
            }
            
            // Find the card
            $card = StripeCard::where('id', $id)
                             ->where('user_id', $user->id)
                             ->first();
            
            if (!$card) {
                return $this->error('NOT_FOUND', 'Payment card not found', null, 404);
            }
            
            // Initialize Stripe
            Stripe::setApiKey(env('STRIPE_SECRET'));
            
            try {
                // Update card in Stripe
                if ($card->stripe_card_id && $user->stripe_id) {
                    $customer = Customer::retrieve($user->stripe_id);
                    $stripeCard = $customer->sources->retrieve($card->stripe_card_id);
                    
                    $stripeCard->name = $request->input('cardholderName');
                    $stripeCard->exp_month = $request->input('expMonth');
                    $stripeCard->exp_year = $request->input('expYear');
                    $stripeCard->save();
                }
            } catch (ApiErrorException $e) {
                return $this->error('STRIPE_UPDATE_ERROR', 'Failed to update card in Stripe: ' . $e->getMessage(), null, 500);
            }
            
            // Update card in database
            $billingDetails = $card->billing_details ?? [];
            $billingDetails['name'] = $request->input('cardholderName');
            $card->billing_details = $billingDetails;
            $card->exp_month = $request->input('expMonth');
            $card->exp_year = $request->input('expYear');
            $card->save();
            
            // Return the updated card data
            $cardData = [
                'id' => (string)$card->id,
                'last4' => $card->last4,
                'brand' => $card->brand,
                'expMonth' => (int)$card->exp_month,
                'expYear' => (int)$card->exp_year,
                'isDefault' => (bool)$card->isDefault,
            ];
            
            return $this->success($cardData, 'Payment card updated successfully');
        } catch (\Exception $e) {
            Log::error('Failed to update payment card: ' . $e->getMessage());
            return $this->error('UPDATE_ERROR', 'Failed to update payment card', null, 500);
        }
    }
    
    /**
     * Delete a payment card
     * 
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request, $id)
    {
        try {
            $user = auth()->user();
            
            // Find the card
            $card = StripeCard::where('id', $id)
                             ->where('user_id', $user->id)
                             ->first();
            
            if (!$card) {
                return $this->error('NOT_FOUND', 'Payment card not found', null, 404);
            }
            
            // Check if it's the default card
            if ($card->isDefault) {
                // Get another card to set as default
                $newDefaultCard = StripeCard::where('user_id', $user->id)
                                           ->where('id', '!=', $card->id)
                                           ->orderBy('created_at', 'desc')
                                           ->first();
                
                if ($newDefaultCard) {
                    $newDefaultCard->isDefault = true;
                    $newDefaultCard->save();
                }
            }
            
            // Initialize Stripe and delete the card
            try {
                if ($card->stripe_card_id && $user->stripe_id) {
                    Stripe::setApiKey(env('STRIPE_SECRET'));
                    $customer = Customer::retrieve($user->stripe_id);
                    $customer->sources->retrieve($card->stripe_card_id)->delete();
                }
            } catch (ApiErrorException $e) {
                // Log the error but continue with deletion from our database
                Log::error('Failed to delete card from Stripe: ' . $e->getMessage());
            }
            
            // Delete the card from our database
            $card->delete();
            
            return $this->success(null, 'Payment card deleted successfully');
        } catch (\Exception $e) {
            Log::error('Failed to delete payment card: ' . $e->getMessage());
            return $this->error('DELETE_ERROR', 'Failed to delete payment card', null, 500);
        }
    }
    
    /**
     * Set a payment card as default
     * 
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function setDefault(Request $request, $id)
    {
        try {
            $user = auth()->user();
            
            // Find the card
            $card = StripeCard::where('id', $id)
                             ->where('user_id', $user->id)
                             ->first();
            
            if (!$card) {
                return $this->error('NOT_FOUND', 'Payment card not found', null, 404);
            }
            
            // Start a database transaction
            DB::beginTransaction();
            
            try {
                // Set all cards as not default
                StripeCard::where('user_id', $user->id)
                         ->update(['isDefault' => false]);
                
                // Set the selected card as default
                $card->isDefault = true;
                $card->save();
                
                // Initialize Stripe and set as default in Stripe
                if ($card->stripe_card_id && $user->stripe_id) {
                    Stripe::setApiKey(env('STRIPE_SECRET'));
                    $customer = Customer::retrieve($user->stripe_id);
                    $customer->default_source = $card->stripe_card_id;
                    $customer->save();
                }
                
                DB::commit();
            } catch (\Exception $e) {
                DB::rollback();
                throw $e;
            }
            
            // Return the updated card data
            $cardData = [
                'id' => (string)$card->id,
                'last4' => $card->last4,
                'brand' => $card->brand,
                'expMonth' => (int)$card->exp_month,
                'expYear' => (int)$card->exp_year,
                'isDefault' => true,
            ];
            
            return $this->success($cardData, 'Default payment card updated successfully');
        } catch (\Exception $e) {
            Log::error('Failed to set default payment card: ' . $e->getMessage());
            return $this->error('DEFAULT_SET_ERROR', 'Failed to set default payment card', null, 500);
        }
    }
} 