<?php

namespace App\Http\Controllers\Api\V3\Dropshipper;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\Review;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ApiDropshipperRatingsController extends Controller
{
    /**
     * Get all ratings for dropshipper's products
     */
    public function getRatings(Request $request)
    {
        $dropshipper = Auth::user();
        
        $ratings = Review::with(['product', 'user'])
            ->whereHas('product', function ($query) use ($dropshipper) {
                $query->where('user_id', $dropshipper->id);
            })
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($review) {
                return [
                    'id' => $review->id,
                    'productId' => $review->product_id,
                    'productName' => $review->product->name,
                    'productImage' => $review->product->thumbnail_img,
                    'customerName' => $review->user->name,
                    'customerAvatar' => $review->user->avatar,
                    'rating' => $review->rating,
                    'review' => $review->comment,
                    'date' => $review->created_at,
                    'verified' => $review->verified,
                    'sentiment' => $this->getSentiment($review->rating),
                    'attachments' => $review->images ? json_decode($review->images) : [],
                    'tags' => $review->tags && is_array($review->tags) ? $review->tags : [],
                    'helpful' => $review->helpful_count,
                    'sellerResponse' => $review->seller_response,
                    'sellerResponseDate' => $review->seller_response_date,
                    'dropshipperResponse' => $review->dropshipper_response,
                    'dropshipperResponseDate' => $review->dropshipper_response_date,
                ];
            });

        return response()->json($ratings);
    }

    /**
     * Get products with ratings summary
     */
    public function getProductsWithRatings(Request $request)
    {
        $dropshipper = Auth::user();
        
        $products = Product::where('user_id', $dropshipper->id)
            ->with(['reviews', 'category'])
            ->get()
            ->map(function ($product) {
                $ratings = $product->reviews;
                $averageRating = $ratings->avg('rating');
                
                return [
                    'id' => $product->id,
                    'title' => $product->name,
                    'thumbnail' => $product->thumbnail_img,
                    'rating' => $averageRating,
                    'reviewCount' => $ratings->count(),
                    'categories' => $product->category ? [[
                        'id' => $product->category->id,
                        'name' => $product->category->name
                    ]] : []
                ];
            });

        return response()->json($products);
    }

    /**
     * Get ratings analytics
     */
    public function getRatingsAnalytics(Request $request)
    {
        $dropshipper = Auth::user();
        
        // Get all ratings for dropshipper's products
        $ratings = Review::whereHas('product', function ($query) use ($dropshipper) {
            $query->where('user_id', $dropshipper->id);
        })->get();

        // Calculate average rating
        $averageRating = $ratings->avg('rating');
        
        // Calculate rating distribution
        $distribution = $ratings->groupBy('rating')
            ->map(function ($group) {
                return $group->count();
            })
            ->toArray();

        // Calculate response rate
        $totalRatings = $ratings->count();
        $respondedRatings = $ratings->whereNotNull('dropshipper_response')->count();
        $responseRate = $totalRatings > 0 ? ($respondedRatings / $totalRatings) * 100 : 0;

        // Calculate sentiment distribution
        $sentiment = [
            'positive' => $ratings->where('rating', '>=', 4)->count() / $totalRatings * 100,
            'neutral' => $ratings->whereBetween('rating', [3, 3.99])->count() / $totalRatings * 100,
            'negative' => $ratings->where('rating', '<', 3)->count() / $totalRatings * 100,
        ];

        // Calculate recent trend (last 30 days vs previous 30 days)
        $thirtyDaysAgo = now()->subDays(30);
        $sixtyDaysAgo = now()->subDays(60);
        
        $recentRatings = $ratings->where('created_at', '>=', $thirtyDaysAgo);
        $previousRatings = $ratings->whereBetween('created_at', [$sixtyDaysAgo, $thirtyDaysAgo]);
        
        $recentAverage = $recentRatings->avg('rating');
        $previousAverage = $previousRatings->avg('rating');
        
        $recentTrend = $previousAverage > 0 
            ? (($recentAverage - $previousAverage) / $previousAverage) * 100 
            : 0;

        // Get top tags
        $topTags = $ratings->pluck('tags')
            ->filter()
            ->map(function ($tags) {
                return is_array($tags) ? $tags : (is_string($tags) ? json_decode($tags, true) : []);
            })
            ->flatten()
            ->countBy()
            ->map(function ($count, $tag) {
                return [
                    'name' => $tag,
                    'count' => $count
                ];
            })
            ->values()
            ->sortByDesc('count')
            ->take(5)
            ->values();

        return response()->json([
            'averageRating' => $averageRating,
            'totalRatings' => $totalRatings,
            'distribution' => $distribution,
            'responseRate' => $responseRate,
            'sentiment' => $sentiment,
            'recentTrend' => $recentTrend,
            'topTags' => $topTags
        ]);
    }

    /**
     * Submit response to a rating
     */
    public function submitResponse(Request $request, $id)
    {
        $request->validate([
            'response' => 'required|string|max:1000'
        ]);

        $review = Review::findOrFail($id);
        
        // Check if the review belongs to dropshipper's product
        if ($review->product->user_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $review->update([
            'dropshipper_response' => $request->response,
            'dropshipper_response_date' => now()
        ]);

        return response()->json([
            'message' => 'Response submitted successfully',
            'review' => $review
        ]);
    }

    /**
     * Helper function to determine sentiment based on rating
     */
    private function getSentiment($rating)
    {
        if ($rating >= 4) return 'positive';
        if ($rating >= 3) return 'neutral';
        return 'negative';
    }
} 