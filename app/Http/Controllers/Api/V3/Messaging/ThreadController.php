<?php

namespace App\Http\Controllers\Api\V3\Messaging;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Messaging\MessageThread;
use App\Models\Messaging\Message;
use App\Models\Messaging\ThreadParticipant;
use App\Models\Messaging\MessageReadStatus;
use App\Models\Messaging\MessageAttachment;
use App\Models\User;
use App\Events\Messaging\MessageSent;
use App\Events\Messaging\ThreadUpdated;
use App\Events\Messaging\UnreadCountUpdated;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ThreadController extends Controller
{
    /**
     * Get all threads for the authenticated user with pagination and filters
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        
        // Query parameters
        $perPage = $request->input('perPage', 15);
        $page = $request->input('page', 1);
        $search = $request->input('search');
        $status = $request->input('status', 'open'); // 'open', 'closed', 'archived', 'all'
        $sortBy = $request->input('sortBy', 'updated_at');
        $sortDir = $request->input('sortDir', 'desc');
        
        // Start with user's threads
        $query = MessageThread::whereHas('participants', function($q) use ($user) {
            $q->where('user_id', $user->id);
        });
        
        // Apply status filter
        if ($status !== 'all') {
            if ($status === 'archived') {
                $query->where('is_archived', true);
            } elseif ($status === 'closed') {
                $query->where('is_closed', true)
                      ->where('is_archived', false);
            } elseif ($status === 'open') {
                $query->where('is_closed', false)
                      ->where('is_archived', false);
            }
        }
        
        // Apply search filter
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('subject', 'like', "%{$search}%")
                  ->orWhereHas('messages', function($mq) use ($search) {
                      $mq->where('content', 'like', "%{$search}%");
                  });
            });
        }
        
        // Apply sorting
        $query->orderBy($sortBy, $sortDir);
        
        // Get paginated results
        $threads = $query->with(['participants.user', 'lastMessage.sender'])
                         ->paginate($perPage, ['*'], 'page', $page);
        
        // Prepare response data with unread counts
        $data = $threads->toArray();
        
        // Add unread count for each thread
        foreach ($threads as $index => $thread) {
            $data['data'][$index]['unreadCount'] = $thread->unreadMessagesCountForUser($user->id);
        }
        
        return response()->json([
            'success' => true,
            'data' => $data,
        ]);
    }

    /**
     * Create a new thread
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        
        // Validate request
        $validator = Validator::make($request->all(), [
            'subject' => 'required|string|max:255',
            'participants' => 'required|array|min:1',
            'participants.*' => 'required|exists:users,id',
            'message' => 'required|string',
            'attachments' => 'nullable|array',
            'attachments.*' => 'file|max:10240', // 10MB max per file
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        // Create thread
        DB::beginTransaction();
        
        try {
            $thread = MessageThread::create([
                'subject' => $request->subject,
            ]);
            
            // Add sender as participant with admin role
            $thread->addParticipant($user->id, 'admin');
            
            // Add other participants
            foreach ($request->participants as $participantId) {
                if ($participantId != $user->id) {
                    $thread->addParticipant($participantId);
                }
            }
            
            // Create the first message
            $message = $thread->messages()->create([
                'sender_id' => $user->id,
                'content' => $request->message,
            ]);
            
            // Handle attachments
            if ($request->hasFile('attachments')) {
                foreach ($request->file('attachments') as $file) {
                    $path = $file->store('message-attachments', 'public');
                    $url = Storage::url($path);
                    
                    $message->attachments()->create([
                        'name' => $file->getClientOriginalName(),
                        'url' => $url,
                        'type' => $file->getMimeType(),
                        'size' => $file->getSize(),
                    ]);
                }
            }
            
            // Mark as read by sender
            $message->markAsReadBy($user->id);
            
            DB::commit();
            
            // Load relationships for response
            $thread->load(['participants.user', 'messages.sender', 'messages.attachments']);
            
            // Broadcast events
            broadcast(new MessageSent($message))->toOthers();
            broadcast(new ThreadUpdated($thread))->toOthers();
            
            // Update unread counts for all participants except sender
            $this->broadcastUnreadCountUpdates($thread->participants, [$user->id]);
            
            return response()->json([
                'success' => true,
                'message' => 'Thread created successfully',
                'data' => $thread,
            ], 201);
            
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to create thread',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get a specific thread
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $user = Auth::user();
        
        // Find thread and check if user is a participant
        $thread = MessageThread::with(['participants.user', 'messages.sender', 'messages.attachments'])
            ->whereHas('participants', function($q) use ($user) {
                $q->where('user_id', $user->id);
            })
            ->find($id);
        
        if (!$thread) {
            return response()->json([
                'success' => false,
                'message' => 'Thread not found or you do not have access',
            ], 404);
        }
        
        // Add unread count
        $threadData = $thread->toArray();
        $threadData['unreadCount'] = $thread->unreadMessagesCountForUser($user->id);
        
        return response()->json([
            'success' => true,
            'data' => $threadData,
        ]);
    }

    /**
     * Update thread status (archive, close, etc.)
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $user = Auth::user();
        
        // Find thread and check if user is a participant with admin role
        $thread = MessageThread::whereHas('participants', function($q) use ($user) {
            $q->where('user_id', $user->id);
        })->find($id);
        
        if (!$thread) {
            return response()->json([
                'success' => false,
                'message' => 'Thread not found or you do not have access',
            ], 404);
        }
        
        // Check if user has admin role
        $participant = $thread->participants()->where('user_id', $user->id)->first();
        $isAdmin = $participant && $participant->role === 'admin';
        
        // Validate request
        $validator = Validator::make($request->all(), [
            'subject' => 'sometimes|string|max:255',
            'is_archived' => 'sometimes|boolean',
            'is_closed' => 'sometimes|boolean',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors(),
            ], 422);
        }
        
        // Update thread
        if ($request->has('subject') && $isAdmin) {
            $thread->subject = $request->subject;
        }
        
        if ($request->has('is_archived')) {
            $thread->is_archived = $request->is_archived;
        }
        
        if ($request->has('is_closed') && $isAdmin) {
            $thread->is_closed = $request->is_closed;
        }
        
        $thread->save();
        
        // Load relationships for response
        $thread->load(['participants.user', 'lastMessage.sender']);
        
        // Broadcast thread update
        broadcast(new ThreadUpdated($thread));
        
        return response()->json([
            'success' => true,
            'message' => 'Thread updated successfully',
            'data' => $thread,
        ]);
    }

    /**
     * Mark thread as read
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function markAsRead($id)
    {
        $user = Auth::user();
        
        // Find thread and check if user is a participant
        $thread = MessageThread::whereHas('participants', function($q) use ($user) {
            $q->where('user_id', $user->id);
        })->find($id);
        
        if (!$thread) {
            return response()->json([
                'success' => false,
                'message' => 'Thread not found or you do not have access',
            ], 404);
        }
        
        // Mark all unread messages as read
        $unreadMessages = $thread->messages()
            ->whereDoesntHave('readStatus', function($q) use ($user) {
                $q->where('user_id', $user->id);
            })
            ->where('sender_id', '!=', $user->id)
            ->get();
        
        foreach ($unreadMessages as $message) {
            $message->markAsReadBy($user->id);
        }
        
        // Get updated unread counts for the user
        $this->broadcastUnreadCountUpdate($user->id);
        
        return response()->json([
            'success' => true,
            'message' => 'Thread marked as read',
        ]);
    }

    /**
     * Archive thread
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function archive($id)
    {
        $user = Auth::user();
        
        // Find thread and check if user is a participant
        $thread = MessageThread::whereHas('participants', function($q) use ($user) {
            $q->where('user_id', $user->id);
        })->find($id);
        
        if (!$thread) {
            return response()->json([
                'success' => false,
                'message' => 'Thread not found or you do not have access',
            ], 404);
        }
        
        // Archive thread
        $thread->is_archived = true;
        $thread->save();
        
        // Load relationships for response
        $thread->load(['participants.user', 'lastMessage.sender']);
        
        // Broadcast thread update
        broadcast(new ThreadUpdated($thread));
        
        return response()->json([
            'success' => true,
            'message' => 'Thread archived successfully',
            'data' => $thread,
        ]);
    }

    /**
     * Close thread
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function close($id)
    {
        $user = Auth::user();
        
        // Find thread and check if user is a participant with admin role
        $thread = MessageThread::whereHas('participants', function($q) use ($user) {
            $q->where('user_id', $user->id)
              ->where('role', 'admin');
        })->find($id);
        
        if (!$thread) {
            return response()->json([
                'success' => false,
                'message' => 'Thread not found or you do not have admin access',
            ], 404);
        }
        
        // Close thread
        $thread->is_closed = true;
        $thread->save();
        
        // Load relationships for response
        $thread->load(['participants.user', 'lastMessage.sender']);
        
        // Broadcast thread update
        broadcast(new ThreadUpdated($thread));
        
        return response()->json([
            'success' => true,
            'message' => 'Thread closed successfully',
            'data' => $thread,
        ]);
    }

    /**
     * Reopen thread
     *
     * @param  string  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function reopen($id)
    {
        $user = Auth::user();
        
        // Find thread and check if user is a participant with admin role
        $thread = MessageThread::whereHas('participants', function($q) use ($user) {
            $q->where('user_id', $user->id)
              ->where('role', 'admin');
        })->find($id);
        
        if (!$thread) {
            return response()->json([
                'success' => false,
                'message' => 'Thread not found or you do not have admin access',
            ], 404);
        }
        
        // Reopen thread
        $thread->is_closed = false;
        $thread->save();
        
        // Load relationships for response
        $thread->load(['participants.user', 'lastMessage.sender']);
        
        // Broadcast thread update
        broadcast(new ThreadUpdated($thread));
        
        return response()->json([
            'success' => true,
            'message' => 'Thread reopened successfully',
            'data' => $thread,
        ]);
    }

    /**
     * Get unread message counts
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function unreadCounts()
    {
        $user = Auth::user();
        
        // Count unread messages in threads
        $threadUnreadCount = DB::table('messages')
            ->join('thread_participants', 'messages.thread_id', '=', 'thread_participants.thread_id')
            ->leftJoin('message_read_status', function($join) use ($user) {
                $join->on('messages.id', '=', 'message_read_status.message_id')
                     ->where('message_read_status.user_id', '=', $user->id);
            })
            ->where('thread_participants.user_id', $user->id)
            ->where('messages.sender_id', '!=', $user->id)
            ->whereNull('message_read_status.id')
            ->whereNull('messages.deleted_at')
            ->whereNull('thread_participants.deleted_at')
            ->count();
        
        // Count unread messages in seller chats (if exists)
        $sellerChatUnreadCount = 0;
        
        // Total count
        $totalUnreadCount = $threadUnreadCount + $sellerChatUnreadCount;
        
        return response()->json([
            'success' => true,
            'data' => [
                'threads' => $threadUnreadCount,
                'sellerChats' => $sellerChatUnreadCount,
                'total' => $totalUnreadCount,
            ],
        ]);
    }

    /**
     * Helper to broadcast unread count updates to multiple users
     *
     * @param  \Illuminate\Database\Eloquent\Collection  $participants
     * @param  array  $excludeUserIds
     * @return void
     */
    private function broadcastUnreadCountUpdates($participants, $excludeUserIds = [])
    {
        foreach ($participants as $participant) {
            if (!in_array($participant->user_id, $excludeUserIds)) {
                $this->broadcastUnreadCountUpdate($participant->user_id);
            }
        }
    }

    /**
     * Helper to broadcast unread count update to a user
     *
     * @param  int  $userId
     * @return void
     */
    private function broadcastUnreadCountUpdate($userId)
    {
        $user = User::find($userId);
        
        if (!$user) {
            return;
        }
        
        // Count unread messages in threads
        $threadUnreadCount = DB::table('messages')
            ->join('thread_participants', 'messages.thread_id', '=', 'thread_participants.thread_id')
            ->leftJoin('message_read_status', function($join) use ($userId) {
                $join->on('messages.id', '=', 'message_read_status.message_id')
                     ->where('message_read_status.user_id', '=', $userId);
            })
            ->where('thread_participants.user_id', $userId)
            ->where('messages.sender_id', '!=', $userId)
            ->whereNull('message_read_status.id')
            ->whereNull('messages.deleted_at')
            ->whereNull('thread_participants.deleted_at')
            ->count();
        
        // Count unread messages in seller chats (if exists)
        $sellerChatUnreadCount = 0;
        
        // Total count
        $totalUnreadCount = $threadUnreadCount + $sellerChatUnreadCount;
        
        // Broadcast update
        broadcast(new UnreadCountUpdated($userId, [
            'threads' => $threadUnreadCount,
            'sellerChats' => $sellerChatUnreadCount,
            'total' => $totalUnreadCount,
        ]));
    }
} 