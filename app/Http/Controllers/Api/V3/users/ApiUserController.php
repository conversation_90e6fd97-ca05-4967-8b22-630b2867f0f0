<?php

namespace App\Http\Controllers\Api\V3\users;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\User\UserResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class ApiUserController extends ApiResponse
{
    public function profile(Request $request): \Illuminate\Http\JsonResponse
    {
        return $this->success(new UserResource($request->user()));
    }
    public function uploadProfilePicture(Request $request): \Illuminate\Http\JsonResponse
    {

        $validator = Validator::make($request->all(), [
            'aiz_file' => 'required|image|mimes:jpg,jpeg,png|max:5120', // Max 5MB
        ]);
        if ($validator->fails()) {
            return $this->validation_error(
                400,
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        $user = $request->user();

        try {
            $upload = new \App\Utility\ApiAizUploadUtility();
            //$request->merge(['aiz_file' => $request->file('profile_picture')]);
            $result = $upload->upload($request, $user->id);

            if ($result) {
                $user->avatar_original = $result['file_path'];
                $user->save();
                return $this->success(new UserResource($user), 'Profile picture updated successfully');
            }

            return $this->error(400, 'Failed to upload profile picture', 'Upload failed');
        } catch (\Exception $e) {
            return $this->error(500, 'Failed to upload profile picture', $e->getMessage());
        }
    }
    public function changePassword(Request $request): \Illuminate\Http\JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'current_password' => 'required',
            'password' => 'required|min:8',
            'password_confirmation' => 'required|same:password',
        ]);
        if ($validator->fails()) {
            return $this->validation_error(
                'validation_error',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        $user = $request->user();
        if (Hash::check($request->current_password, $user->password)) {
            $user->password = Hash::make($request->password);
            $user->save();
            return $this->success(new UserResource($user) , 'Password changed successfully');
        } else {
            return $this->error(
                'password_mismatch',
                'Old password is incorrect',
                'Old password is incorrect',
                400
            );
        }
    }
}
