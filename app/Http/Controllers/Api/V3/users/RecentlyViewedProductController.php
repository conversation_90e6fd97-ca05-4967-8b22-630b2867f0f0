<?php

namespace App\Http\Controllers\Api\V3\users;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\Product\UserRecentlyViewedProductResource;
use App\Http\Resources\V3\ProductResource;
use App\Models\Product;
use App\Models\RecentlyViewedProduct;
use App\Services\ApiProductService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class RecentlyViewedProductController extends ApiResponse
{
    protected ApiProductService $apiProductService;
    public function __construct
    (
        ApiProductService $apiProductService
    )
    {
        parent::__construct();
        $this->apiProductService = $apiProductService;
    }

    /**
     * Get recently viewed products for authenticated or non-authenticated users
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        try {
            $user = $request->user();
            $sessionId = $request->input('session_id');
            $limit = min((int)$request->input('limit', 10), 50);
            $page = max((int)$request->input('page', 1), 1);
            $sortBy = in_array($request->input('sort_by'), ['viewedAt', 'name', 'price']) ? $request->input('sort_by') : 'viewedAt';
            $sortOrder = strtolower($request->input('sort_direction', 'desc')) === 'asc' ? 'asc' : 'desc';
            $startDate = $request->input('startDate');
            $endDate = $request->input('endDate');
            $category = $request->input('category');

            // Build query based on authentication status
            $query = RecentlyViewedProduct::query()
                ->with(['product' => function($query) {
                    $query->where('published', 1);
                }])
                ->whereHas('product', function (Builder $query) use ($category) {
                    $query->where('published', 1);
                    if ($category) {
                        $query->where('category_id', $category);
                    }
                });

            // Filter by user_id or session_id
            if ($user) {
                $query->where('recently_viewed_products.user_id', $user->id);
            } elseif ($sessionId) {
                $query->where('recently_viewed_products.session_id', $sessionId);
            } else {
                return $this->error(
                    'AUTHENTICATION_ERROR',
                    'Either you need to be authenticated or provide a session_id',
                    null,
                    401
                );
            }

            // Apply date range filter
            if ($startDate) {
                $query->where('recently_viewed_products.viewed_at', '>=', $startDate);
            }
            if ($endDate) {
                $query->where('recently_viewed_products.viewed_at', '<=', $endDate);
            }

            // Apply sorting
            switch ($sortBy) {
                case 'name':
                    $query->join('products', 'products.id', '=', 'recently_viewed_products.product_id')
                        ->orderBy('products.name', $sortOrder);
                    break;
                case 'price':
                    $query->join('products', 'products.id', '=', 'recently_viewed_products.product_id')
                        ->orderBy('products.unit_price', $sortOrder);
                    break;
                default:
                    $query->orderBy('recently_viewed_products.viewed_at', $sortOrder);
            }

            $recentlyViewed = $query->paginate($limit);
            $total_items = $recentlyViewed->total();
            $total_pages = $recentlyViewed->lastPage();

            $data=[
                'data' => UserRecentlyViewedProductResource::collection($recentlyViewed->items()),
                'meta' => [
                    'total' => $total_items,
                    'totalPages' => $total_pages,
                    'currentPage' => $page,
                    'perPage' => $limit,
                    'from' => $recentlyViewed->firstItem() ?: 0,
                    'to' => $recentlyViewed->lastItem() ?: 0
                ]
            ];
            return $this->success($data);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch recently viewed products',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Store a product in recently viewed
     * Works for both authenticated and non-authenticated users
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'productId' => 'required|exists:products,slug',
            'session_id' => 'required_without:user_id|string|nullable',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Invalid request',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            $user = $request->user();
            $sessionId = $request->input('session_id');
            
            // Require either user authentication or session_id
            if (!$user && !$sessionId) {
                return $this->error(
                    'AUTHENTICATION_ERROR',
                    'Either you need to be authenticated or provide a session_id',
                    null,
                    401
                );
            }

            $product = Product::where('slug', $request->productId)
                ->where('published', 1)
                ->firstOrFail();
            
            // Store with appropriate identifier
            $userId = $user ? $user->id : null;
            $recentlyViewed = $this->apiProductService->storeRecentlyViewedProduct($userId, $product->id, $sessionId);
            
            return $this->success([
                'message' => 'Product added to recently viewed',
                'productId' => $product->slug,
                'viewedAt' => $recentlyViewed->viewed_at->toIso8601String(),
                'viewCount' => $recentlyViewed->view_count,
                'product' => new ProductResource($product),
            ]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to add product to recently viewed',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Clear all recently viewed products for authenticated user or session
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy(Request $request)
    {
        try {
            $user = $request->user();
            $sessionId = $request->input('session_id');
            
            // Require either user authentication or session_id
            if (!$user && !$sessionId) {
                return $this->error(
                    'AUTHENTICATION_ERROR',
                    'Either you need to be authenticated or provide a session_id',
                    null,
                    401
                );
            }
            
            // Delete all recently viewed products for this user or session
            $query = RecentlyViewedProduct::query();
            
            if ($user) {
                $query->where('recently_viewed_products.user_id', $user->id);
            } elseif ($sessionId) {
                $query->where('recently_viewed_products.session_id', $sessionId);
            }
            
            $deleted = $query->delete();
            
            return $this->success([
                'message' => 'Recently viewed history cleared successfully',
                'deletedCount' => $deleted
            ]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to clear recently viewed history',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Get statistics about recently viewed products
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function stats(Request $request)
    {
        try {
            $user = Auth::user();
            $sessionId = $request->input('session_id');
            
            // Require either user authentication or session_id
            if (!$user && !$sessionId) {
                return $this->error(
                    'AUTHENTICATION_ERROR',
                    'Either you need to be authenticated or provide a session_id',
                    null,
                    401
                );
            }
            
            $now = now();
            $sevenDaysAgo = $now->copy()->subDays(7);
            $thirtyDaysAgo = $now->copy()->subDays(30);

            // Create query based on identifier
            $query = RecentlyViewedProduct::query();
            
            if ($user) {
                $query->where('recently_viewed_products.user_id', $user->id);
            } elseif ($sessionId) {
                $query->where('recently_viewed_products.session_id', $sessionId);
            }

            // Get total views
            $totalViewed = (clone $query)->sum('recently_viewed_products.view_count');

            // Get views in last 7 days
            $viewsLast7Days = (clone $query)
                ->where('recently_viewed_products.viewed_at', '>=', $sevenDaysAgo)
                ->sum('recently_viewed_products.view_count');

            // Get views in last 30 days
            $viewsLast30Days = (clone $query)
                ->where('recently_viewed_products.viewed_at', '>=', $thirtyDaysAgo)
                ->sum('recently_viewed_products.view_count');

            // Get most viewed categories
            $categoryQuery = clone $query;
            $mostViewedCategories = $categoryQuery
                ->join('products', 'products.id', '=', 'recently_viewed_products.product_id')
                ->join('categories', 'categories.id', '=', 'products.category_id')
                ->select('categories.id as categoryId', 'categories.name as categoryName', DB::raw('SUM(recently_viewed_products.view_count) as count'))
                ->groupBy('categories.id', 'categories.name')
                ->orderBy('count', 'desc')
                ->limit(5)
                ->get();

            // Get recent views count by date
            $recentViewsCount = (clone $query)
                ->where('recently_viewed_products.viewed_at', '>=', $thirtyDaysAgo)
                ->select(DB::raw('DATE(recently_viewed_products.viewed_at) as date'), DB::raw('SUM(recently_viewed_products.view_count) as count'))
                ->groupBy('date')
                ->orderBy('date', 'desc')
                ->get()
                ->map(function ($item) {
                    return [
                        'date' => $item->date,
                        'count' => (int)$item->count
                    ];
                });

            return $this->success([
                'totalViewed' => (int)$totalViewed,
                'viewsLast7Days' => (int)$viewsLast7Days,
                'viewsLast30Days' => (int)$viewsLast30Days,
                'mostViewedCategories' => $mostViewedCategories,
                'recentViewsCount' => $recentViewsCount
            ]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch recently viewed statistics',
                $e->getMessage(),
                500
            );
        }
    }

    /**
     * Remove a specific product from recently viewed
     *
     * @param Request $request
     * @param string $productId
     * @return \Illuminate\Http\JsonResponse
     */
    public function removeProduct(Request $request, $productId)
    {
        try {
            $user = $request->user();
            $sessionId = $request->input('session_id');
            
            // Require either user authentication or session_id
            if (!$user && !$sessionId) {
                return $this->error(
                    'AUTHENTICATION_ERROR',
                    'Either you need to be authenticated or provide a session_id',
                    null,
                    401
                );
            }
            
            // Find the product by slug
            $product = Product::where('slug', $productId)
                ->where('published', 1)
                ->first();
                
            if (!$product) {
                return $this->error(
                    'PRODUCT_NOT_FOUND',
                    'Product not found',
                    null,
                    404
                );
            }
            
            // Delete the recently viewed record
            $query = RecentlyViewedProduct::where('recently_viewed_products.product_id', $product->id);
            
            if ($user) {
                $query->where('recently_viewed_products.user_id', $user->id);
            } elseif ($sessionId) {
                $query->where('recently_viewed_products.session_id', $sessionId);
            }
            
            $deleted = $query->delete();
            
            if (!$deleted) {
                return $this->error(
                    'NOT_FOUND',
                    'Product was not in your recently viewed list',
                    null,
                    404
                );
            }
            
            return $this->success([
                'message' => 'Product removed from recently viewed',
                'productId' => $productId
            ]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to remove product from recently viewed',
                $e->getMessage(),
                500
            );
        }
    }
    
    /**
     * Merge recently viewed products from a session to a user account
     * This is useful when a guest user logs in
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function mergeSessionToUser(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'session_id' => 'required|string'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Invalid request',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            $user = $request->user();
            $sessionId = $request->input('session_id');
            
            if (!$user) {
                return $this->error(
                    'AUTHENTICATION_ERROR',
                    'You need to be authenticated to merge recently viewed products',
                    null,
                    401
                );
            }
            
            // Get all recently viewed products from the session
            $sessionViewed = RecentlyViewedProduct::where('recently_viewed_products.session_id', $sessionId)
                ->whereNotNull('recently_viewed_products.product_id')
                ->get();
                
            if ($sessionViewed->isEmpty()) {
                return $this->success([
                    'message' => 'No session data to merge',
                    'merged' => 0
                ]);
            }
            
            $merged = 0;
            
            foreach ($sessionViewed as $viewed) {
                // Check if this product is already in the user's recently viewed
                $existingView = RecentlyViewedProduct::where('recently_viewed_products.user_id', $user->id)
                    ->where('recently_viewed_products.product_id', $viewed->product_id)
                    ->first();
                
                if ($existingView) {
                    // Update existing view with latest data
                    $existingView->viewed_at = max($existingView->viewed_at, $viewed->viewed_at);
                    $existingView->view_count += $viewed->view_count;
                    $existingView->save();
                } else {
                    // Create a new user view from the session view
                    $newView = new RecentlyViewedProduct();
                    $newView->user_id = $user->id;
                    $newView->product_id = $viewed->product_id;
                    $newView->viewed_at = $viewed->viewed_at;
                    $newView->view_count = $viewed->view_count;
                    $newView->save();
                }
                
                $merged++;
            }
            
            // Delete session views after successful merge
            RecentlyViewedProduct::where('recently_viewed_products.session_id', $sessionId)->delete();
            
            return $this->success([
                'message' => 'Session recently viewed products merged with user account',
                'merged' => $merged
            ]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to merge recently viewed products',
                $e->getMessage(),
                500
            );
        }
    }
}
