<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Http\Resources\V3\UserResource;
use App\Models\PasswordReset;
use App\Models\User;
use App\Notifications\OTPRestPasswordNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Carbon\Carbon;
class ApiResetPasswordController extends ApiResponse
{
    public function forgetRequest(Request $request)
    {
        $messages = array(
            'email.required' => 'Please enter your email address.',
            'email.email' => 'The email address must be in a valid format.',
            'email.exists' => 'We couldn’t find an account with that email address.',
        );
        $validator = Validator::make($request->all(), [
            'email' => 'required|min:6|exists:users,email',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error(
                'Validation Error',
                'Please provide valid credentials',
                $validator->errors()->messages(),
                400);
        }

        $user = User::where('email', $request->email)->first();
        PasswordReset::where('email', $request->email)->delete();
        $token = Str::random(64);
        PasswordReset::insert([
            'email' => $request->email,
            'token' => $token,
            'created_at' => Carbon::now()
        ]);

        $user->notify(new OTPRestPasswordNotification());
        $data['user']=new UserResource($user);
        $data['token']=$token;
        return $this->success($data, 'OTP send successful. Please verify your email.');
    }
    public function confirmReset(Request $request)
    {
        $messages = array(
            'email.required' => 'Please enter your email address.',
            'email.email' => 'The email address must be in a valid format.',
            'email.exists' => 'We couldn’t find an account with that email address.',
            'token.required' => 'Token is required.',
            'token.exists' => 'Can not find token.',
        );
        $validator = Validator::make($request->all(), [
            'email' => 'required|min:6|exists:users,email',
            'verification_code' => 'required|min:6',
            'password' => 'required|min:6|confirmed',
            'token' => 'required|exists:password_resets,token',
        ], $messages);

        if ($validator->fails()) {
            return $this->validation_error(
                'Validation Error',
                'Please provide valid credentials',
                $validator->errors()->messages(),
                400);
        }
        $token_exist = PasswordReset::where('token', $request->token)
            ->where('email', $request->email)
            ->first();

        if(!$token_exist){
            return $this->error('Invalid Token','We couldn’t find the token.',200);
        }

        $user = User::where('email', $request->email)->where('verification_code', $request->verification_code)->first();
        if (!$user) {
            return $this->error('Invalid verification code','We couldn’t find the verification code.',200);
        }
        $user->password = bcrypt($request->password);
        $user->save();
        PasswordReset::where('email', $request->email)->delete();
        $data['user'] = new UserResource($user);
        return $this->success($data, 'Password reset successfully.');
    }
    public function resendCode(Request $request){

    }
}
