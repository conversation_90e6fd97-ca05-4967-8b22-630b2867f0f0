<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Api\V3\ApiResponse;
use Illuminate\Http\Request;

class ApiResolutionTypesController extends ApiResponse
{
    /**
     * Get all resolution types
     */
    public function index()
    {
        try {
            $resolutionTypes = [
                [
                    'id' => 'refund',
                    'name' => translate('Full Refund'),
                    'description' => translate('Get a full refund for the returned item(s)'),
                    'available' => true,
                ],
                [
                    'id' => 'exchange',
                    'name' => translate('Exchange'),
                    'description' => translate('Exchange the item for the same product'),
                    'available' => true,
                ],
                [
                    'id' => 'replacement',
                    'name' => translate('Replacement'),
                    'description' => translate('Get a replacement for the defective item'),
                    'available' => true,
                ],
                [
                    'id' => 'store_credit',
                    'name' => translate('Store Credit'),
                    'description' => translate('Receive store credit for future purchases'),
                    'available' => true,
                ],
                [
                    'id' => 'repair',
                    'name' => translate('Repair'),
                    'description' => translate('Repair the defective item if possible'),
                    'available' => false,
                ],
            ];

            return $this->success(
                $resolutionTypes,
                translate('Resolution types retrieved successfully')
            );
        } catch (\Exception $e) {
            return $this->error(
                'INTERNAL_SERVER_ERROR',
                translate('Failed to retrieve resolution types'),
                500
            );
        }
    }
} 