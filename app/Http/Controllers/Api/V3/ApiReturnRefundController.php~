<?php

namespace App\Http\Controllers\Api\V3;

use App\Enums\OrderStatus;
use App\Enums\ReturnStatus;
use App\Http\Controllers\Controller;
use App\Http\Resources\V3\Orders\OrdersResource;
use App\Http\Resources\V3\Rerurn\RerurnRequestResource;
use App\Http\Resources\V3\Rerurn\RerurnRequestsResource;
use App\Http\Resources\V3\ReturnMethods\PickupDetailResource;
use App\Http\Resources\V3\ReturnMethods\ReturnEligibilityResource;
use App\Http\Resources\V3\ReturnMethods\ReturnFeedbackResource;
use App\Http\Resources\V3\ReturnMethods\ShippingLabelResource;
use App\Models\BusinessSetting;
use App\Models\Order;
use App\Models\ReturnRequest;
use App\Models\ReturnRequestInfo;
use App\Models\ReturnRequestProduct;
use App\Services\ActivityLogService;
use App\Services\ReturnMethodService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class ApiReturnRefundController extends ApiResponse
{
    protected ActivityLogService $activityLogService;
    protected ReturnMethodService $returnMethodService;

    public function __construct(ActivityLogService $activityLogService, ReturnMethodService $returnMethodService)
    {
        parent::__construct();
        $this->activityLogService = $activityLogService;
        $this->returnMethodService = $returnMethodService;
    }

    public function index(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'sort' => 'nullable|in:newest,oldest',
            'date_from' => 'nullable|date_format:Y-m-d',
            'date_to' => 'nullable|date_format:Y-m-d|after_or_equal:date_from',
            'search' => 'nullable|string|max:255',
            'status' => 'nullable|string|in:' . implode(',', ReturnStatus::getValues())
        ]);
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        try {
            $per_page = min((int) $request->input('per_page', 20), 50);
            $page = max((int) $request->input('page', 1), 1);
            $sort_by = in_array($request->input('sort_by'), ['newest', 'oldest']) ? $request->input('sort') : 'newest';
            $start_date = $request->input('date_from');
            $end_date = $request->input('date_to');
            $search = $request->input('search');
            $status = $request->input('status');

            $query = ReturnRequestInfo::with('order', 'return_request_products', 'return_request_products.product')
                ->where('user_id', auth()->user()->id);

            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->whereHas('order', function ($q) use ($search) {
                        $q->where('code', 'like', '%' . $search . '%');
                    })->orWhereHas('return_request_products', function ($q) use ($search) {
                        $q->whereHas('product', function ($q) use ($search) {
                            $q->where('name', 'like', '%' . $search . '%');
                        });
                    });
                });
            }

            if ($status) {
                $query->where('return_requests.status', $status);
            }

            if ($start_date && $end_date) {
                $query->whereBetween('return_requests.created_at', [$start_date, $end_date]);
            }

            switch ($sort_by) {
                case 'oldest':
                    $query->orderBy('created_at', 'asc');
                    break;
                default: // date_desc
                    $query->orderBy('created_at', 'desc');
            }
            $returnRequests = $query->paginate($per_page);
            $total_items = $returnRequests->total();
            $total_pages = $returnRequests->lastPage();

            $data = [
                'orders' => new RerurnRequestsResource($returnRequests),
                'pagination' => [
                    'currentPage' => (int) $page,
                    'totalPages' => $total_pages,
                    'totalItems' => $total_items,
                    'itemsPerPage' => (int) $per_page,
                ]
            ];
            return $this->success($data);
        } catch (QueryException $e) {
            Log::channel('api_order')->error('Unexpected error while fetching orders list : ApiOrdersController ' . print_r($e->getMessage(), true));
            return $this->error('A database error occurred. Please try again later.', 500);
        } catch (ModelNotFoundException $e) {
            Log::channel('api_order')->error('Unexpected error while fetching orders list : ApiOrdersController ' . print_r($e->getMessage(), true));
            return $this->error('Requested resource not found.', 404);
        } catch (\Exception $e) {
            Log::channel('api_order')->error('Unexpected error while fetching orders list : ApiOrdersController ' . print_r($e->getMessage(), true));
            return $this->error('Something went wrong. Please try again later.', 500);
        }
    }
    public function return_request_details(Request $request, $return_code)
    {

        $returnRequestInfo = ReturnRequestInfo::with('order', 'return_request_products', 'return_request_products.product')
            ->where('return_code', $return_code)->first();
        if (!$returnRequestInfo) {
            return $this->error(
                'VALIDATION_ERROR.',
                'Return Request not found.',
                '',
                404
            );
        }
        return $this->success(
            new RerurnRequestResource($returnRequestInfo),
            'Return Request Placed',
        );
    }


    public function return_configuration()
    {
        return cache()->rememberForever('return_request_configuration', function () {
            $return_request_configuration = BusinessSetting::where('type', 'return_request_configuration')->first();
            return $this->success(json_decode($return_request_configuration->value, true));
        });
    }
    public function return_faq()
    {
        return cache()->rememberForever('return_faq', function () {
            $return_request_configuration = BusinessSetting::where('type', 'return_faq')->first();
            return $this->success(json_decode($return_request_configuration->value, true));
        });
    }





    /**
     * Reschedule a courier pickup for a return
     *
     * @param Request $request
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function reschedulePickup(Request $request, $id)
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'date' => 'required|date|after_or_equal:today',
                'timeSlot' => 'required|string',
                'address' => 'required|string',
                'contactName' => 'required|string',
                'contactPhone' => 'required|string',
                'instructions' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return $this->validation_error(
                    'VALIDATION_ERROR',
                    'Please provide valid information',
                    $validator->errors()->messages(),
                    400
                );
            }

            // Find the return request
            $returnRequest = ReturnRequestInfo::where('return_code', $id)->firstOrFail();

            // Check if the return request belongs to the authenticated user
            if ($returnRequest->user_id !== auth()->id()) {
                return $this->error(
                    'UNAUTHORIZED',
                    'You are not authorized to access this return request',
                    '',
                    403
                );
            }

            // Check if the return request has a pickup method
            if ($returnRequest->returnMethod !== 'courier_pickup') {
                return $this->error(
                    'INVALID_METHOD',
                    'This return request does not use courier pickup',
                    '',
                    400
                );
            }

            // Check if the return_pickup_details table exists
            if (!Schema::hasTable('return_pickup_details')) {
                return $this->error(
                    'TABLE_NOT_FOUND',
                    'The return_pickup_details table does not exist',
                    '',
                    500
                );
            }

            // Reschedule the pickup
            $pickupData = $this->returnMethodService->reschedulePickup($returnRequest, $request->all());

            if (!$pickupData) {
                return $this->error(
                    'RESCHEDULE_FAILED',
                    'Failed to reschedule pickup',
                    '',
                    500
                );
            }

            return $this->success(
                $pickupData,
                'Pickup rescheduled successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->error(
                'NOT_FOUND',
                'Return request not found',
                '',
                404
            );
        } catch (\Exception $e) {
            Log::channel('api_order')->error('Error in reschedulePickup: ' . $e->getMessage());
            return $this->error(
                'SERVER_ERROR',
                'An error occurred while rescheduling the pickup',
                $e->getMessage(),
                500
            );
        }
    }
}
