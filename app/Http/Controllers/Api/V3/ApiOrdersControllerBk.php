<?php

namespace App\Http\Controllers\Api\V3;

use App\Enums\OrderStatus;
use App\Http\Controllers\AffiliateController;
use App\Http\Controllers\Controller;
use App\Http\Resources\V3\Orders\OrderResource;
use App\Http\Resources\V3\Orders\OrdersResource;
use App\Models\Address;
use App\Models\Cart;
use App\Models\CombinedOrder;
use App\Models\Coupon;
use App\Models\CouponUsage;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\Product;
use App\Models\StripeCard;
use App\Models\User;
use App\Services\ActivityLogService;
use App\Services\ApiOrderService;
use App\Services\OrderService;
use App\Services\StripeService;
use App\Utility\NotificationUtility;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\JsonResponse;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\QueryException;
use App\Http\Resources\V3\BulkOrder\ProductsForBulkOrderResource;
use App\Http\Resources\V3\BulkOrder\ProductBulkOrderDetailResource;
use Stripe\Exception\ApiErrorException;
use Stripe\Exception\CardException;
use Stripe\Exception\InvalidRequestException;

class ApiOrdersControllerBk extends ApiResponse
{
    /**
     * @var \App\Services\OrderService
     */
    protected OrderService $orderService ;
    protected StripeService $stripeService;
    protected ActivityLogService $activityLogService;
    protected ApiOrderService $apiOrderService;
    public function __construct(
        StripeService $stripeService,
        OrderService $orderService,
        ActivityLogService $activityLogService,
        ApiOrderService $apiOrderService
    )
    {
        parent::__construct();
        $this->stripeService = $stripeService;
        $this->orderService = $orderService;
        $this->activityLogService = $activityLogService;
        $this->apiOrderService = $apiOrderService;
    }
    //Log::channel('api_order')->error('Error in user social login : AuthController ' . print_r($e->getMessage(),true));
    public function order_list(Request $request){
        $validator = Validator::make($request->all(), [
            'sort' => 'nullable|in:date_desc,date_asc,total_desc,total_asc',
            'start_date' => 'nullable|date_format:Y-m-d',
            'end_date' => 'nullable|date_format:Y-m-d|after_or_equal:start_date',
            'search' => 'nullable|string|max:255',
            'status' => 'nullable|string|in:' . implode(',', OrderStatus::getValues())
        ]);
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        try {
        $per_page = min((int)$request->input('per_page', 20), 50);
        $page = max((int)$request->input('page', 1), 1);
        $sort = $request->input('sort', 'date_desc');
        $start_date = $request->input('start_date');
        $end_date = $request->input('end_date');
        $search = $request->input('search');

        $query = Order::with('orderDetails','orderDetails.product','user');

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('code', 'like', '%' . $search . '%')
                  ->orWhereHas('user', function($q) use ($search) {
                      $q->where('name', 'like', '%' . $search . '%');
                  })
                  ->orWhereHas('orderDetails.product', function($q) use ($search) {
                      $q->where('name', 'like', '%' . $search . '%');
                  });
            });
        }

        if ($start_date) {
            $query->whereDate('created_at', '>=', $start_date);
        }

        if ($end_date) {
            $query->whereDate('created_at', '<=', $end_date);
        }

        if ($request->has('status')) {
            $query->where('delivery_status', $request->status);
        }

        switch ($sort) {
            case 'date_asc':
                $query->orderBy('created_at', 'asc');
                break;
            case 'total_desc':
                $query->orderBy('grand_total', 'desc');
                break;
            case 'total_asc':
                $query->orderBy('grand_total', 'asc');
                break;
            default: // date_desc
                $query->orderBy('created_at', 'desc');
        }

        $orders = $query->paginate($per_page);
        $total_items = $orders->total();
        $total_pages = $orders->lastPage();

        $data = [
            'orders' => new OrdersResource($orders),
            'pagination' => [
                'currentPage' => (int) $page,
                'totalPages' => $total_pages,
                'totalItems' => $total_items,
                'itemsPerPage' => (int) $per_page,
            ]
        ];
        return $this->success($data);
        } catch (QueryException $e) {
            Log::channel('api_order')->error('Unexpected error while fetching orders list : ApiOrdersController ' . print_r($e->getMessage(),true));
            return $this->error('A database error occurred. Please try again later.', 500);
        } catch (ModelNotFoundException $e) {
            Log::channel('api_order')->error('Unexpected error while fetching orders list : ApiOrdersController ' . print_r($e->getMessage(),true));
            return $this->error('Requested resource not found.', 404);
        } catch (\Exception $e) {
            Log::channel('api_order')->error('Unexpected error while fetching orders list : ApiOrdersController ' . print_r($e->getMessage(),true));
            return $this->error('Something went wrong. Please try again later.', 500);
        }
    }

    public function get_order_details(Request $request){
        $validator = Validator::make($request->all(), [
            'order_code' => 'required|string|exists:orders,code',
        ]);
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        try{
            $order = Order::with('orderDetails','orderDetails.product','user')
                ->where('code', $request->order_code)
                ->first();
            if (!$order) {
                return $this->error('Requested resource not found.', 404);
            }
            $data = [
                'order' => new OrderResource($order),
            ];
            return $this->success($data);

        } catch (QueryException $e) {
            Log::channel('api_order')->error('Unexpected error while fetching orders list : ApiOrdersController ' . print_r($e->getMessage(),true));
            return $this->error('A database error occurred. Please try again later.', 500);
        } catch (ModelNotFoundException $e) {
            Log::channel('api_order')->error('Unexpected error while fetching orders list : ApiOrdersController ' . print_r($e->getMessage(),true));
            return $this->error('Requested resource not found.', 404);
        } catch (\Exception $e) {
            Log::channel('api_order')->error('Unexpected error while fetching orders list in ApiOrdersController', [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
            ]);
            return $this->error('Something went wrong. Please try again later.', 500);
        }


    }
    public function save_order(Request $request):JsonResponse{

        $validator = Validator::make($request->all(), [
            'order_from' => 'required|in:web,android,ios',
        ]);


        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        $user_id = auth()->user()->id;
        $carts = Cart::where('user_id', $user_id)->get();

        $additional_info=$request->additional_info;
        $is_gift=$request->input('is_gift', 0);
        $order_from=$request->order_from;

        if(empty($carts)){
            return $this->validation_error(
                400,
                'Please add product to cart',
                [],
                400
            );
        }
        $address = Address::where('id',$carts->first()->address_id)
            ->where('user_id',$user_id)
            ->first();
        $shipping_address = [];
        if(empty($address)){
            return $this->validation_error(
                400,
                'Please add address',
                [],
                400
            );
        }
        $seller_products = array();
        foreach ($carts as $cartItem) {
            $product = Product::find($cartItem['product_id']);
            if (product_stock_quantity($product, $cartItem['variation']) < $cartItem['quantity']) {
                return $this->error(
                    'Out of Stock',
                    'Product is out of stock',
                    null,
                    200
                );
            }

            $product_ids = array();

            if (isset($seller_products[$product->user_id])) {
                $product_ids = $seller_products[$product->user_id];
            }
            array_push($product_ids, $cartItem);
            $seller_products[$product->user_id] = $product_ids;
        }
        DB::beginTransaction();
        try {

            $shipping_address = [
                'street_address' => $address->street_address,
                'apartment_address' => $address->apartment_address,
                'country' => $address->country?->name,
                'state' =>  $address->state->name,
                'city' => $address->city->name,
                'longitude' => $address->longitude,
                'latitude' => $address->latitude,
                'postal_code' => $address->postal_code,
                'phone' => $address->phone,
                'name' => $address->receiver_name,
                'lat_lang' => $address->lat_lang,
                'email' => '',
                'address' => '',
            ];






            $combined_order = new CombinedOrder();
            $combined_order->user_id = $user_id;
            $combined_order->shipping_address = json_encode($shipping_address);
            $combined_order->save();
            foreach ($seller_products as $seller_product) {
                $order = new Order;
                $order->combined_order_id = $combined_order->id;
                $order->user_id = $user_id;
                $order->shipping_address = $combined_order->shipping_address;
                $order->additional_info = $additional_info;
                $order->is_gift = $is_gift;
                $order->order_from = $order_from;
                $order->payment_type = 'stripe';
                $order->payment_status = 'unpaid';
                $order->delivery_viewed = '0';
                $order->payment_status_viewed = '0';
                $order->code = date('Ymd-His') . rand(10, 99);
                $order->date = strtotime('now');
                $order->payment_status = 'unpaid';
                $order->pending_order_email   = Carbon::now()->addMinutes(env("PENDING_ORDER_EMAIL_MINUTES", 10));
                $order->cancel_pending_order_email   = Carbon::now()->addMinutes(env("CANCEL_PENDING_ORDER_MINUTES", 720));
                $order->pending_order_reminder_email   = Carbon::now()->addMinutes(env("PENDING_ORDER_REMINDER_EMAIL_MINUTES", 600));
                $order->save();

                $this->activityLogService->log(
                    'order_status_changed',
                    'Customer Order Created',
                    $order->id,
                    Order::class,
                    auth()->user()->id,
                    get_class(auth()->user()),
                    '',
                    OrderStatus::PENDING,
                    null,
                    email_end_time: null,
                );



                $subtotal = 0;
                $tax = 0;
                $shipping = 0;
                $coupon_discount = 0;
                foreach ($seller_product as $cartItem) {
                    $product = Product::find($cartItem['product_id']);
                    //$subtotal += cart_product_price($cartItem, $product, false, false) * $cartItem['quantity'];
                    //$tax += cart_product_tax($cartItem, $product, false) * $cartItem['quantity'];
                    $subtotal += $cartItem['price'] * $cartItem['quantity'];
                    $tax += $cartItem['tax'] * $cartItem['quantity'];
                    $coupon_discount += $cartItem['discount'];
                    $product_variation = $cartItem['variation'];
                    $shipping += $cartItem['shipping_cost'];

                    if ($product->variant_product == 1 && $product_variation != null) {
                        $product_stock = $product->stocks->where('variant', $product_variation)->first();
                    } else {
                        $product_stock = $product->stocks->where('product_id', $product->id)->first();
                    }
                   if ($product->digital != 1) {
                        $product_stock->qty -= $cartItem['quantity'];
                        $product_stock->save();
                    }

                    $order_detail = new OrderDetail;
                    $order_detail->order_id = $order->id;
                    $order_detail->seller_id = $product->user_id;
                    $order_detail->product_id = $product->id;
                    $order_detail->variation = $product_variation;
                    $order_detail->unit_price = $cartItem['price'];
                    $order_detail->price = $cartItem['price'] * $cartItem['quantity'];
                    $order_detail->tax = $cartItem['tax'] * $cartItem['quantity'];
                    $order_detail->shipping_type = $cartItem['shipping_type'];
                    $order_detail->product_referral_code = $cartItem['product_referral_code'];
                    $order_detail->shipping_cost = $cartItem['shipping_cost'];
                    $order_detail->quantity = $cartItem['quantity'];
                    if (addon_is_activated('club_point')) {
                        $order_detail->earn_point = $product->earn_point;
                    }
                    $order_detail->save();

                    $product->num_of_sale += $cartItem['quantity'];
                    $product->save();


                    $order->seller_id = $product->user_id;
                    $order->shipping_type = $cartItem['shipping_type'];
                    if ($cartItem['shipping_type'] == 'pickup_point') {
                        $order->pickup_point_id = $cartItem['pickup_point'];
                    }
                    if ($cartItem['shipping_type'] == 'carrier') {
                        $order->carrier_id = $cartItem['carrier_id'];
                    }
                    if ($product->added_by == 'seller' && $product->user->seller != null) {
                        $seller = $product->user->seller;
                        $seller->num_of_sale += $cartItem['quantity'];
                        $seller->save();
                    }
                    if (addon_is_activated('affiliate_system')) {
                        if ($order_detail->product_referral_code) {
                            $referred_by_user = User::where('referral_code', $order_detail->product_referral_code)->first();

                            $affiliateController = new AffiliateController;
                            $affiliateController->processAffiliateStats($referred_by_user->id, 0, $order_detail->quantity, 0, 0);
                        }
                    }
                }
                $order->grand_total = $subtotal + $tax + $shipping;
                if ($seller_product[0]->coupon_code != null) {
                    $order->coupon_discount = $coupon_discount;
                    $order->grand_total -= $coupon_discount;

                    $coupon_usage = new CouponUsage;
                    $coupon_usage->user_id = $user_id;
                    $coupon_usage->coupon_id = Coupon::where('code', $seller_product[0]->coupon_code)->first()->id;
                    $coupon_usage->save();
                }
                $combined_order->grand_total += $order->grand_total;

                $order->save();
            }
            $combined_order->save();

            DB::commit();
            $responce = [
                'order' => '',
            ];
            return $this->success($responce,
                'Order placed successfully',
            );

        } catch (\Exception $e) {
            Log::channel('api_order')->error('Error in ApiOrdersController : AuthController ' . print_r($e->getMessage(),true));
            DB::rollBack();
            return $this->error(
                'INTERNAL_SERVER_ERROR',
                $e->getMessage(),
                500
            );
        }

    }


    public function bulk_order_products(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'nullable|integer|min:1',
            'limit' => 'nullable|integer|min:1|max:50',
            'search' => 'nullable|string|max:255',
            'category' => 'nullable|string|exists:categories,slug',
            'sort' => 'nullable|string|in:price-asc,price-desc,name-asc,name-desc,stock-asc,stock-desc',
            'inStock' => 'nullable|boolean'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            $query = Product::with(['category', 'brand', 'stocks'])
                ->where('published', 1)
                ->where('auction_product', 0)
                ->where('approved', 1);

            // Apply search filter
            if ($request->search) {
                $query->where(function($q) use ($request) {
                    $q->where('name', 'like', '%' . $request->search . '%')
                      ->orWhere('description', 'like', '%' . $request->search . '%');
                });
            }

            // Apply category filter
            if ($request->category) {
                $query->whereHas('category', function($q) use ($request) {
                    $q->where('slug', $request->category);
                });
            }

            // Apply stock filter
            if ($request->has('inStock')) {
                $query->whereHas('stocks', function($q) use ($request) {
                    if ($request->inStock) {
                        $q->where('qty', '>', 0);
                    }
                });
            }

            // Apply sorting
            if ($request->sort) {
                [$field, $direction] = explode('-', $request->sort);
                switch ($field) {
                    case 'price':
                        $query->orderBy('unit_price', $direction);
                        break;
                    case 'name':
                        $query->orderBy('name', $direction);
                        break;
                    case 'stock':
                        $query->orderBy('current_stock', $direction);
                        break;
                }
            }

            $limit = $request->input('limit', 10);
            $products = $query->paginate($limit);

            return $this->success([
                'products' => ProductsForBulkOrderResource::collection($products),
                'meta' => [
                    'current_page' => $products->currentPage(),
                    'from' => $products->firstItem(),
                    'last_page' => $products->lastPage(),
                    'path' => $request->url(),
                    'per_page' => $products->perPage(),
                    'to' => $products->lastItem(),
                    'total' => $products->total()
                ],
                'links' => [
                    'first' => $products->url(1),
                    'last' => $products->url($products->lastPage()),
                    'prev' => $products->previousPageUrl(),
                    'next' => $products->nextPageUrl()
                ]
            ]);

        } catch (QueryException $e) {
            Log::channel('api_order')->error('Database error in bulk_order_products: ' . $e->getMessage());
            return $this->error('A database error occurred. Please try again later.', 500);
        } catch (\Exception $e) {
            Log::channel('api_order')->error('Unexpected error in bulk_order_products: ' . $e->getMessage());
            return $this->error('Something went wrong. Please try again later.', 500);
        }
    }
    public function bulk_order_product_details(Request $request, $slug)
    {
        try {
            $product = Product::with(['category', 'brand', 'stocks'])
                ->where('slug', $slug)
                ->where('published', 1)
                ->where('approved', 1)
                ->where('auction_product', 0)
                ->firstOrFail();
            return $this->success(new ProductBulkOrderDetailResource($product));


        } catch (ModelNotFoundException $e) {
            return $this->error('Product not found', 404);
        } catch (\Exception $e) {
            Log::channel('api_order')->error('Unexpected error in bulk_order_product_details: ' . $e->getMessage());
            return $this->error('Something went wrong. Please try again later.', 500);
        }
    }
    public function bulk_order_save(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'items' => 'required|array',
            'items.*.product_id' => 'required|string|exists:products,slug',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.notes' => 'nullable|string',
            'address_id' => 'required|exists:addresses,id',
            'order_from' => 'required|in:web,android,ios',
            'paymentMethod' => 'required|exists:stripe_cards,stripe_card_id',
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }
        $paymentMethod=$request->paymentMethod;
        $additional_info=$request->additional_info;
        $pickup_point=$request->pickup_point;
        $shipping_type=$request->shipping_type;
        $order_from=$request->order_from;
        $carrier_id=$request->carrier_id;
        $user_id = auth()->user()->id;
        $user = auth()->user();
        $is_gift = 0;

        $address = Address::where('id', $request->address_id)
            ->where('user_id', $user_id)
            ->first();

        if (empty($address)) {
            return $this->validation_error(
                400,
                'Invalid address',
                [],
                400
            );
        }
        DB::beginTransaction();
        try {
            $shipping_address = $this->apiOrderService->formatShippingAddress($address);
            $combined_order = $this->apiOrderService->createCombinedOrder($user_id, $shipping_address);
            $seller_products =  $this->apiOrderService->groupProductsBySeller($request->items);
            $combined_order_code = $combined_order->combined_order_code;
            foreach ($seller_products as $seller_product) {
                $order_code = count($seller_products) > 1 ? $combined_order_code . '-' . $i++ : $combined_order_code;
                $order = $this->apiOrderService->createOrder($order_code,$combined_order->id, $user_id, $shipping_address, $additional_info, $is_gift, $order_from);
                $this->apiOrderService->processOrderDetails($order, $seller_product);
                $this->apiOrderService->updateOrderTotals($order, $seller_product);
                $this->apiOrderService->processCouponIfExists($order, $seller_product, $user_id);

                $combined_order->grand_total += $order->grand_total;
            }
            $combined_order->save();
            DB::commit();
            $paymentIntent = $this->apiOrderService->makePaymentIntent(
                $combined_order,
                $paymentMethod,
                $user->stripeCustomer->stripe_customer_id,
                $user->email,
                '',
                ''
            );
            if ($paymentIntent->status !== 'succeeded') {
                Log::channel('api_stripe_payment')->error(
                    'Payment failed',
                    [
                        'order_code' => $combined_order->combined_order_code,
                        'user_id' => $user->id,
                        'payment_intent_status' => $paymentIntent->status
                    ]
                );
                return $this->error('PAYMENT_FAILED', 'Payment was not successful', null, 400);
            }

            $card_details = StripeCard::where('stripe_card_id', $request->paymentMethod)->first();
            $paymentStatusUpdate =$this->apiOrderService->processPayment(
                $paymentIntent,
                $combined_order,
                $card_details
            );
            if(!$paymentStatusUpdate){
                return $this->error(
                    'ORDER_PROCESSING_ERROR',
                    'Error processing order after payment',
                    null, 500
                );
            }
            return $this->success(
                [
                    'message' => 'Order placed successfully',
                    'orderId' => $combined_order->combined_order_code,
                    'status' => OrderStatus::ORDER_PLACED,
                    'totalAmount' => $combined_order->grand_total,
                    'estimatedDelivery' => '',
                    'paymentStatus' => 'paid',
                    'trackingNumber' => null,
                ],
                'Order placed successfully',
                200
            );
        } catch (\Exception $e) {
            DB::rollBack();
            Log::channel('api_order')->error('Error in bulk order save: ' . $e->getMessage());
            return $this->error(
                'INTERNAL_SERVER_ERROR',
                'Something went wrong while processing your order',
                500
            );
        }
    }
    public function dropshipper_orders(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'page' => 'nullable|integer|min:1',
            'limit' => 'nullable|integer|min:1|max:50',
            'status' => 'nullable|string|in:pending,processing,shipped,delivered,cancelled,refunded',
            'search' => 'nullable|string|max:255',
            'start_date' => 'nullable|date_format:Y-m-d',
            'end_date' => 'nullable|date_format:Y-m-d|after_or_equal:start_date',
            'sort_by' => 'nullable|string|in:created_at,total,status',
            'sort_order' => 'nullable|string|in:asc,desc'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            $limit = $request->input('limit', 20);
            $query = Order::with(['user', 'orderDetails'])
                ->where('seller_id', auth()->id());

            // Apply search filter
            if ($request->search) {
                $query->where(function($q) use ($request) {
                    $q->where('code', 'like', '%' . $request->search . '%')
                        ->orWhereHas('user', function($q) use ($request) {
                            $q->where('name', 'like', '%' . $request->search . '%');
                        });
                });
            }

            // Apply status filter
            if ($request->status) {
                $query->where('delivery_status', $request->status);
            }

            // Apply date filters
            if ($request->start_date) {
                $query->whereDate('created_at', '>=', $request->start_date);
            }
            if ($request->end_date) {
                $query->whereDate('created_at', '<=', $request->end_date);
            }

            // Apply sorting
            $sort_by = $request->input('sort_by', 'created_at');
            $sort_order = $request->input('sort_order', 'desc');

            switch ($sort_by) {
                case 'total':
                    $query->orderBy('grand_total', $sort_order);
                    break;
                case 'status':
                    $query->orderBy('delivery_status', $sort_order);
                    break;
                default:
                    $query->orderBy('created_at', $sort_order);
            }

            $orders = $query->paginate($limit);

            $formatted_orders = $orders->map(function ($order) {
                $shipping_address = json_decode($order->shipping_address);
                return [
                    'id' => $order->code,
                    'customer' => [
                        'id' => $order->user_id,
                        'name' => $order->user->name,
                        'email' => $order->user->email
                    ],
                    'order_date' => $order->created_at,
                    'status' => $order->delivery_status,
                    'tracking_number' => $order->tracking_number,
                    'tracking_url' => $order->tracking_url,
                    'item_count' => $order->orderDetails->sum('quantity'),
                    'subtotal' => $order->orderDetails->sum('price'),
                    'shipping' => $order->orderDetails->sum('shipping_cost'),
                    'tax' => $order->orderDetails->sum('tax'),
                    'total' => $order->grand_total,
                    'commission' => $order->commission,
                    'commission_status' => $order->commission_status,
                    'shipping_address' => [
                        'name' => $shipping_address->name ?? '',
                        'line1' => $shipping_address->street_address ?? '',
                        'line2' => $shipping_address->apartment_address ?? '',
                        'city' => $shipping_address->city ?? '',
                        'state' => $shipping_address->state ?? '',
                        'postal_code' => $shipping_address->postal_code ?? '',
                        'country' => $shipping_address->country ?? ''
                    ],
                    'estimated_delivery' => $order->estimated_delivery
                ];
            });

            return $this->success([
                'orders' => $formatted_orders,
                'pagination' => [
                    'total' => $orders->total(),
                    'count' => $orders->count(),
                    'per_page' => $orders->perPage(),
                    'current_page' => $orders->currentPage(),
                    'total_pages' => $orders->lastPage(),
                    'links' => [
                        'next' => $orders->nextPageUrl()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::channel('api_order')->error('Error in dropshipper orders: ' . $e->getMessage());
            return $this->error(
                'INTERNAL_SERVER_ERROR',
                'Something went wrong while fetching orders',
                500
            );
        }
    }

}


