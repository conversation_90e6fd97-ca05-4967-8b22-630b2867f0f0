<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\Faq;
use App\Models\FaqCategory;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ReturnFaqController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $category = $request->query('category');

        $query = Faq::query()
            ->where('type', 'returns')
            ->with('category')
            ->orderBy('order');

        if ($category) {
            $query->whereHas('category', function ($q) use ($category) {
                $q->where('id', $category);
            });
        }

        $faqs = $query->get();
        $categories = FaqCategory::where('type', 'returns')->get();

        return response()->json([
            'success' => true,
            'data' => [
                'faqs' => $faqs,
                'categories' => $categories,
            ],
        ]);
    }
} 