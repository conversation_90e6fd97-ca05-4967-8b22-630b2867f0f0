<?php

namespace App\Http\Controllers\Api\V3\Account;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\User\UserResource;
use App\Models\User;
use App\Models\UserPreference;
use App\Models\Phone;
use App\Models\Email;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class ApiAccountController extends ApiResponse
{
    /**
     * Get user profile
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getProfile(Request $request): JsonResponse
    {
        $user = $request->user();
        
        // Get user profile details
        $profile = [
            'id' => $user->id,
            'firstName' => $user->name ? explode(' ', $user->name)[0] : '',
            'lastName' => $user->name && count(explode(' ', $user->name)) > 1 ? explode(' ', $user->name, 2)[1] : '',
            'name' => $user->name,
            'displayName' => $user->name,
            'email' => $user->email,
            'phone' => $user->phone ?? '',
            'avatarUrl' => uploaded_asset($user->avatar_original),
            'profilePicture' => uploaded_asset($user->avatar_original),
            'bio' => $user->bio ?? '',
            'dateOfBirth' => $user->date_of_birth ?? '',
            'gender' => $user->gender ?? '',
            'language' => $user->language ?? 'en',
            'country' => $user->country ?? 'US',
            'timezone' => $user->timezone ?? 'UTC',
            'createdAt' => $user->created_at->toISOString(),
            'updatedAt' => $user->updated_at->toISOString(),
        ];
        
        return $this->success($profile);
    }
    
    /**
     * Update user profile
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function updateProfile(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'firstName' => 'sometimes|string|max:255',
            'lastName' => 'sometimes|string|max:255',
            'phone' => 'sometimes|nullable|string|max:20',
            'bio' => 'sometimes|nullable|string|max:1000',
            'dateOfBirth' => 'sometimes|nullable|date',
            'gender' => 'sometimes|nullable|string|in:male,female,other,prefer_not_to_say',
            'language' => 'sometimes|string|max:10',
            'country' => 'sometimes|string|max:10',
            'timezone' => 'sometimes|string|max:50',
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid profile information',
                $validator->errors()->messages(),
                422
            );
        }
        
        $user = $request->user();
        
        // Update name if first or last name is provided
        if ($request->has('firstName') || $request->has('lastName')) {
            $firstName = $request->input('firstName', explode(' ', $user->name)[0]);
            $lastName = $request->input('lastName', count(explode(' ', $user->name)) > 1 ? explode(' ', $user->name, 2)[1] : '');
            $user->name = $firstName . ' ' . $lastName;
        }
        
        // Update phone number
        if ($request->has('phone')) {
            $user->phone = $request->input('phone');
        }
        
        // Update other fields
        if ($request->has('bio')) {
            $user->bio = $request->input('bio');
        }
        
        if ($request->has('dateOfBirth')) {
            $user->date_of_birth = $request->input('dateOfBirth');
        }
        
        if ($request->has('gender')) {
            $user->gender = $request->input('gender');
        }
        
        if ($request->has('language')) {
            $user->language = $request->input('language');
        }
        
        if ($request->has('country')) {
            $user->country = $request->input('country');
        }
        
        if ($request->has('timezone')) {
            $user->timezone = $request->input('timezone');
        }
        
        $user->save();
        
        return $this->getProfile($request);
    }
    
    /**
     * Get account settings
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function getAccountSettings(Request $request): JsonResponse
    {
        $user = $request->user();
        $preferences = $user->preferences()->pluck('value', 'key')->toArray();
        
        $settings = [
            'currency' => $preferences['currency'] ?? 'USD',
            'language' => $preferences['language'] ?? 'en',
            'timezone' => $preferences['timezone'] ?? 'UTC',
            'dateFormat' => $preferences['date_format'] ?? 'MM/DD/YYYY',
            'timeFormat' => $preferences['time_format'] ?? '12h',
            'autoLogout' => (int)($preferences['auto_logout'] ?? 30),
            'theme' => $preferences['theme'] ?? 'system',
            'accessibility' => [
                'highContrast' => isset($preferences['high_contrast']) ? (bool)$preferences['high_contrast'] : false,
                'largerText' => isset($preferences['larger_text']) ? (bool)$preferences['larger_text'] : false,
                'reducedMotion' => isset($preferences['reduced_motion']) ? (bool)$preferences['reduced_motion'] : false,
            ]
        ];
        
        return $this->success($settings);
    }
    
    /**
     * Update account settings
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function updateAccountSettings(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'currency' => 'sometimes|string|max:10',
            'language' => 'sometimes|string|max:10',
            'timezone' => 'sometimes|string|max:50',
            'dateFormat' => 'sometimes|string|max:20',
            'timeFormat' => 'sometimes|string|in:12h,24h',
            'autoLogout' => 'sometimes|integer|min:0|max:1440',
            'theme' => 'sometimes|string|in:light,dark,system',
            'accessibility.highContrast' => 'sometimes|boolean',
            'accessibility.largerText' => 'sometimes|boolean',
            'accessibility.reducedMotion' => 'sometimes|boolean',
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid settings',
                $validator->errors()->messages(),
                422
            );
        }
        
        $user = $request->user();
        
        $settingsMap = [
            'currency' => 'currency',
            'language' => 'language',
            'timezone' => 'timezone',
            'dateFormat' => 'date_format',
            'timeFormat' => 'time_format',
            'autoLogout' => 'auto_logout',
            'theme' => 'theme',
            'accessibility.highContrast' => 'high_contrast',
            'accessibility.largerText' => 'larger_text',
            'accessibility.reducedMotion' => 'reduced_motion',
        ];
        
        foreach ($settingsMap as $requestKey => $dbKey) {
            $value = $request->input($requestKey);
            if ($value !== null) {
                UserPreference::updateOrCreate(
                    ['user_id' => $user->id, 'key' => $dbKey],
                    ['value' => $value]
                );
            }
        }
        
        return $this->getAccountSettings($request);
    }
    
    /**
     * Deactivate user account
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function deactivateAccount(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'password' => 'required|string',
            'reason' => 'sometimes|nullable|string|max:1000',
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                422
            );
        }
        
        $user = $request->user();
        
        if (!Hash::check($request->password, $user->password)) {
            return $this->error(
                'INVALID_PASSWORD',
                'The password is incorrect',
                'Please provide a valid password to deactivate your account',
                401
            );
        }
        
        // Store deactivation reason if provided
        if ($request->has('reason')) {
            UserPreference::updateOrCreate(
                ['user_id' => $user->id, 'key' => 'deactivation_reason'],
                ['value' => $request->reason]
            );
        }
        
        // Mark the user as inactive
        $user->is_active = false;
        $user->deactivated_at = now();
        $user->save();
        
        // Revoke all tokens
        $user->tokens()->delete();
        
        return $this->success(null, 'Account successfully deactivated');
    }
    
    /**
     * Reactivate user account
     * 
     * @param Request $request
     * @return JsonResponse
     */
    public function reactivateAccount(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string',
        ]);
        
        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                422
            );
        }
        
        $user = User::where('email', $request->email)
            ->where('is_active', false)
            ->first();
        
        if (!$user || !Hash::check($request->password, $user->password)) {
            return $this->error(
                'INVALID_CREDENTIALS',
                'The provided credentials are incorrect',
                'Please provide valid credentials to reactivate your account',
                401
            );
        }
        
        // Reactivate the account
        $user->is_active = true;
        $user->deactivated_at = null;
        $user->save();
        
        // Generate a new token
        $token = $user->createToken('auth_token')->plainTextToken;
        
        return $this->success([
            'user' => new UserResource($user),
            'token' => $token
        ], 'Account successfully reactivated');
    }
} 