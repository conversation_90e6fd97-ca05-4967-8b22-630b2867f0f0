<?php

namespace App\Http\Controllers\Api\V3\Promotions;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\Discount\DiscountResource;
use App\Http\Resources\V3\Discount\ProductDiscountResource;
use App\Http\Resources\V3\Discount\VolumeDiscountResource;
use App\Models\Discount;
use App\Models\ProductDiscount;
use App\Models\VolumeDiscount;
use App\Models\Product;
use App\Models\Cart;
use App\Models\Category;
use App\Services\DiscountService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ApiDiscountController extends ApiResponse
{
    protected $discountService;

    public function __construct(DiscountService $discountService)
    {
        $this->discountService = $discountService;
    }

    /**
     * Get a paginated list of discounts
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'active' => 'boolean',
                'search' => 'string|max:100',
                'type' => 'string|in:percentage,fixed,free_shipping,buy_x_get_y',
                'limit' => 'integer|min:1|max:50',
                'page' => 'integer|min:1',
                'sort_by' => 'string|in:created_at,end_date,usage_count,value',
                'sort_direction' => 'string|in:asc,desc'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $limit = $request->input('limit', 15);
            $page = $request->input('page', 1);
            
            $query = Discount::query();
            
            // Apply filters
            if ($request->has('active')) {
                $query->where('is_active', $request->boolean('active'));
            }
            
            if ($request->has('search')) {
                $search = $request->input('search');
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('code', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            }
            
            if ($request->has('type')) {
                $query->where('type', $request->input('type'));
            }
            
            // Apply sorting
            $sortBy = $request->input('sort_by', 'created_at');
            $sortDirection = $request->input('sort_direction', 'desc');
            $query->orderBy($sortBy, $sortDirection);
            
            // Get paginated results
            $discounts = $query->paginate($limit, ['*'], 'page', $page);
            
            return $this->success([
                'discounts' => DiscountResource::collection($discounts),
                'pagination' => [
                    'total' => $discounts->total(),
                    'per_page' => $discounts->perPage(),
                    'current_page' => $discounts->currentPage(),
                    'last_page' => $discounts->lastPage()
                ]
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get a specific discount by ID
     *
     * @param string $id
     * @return JsonResponse
     */
    public function show(string $id): JsonResponse
    {
        try {
            $discount = Discount::findOrFail($id);
            return $this->success(new DiscountResource($discount));
        } catch (\Exception $e) {
            return $this->error('Discount not found', 'The requested discount does not exist', 404);
        }
    }

    /**
     * Get active discounts
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getActiveDiscounts(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'integer|min:1|max:50'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $limit = $request->input('limit', 10);
            
            $now = now();
            $activeDiscounts = Discount::where('is_active', true)
                ->where('start_date', '<=', $now)
                ->where(function($query) use ($now) {
                    $query->where('end_date', '>=', $now)
                        ->orWhereNull('end_date');
                })
                ->orderBy('priority', 'desc')
                ->limit($limit)
                ->get();
                
            return $this->success(DiscountResource::collection($activeDiscounts));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get product-specific discounts
     *
     * @param string $productId
     * @return JsonResponse
     */
    public function getProductDiscounts(string $productId): JsonResponse
    {
        try {
            // Check if product exists
            $product = Product::find($productId);
            if (!$product) {
                return $this->error('Product not found', 'The requested product does not exist', 404);
            }
            
            // Get product-specific discounts
            $now = now();
            
            // Product discounts
            $productDiscounts = ProductDiscount::where('product_id', $productId)
                ->where('is_active', true)
                ->where(function($query) use ($now) {
                    $query->where('start_date', '<=', $now)
                        ->where(function($q) use ($now) {
                            $q->where('end_date', '>=', $now)
                                ->orWhereNull('end_date');
                        });
                })
                ->get();
                
            // Category discounts - get product categories and find discounts for those
            $categoryIds = $product->categories()->pluck('category_id')->toArray();
            
            // General discounts that apply to this product
            $generalDiscounts = Discount::where('is_active', true)
                ->where('start_date', '<=', $now)
                ->where(function($query) use ($now) {
                    $query->where('end_date', '>=', $now)
                        ->orWhereNull('end_date');
                })
                ->where(function($query) use ($productId, $categoryIds) {
                    // Global discounts (no product/category restrictions)
                    $query->whereNull('products')
                        ->whereNull('categories')
                        
                        // Product-specific discounts
                        ->orWhereJsonContains('products', $productId)
                        
                        // Category-specific discounts
                        ->orWhere(function($q) use ($categoryIds) {
                            foreach ($categoryIds as $categoryId) {
                                $q->orWhereJsonContains('categories', $categoryId);
                            }
                        });
                })
                ->whereNotJsonContains('excluded_products', $productId)
                ->get();
                
            return $this->success([
                'product_discounts' => ProductDiscountResource::collection($productDiscounts),
                'general_discounts' => DiscountResource::collection($generalDiscounts)
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get discounts for a specific category
     *
     * @param string $categoryId
     * @return JsonResponse
     */
    public function getCategoryDiscounts(string $categoryId): JsonResponse
    {
        try {
            // Check if category exists
            $category = Category::find($categoryId);
            if (!$category) {
                return $this->error('Category not found', 'The requested category does not exist', 404);
            }
            
            $now = now();
            
            // Get all active discounts for this category
            $discounts = Discount::where('is_active', true)
                ->where('start_date', '<=', $now)
                ->where(function($query) use ($now) {
                    $query->where('end_date', '>=', $now)
                        ->orWhereNull('end_date');
                })
                ->where(function($query) use ($categoryId) {
                    // Global discounts (no category restrictions)
                    $query->whereNull('categories')
                    
                    // Category-specific discounts
                    ->orWhereJsonContains('categories', $categoryId);
                })
                ->get();
                
            return $this->success(DiscountResource::collection($discounts));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get featured discounts
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getFeatured(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'limit' => 'integer|min:1|max:20'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $limit = $request->input('limit', 6);
            
            $now = now();
            $featuredDiscounts = Discount::where('is_active', true)
                ->where('is_featured', true)
                ->where('start_date', '<=', $now)
                ->where(function($query) use ($now) {
                    $query->where('end_date', '>=', $now)
                        ->orWhereNull('end_date');
                })
                ->orderBy('priority', 'desc')
                ->limit($limit)
                ->get();
                
            return $this->success(DiscountResource::collection($featuredDiscounts));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get discount statistics
     *
     * @return JsonResponse
     */
    public function getStats(): JsonResponse
    {
        try {
            $now = now();
            
            // Total discounts count
            $totalDiscounts = Discount::count();
            
            // Active discounts count
            $activeDiscounts = Discount::where('is_active', true)
                ->where('start_date', '<=', $now)
                ->where(function($query) use ($now) {
                    $query->where('end_date', '>=', $now)
                        ->orWhereNull('end_date');
                })
                ->count();
                
            // Popular discounts
            $popularDiscounts = Discount::orderBy('usage_count', 'desc')
                ->limit(5)
                ->get(['id', 'name', 'usage_count']);
                
            // Savings by discount type
            $savingsByType = Discount::selectRaw('type, SUM(value * usage_count) as amount')
                ->groupBy('type')
                ->get()
                ->map(function($item) {
                    return [
                        'type' => $item->type,
                        'amount' => $item->amount
                    ];
                });
                
            return $this->success([
                'total_discounts' => $totalDiscounts,
                'active_discounts' => $activeDiscounts,
                'total_savings' => Discount::sum(\DB::raw('value * usage_count')),
                'popular_discounts' => $popularDiscounts,
                'savings_by_type' => $savingsByType
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Validate a discount
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function validateDiscount(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'code' => 'required|string|max:50',
                'cart_id' => 'string',
                'cart_amount' => 'numeric'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $code = $request->input('code');
            $cartId = $request->input('cart_id');
            $cartAmount = $request->input('cart_amount');
            $userId = Auth::id();
            
            // Get cart if available
            $cart = null;
            if ($userId) {
                $cart = Cart::where('user_id', $userId)->first();
            } elseif ($cartId) {
                $cart = Cart::where('id', $cartId)->orWhere('temp_user_id', $cartId)->first();
            }
            
            // Validate the discount
            $result = $this->discountService->validateDiscount($code, $cart, $cartAmount);
            
            if (!$result['valid']) {
                return $this->success([
                    'valid' => false,
                    'message' => $result['message']
                ]);
            }
            
            return $this->success([
                'valid' => true,
                'message' => $result['message'],
                'discount' => new DiscountResource($result['discount']),
                'discount_amount' => $result['discount_amount'] ?? 0,
                'applicable_items' => $result['applicable_items'] ?? []
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Apply a discount to cart
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function applyDiscount(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'code' => 'required|string|max:50',
                'cart_id' => 'string'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $code = $request->input('code');
            $cartId = $request->input('cart_id');
            $userId = Auth::id();
            
            // Get cart
            $cart = null;
            if ($userId) {
                $cart = Cart::where('user_id', $userId)->first();
            } elseif ($cartId) {
                $cart = Cart::where('id', $cartId)->orWhere('temp_user_id', $cartId)->first();
            }
            
            if (!$cart) {
                return $this->error('Cart not found', 'Unable to find shopping cart', 404);
            }
            
            // Apply discount
            $result = $this->discountService->applyDiscount($code, $cart);
            
            if (!$result['success']) {
                return $this->error('Apply failed', $result['message'], 400);
            }
            
            return $this->success([
                'message' => $result['message'],
                'discount_amount' => $result['discount_amount'],
                'discount' => new DiscountResource($result['discount']),
                'cart_total' => $result['cart_total']
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get volume discounts for a product
     *
     * @param string $productId
     * @return JsonResponse
     */
    public function getVolumeDiscounts(string $productId): JsonResponse
    {
        try {
            // Check if product exists
            $product = Product::find($productId);
            if (!$product) {
                return $this->error('Product not found', 'The requested product does not exist', 404);
            }
            
            // Get volume discounts for the product
            $volumeDiscount = VolumeDiscount::where('product_id', $productId)->first();
            
            if (!$volumeDiscount) {
                return $this->success(null);
            }
            
            return $this->success(new VolumeDiscountResource($volumeDiscount));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }
} 