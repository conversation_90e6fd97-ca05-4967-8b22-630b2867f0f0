<?php

namespace App\Http\Controllers\Api\V3\Promotions;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\Campaign\SeasonalCampaignResource;
use App\Models\SeasonalCampaign;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ApiSeasonalCampaignController extends ApiResponse
{
    /**
     * Get active seasonal campaigns
     *
     * @return JsonResponse
     */
    public function getActiveSeasonalCampaigns(): JsonResponse
    {
        try {
            $now = now();
            
            $campaigns = SeasonalCampaign::where('is_active', true)
                ->where('start_date', '<=', $now)
                ->where('end_date', '>=', $now)
                ->orderBy('priority', 'desc')
                ->get();
                
            return $this->success(SeasonalCampaignResource::collection($campaigns));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get upcoming seasonal campaigns
     *
     * @return JsonResponse
     */
    public function getUpcomingSeasonalCampaigns(): JsonResponse
    {
        try {
            $now = now();
            
            $campaigns = SeasonalCampaign::where('is_active', true)
                ->where('start_date', '>', $now)
                ->orderBy('start_date', 'asc')
                ->get();
                
            return $this->success(SeasonalCampaignResource::collection($campaigns));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get a specific seasonal campaign by ID
     *
     * @param string $id
     * @return JsonResponse
     */
    public function getSeasonalCampaignById(string $id): JsonResponse
    {
        try {
            $campaign = SeasonalCampaign::find($id);
            
            if (!$campaign) {
                return $this->error('Campaign not found', 'The requested campaign does not exist', 404);
            }
            
            return $this->success(new SeasonalCampaignResource($campaign));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get seasonal campaigns by type
     *
     * @param string $type
     * @return JsonResponse
     */
    public function getSeasonalCampaignByType(string $type): JsonResponse
    {
        try {
            $validator = Validator::make(['type' => $type], [
                'type' => 'required|string|in:holiday,sale,promotion,event,exclusive'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Invalid campaign type', 422);
            }
            
            $now = now();
            
            $campaigns = SeasonalCampaign::where('type', $type)
                ->where('is_active', true)
                ->where(function($query) use ($now) {
                    // Include active or upcoming campaigns
                    $query->where(function($q) use ($now) {
                        $q->where('start_date', '<=', $now)
                          ->where('end_date', '>=', $now);
                    })
                    ->orWhere('start_date', '>', $now);
                })
                ->orderBy('start_date', 'asc')
                ->get();
                
            return $this->success(SeasonalCampaignResource::collection($campaigns));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }
} 