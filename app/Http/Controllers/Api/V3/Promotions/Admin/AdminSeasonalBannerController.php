<?php

namespace App\Http\Controllers\Api\V3\Promotions\Admin;

use App\Http\Controllers\Api\V3\ApiResponse;
use App\Http\Resources\V3\Banner\SeasonalBannerResource;
use App\Models\Promotions\SeasonalBanner;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class AdminSeasonalBannerController extends ApiResponse
{
    /**
     * Get all seasonal banners
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'page' => 'integer|min:1',
                'limit' => 'integer|min:1|max:100',
                'status' => 'nullable|string|in:active,upcoming,expired,all',
                'season_type' => 'nullable|string|max:50',
                'campaign_id' => 'nullable|integer|exists:seasonal_campaigns,id',
                'section_position' => 'nullable|string|max:50',
                'sort_by' => 'nullable|string|in:created_at,display_order,title,start_date,end_date',
                'sort_order' => 'nullable|string|in:asc,desc',
                'search' => 'nullable|string|max:255'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $page = $request->input('page', 1);
            $limit = $request->input('limit', 20);
            $status = $request->input('status', 'all');
            $seasonType = $request->input('season_type');
            $campaignId = $request->input('campaign_id');
            $sectionPosition = $request->input('section_position');
            $sortBy = $request->input('sort_by', 'display_order');
            $sortOrder = $request->input('sort_order', 'asc');
            $search = $request->input('search');

            $query = SeasonalBanner::query();

            // Apply status filter
            if ($status !== 'all') {
                $now = now();
                
                switch ($status) {
                    case 'active':
                        $query->where('is_active', true)
                            ->where(function($q) use ($now) {
                                $q->whereNull('start_date')
                                    ->orWhere('start_date', '<=', $now);
                            })
                            ->where(function($q) use ($now) {
                                $q->whereNull('end_date')
                                    ->orWhere('end_date', '>=', $now);
                            });
                        break;
                    case 'upcoming':
                        $query->where('is_active', true)
                            ->whereNotNull('start_date')
                            ->where('start_date', '>', $now);
                        break;
                    case 'expired':
                        $query->where(function($q) use ($now) {
                            $q->where('is_active', false)
                                ->orWhere(function($q2) use ($now) {
                                    $q2->whereNotNull('end_date')
                                        ->where('end_date', '<', $now);
                                });
                        });
                        break;
                }
            }

            // Apply season type filter
            if ($seasonType) {
                $query->where('season_type', $seasonType);
            }

            // Apply campaign filter
            if ($campaignId) {
                $query->where('campaign_id', $campaignId);
            }

            // Apply section position filter
            if ($sectionPosition) {
                $query->where('section_position', $sectionPosition);
            }

            // Apply search
            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('title', 'like', '%' . $search . '%')
                        ->orWhere('subtitle', 'like', '%' . $search . '%')
                        ->orWhere('description', 'like', '%' . $search . '%');
                });
            }

            // Apply sorting
            $query->orderBy($sortBy, $sortOrder);

            // Load campaign relation
            $query->with('campaign');

            // Get paginated results
            $seasonalBanners = $query->paginate($limit, ['*'], 'page', $page);

            return $this->success([
                'seasonal_banners' => SeasonalBannerResource::collection($seasonalBanners),
                'pagination' => [
                    'total' => $seasonalBanners->total(),
                    'per_page' => $seasonalBanners->perPage(),
                    'current_page' => $seasonalBanners->currentPage(),
                    'last_page' => $seasonalBanners->lastPage(),
                    'from' => $seasonalBanners->firstItem(),
                    'to' => $seasonalBanners->lastItem()
                ]
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Create a new seasonal banner
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'subtitle' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'season_type' => 'required|string|max:50',
                'image_url' => 'required|string|max:255',
                'mobile_image_url' => 'nullable|string|max:255',
                'background_color' => 'nullable|string|max:50',
                'text_color' => 'nullable|string|max:50',
                'button_text' => 'nullable|string|max:100',
                'button_url' => 'nullable|string|max:255',
                'overlay_opacity' => 'nullable|numeric|min:0|max:1',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after_or_equal:start_date',
                'is_active' => 'boolean',
                'display_order' => 'nullable|integer|min:0',
                'section_position' => 'required|string|max:50',
                'campaign_id' => 'nullable|integer|exists:seasonal_campaigns,id',
                'show_countdown' => 'boolean',
                'countdown_type' => 'nullable|string|max:50',
                'countdown_background' => 'nullable|string|max:50',
                'countdown_text_color' => 'nullable|string|max:50',
                'metadata' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            // Create seasonal banner
            $seasonalBanner = new SeasonalBanner();
            $seasonalBanner->title = $request->input('title');
            $seasonalBanner->subtitle = $request->input('subtitle');
            $seasonalBanner->description = $request->input('description');
            $seasonalBanner->season_type = $request->input('season_type');
            $seasonalBanner->image_url = $request->input('image_url');
            $seasonalBanner->mobile_image_url = $request->input('mobile_image_url');
            $seasonalBanner->background_color = $request->input('background_color');
            $seasonalBanner->text_color = $request->input('text_color');
            $seasonalBanner->button_text = $request->input('button_text');
            $seasonalBanner->button_url = $request->input('button_url');
            $seasonalBanner->overlay_opacity = $request->input('overlay_opacity');
            $seasonalBanner->start_date = $request->input('start_date');
            $seasonalBanner->end_date = $request->input('end_date');
            $seasonalBanner->is_active = $request->input('is_active', true);
            $seasonalBanner->display_order = $request->input('display_order', 0);
            $seasonalBanner->section_position = $request->input('section_position');
            $seasonalBanner->campaign_id = $request->input('campaign_id');
            $seasonalBanner->show_countdown = $request->input('show_countdown', false);
            $seasonalBanner->countdown_type = $request->input('countdown_type');
            $seasonalBanner->countdown_background = $request->input('countdown_background');
            $seasonalBanner->countdown_text_color = $request->input('countdown_text_color');
            $seasonalBanner->metadata = $request->input('metadata');
            
            $seasonalBanner->save();

            DB::commit();
            return $this->success(new SeasonalBannerResource($seasonalBanner), 'Seasonal banner created successfully', 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get a seasonal banner by ID
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        try {
            $seasonalBanner = SeasonalBanner::with('campaign')->find($id);

            if (!$seasonalBanner) {
                return $this->error('Seasonal banner not found', 'The requested seasonal banner does not exist', 404);
            }

            return $this->success(new SeasonalBannerResource($seasonalBanner));
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Update a seasonal banner
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, int $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $seasonalBanner = SeasonalBanner::find($id);

            if (!$seasonalBanner) {
                return $this->error('Seasonal banner not found', 'The requested seasonal banner does not exist', 404);
            }

            $validator = Validator::make($request->all(), [
                'title' => 'nullable|string|max:255',
                'subtitle' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'season_type' => 'nullable|string|max:50',
                'image_url' => 'nullable|string|max:255',
                'mobile_image_url' => 'nullable|string|max:255',
                'background_color' => 'nullable|string|max:50',
                'text_color' => 'nullable|string|max:50',
                'button_text' => 'nullable|string|max:100',
                'button_url' => 'nullable|string|max:255',
                'overlay_opacity' => 'nullable|numeric|min:0|max:1',
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after_or_equal:start_date',
                'is_active' => 'boolean',
                'display_order' => 'nullable|integer|min:0',
                'section_position' => 'nullable|string|max:50',
                'campaign_id' => 'nullable|integer|exists:seasonal_campaigns,id',
                'show_countdown' => 'boolean',
                'countdown_type' => 'nullable|string|max:50',
                'countdown_background' => 'nullable|string|max:50',
                'countdown_text_color' => 'nullable|string|max:50',
                'metadata' => 'nullable|array'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            // Update seasonal banner fields if they are provided
            if ($request->has('title')) {
                $seasonalBanner->title = $request->input('title');
            }
            
            if ($request->has('subtitle')) {
                $seasonalBanner->subtitle = $request->input('subtitle');
            }
            
            if ($request->has('description')) {
                $seasonalBanner->description = $request->input('description');
            }
            
            if ($request->has('season_type')) {
                $seasonalBanner->season_type = $request->input('season_type');
            }
            
            if ($request->has('image_url')) {
                $seasonalBanner->image_url = $request->input('image_url');
            }
            
            if ($request->has('mobile_image_url')) {
                $seasonalBanner->mobile_image_url = $request->input('mobile_image_url');
            }
            
            if ($request->has('background_color')) {
                $seasonalBanner->background_color = $request->input('background_color');
            }
            
            if ($request->has('text_color')) {
                $seasonalBanner->text_color = $request->input('text_color');
            }
            
            if ($request->has('button_text')) {
                $seasonalBanner->button_text = $request->input('button_text');
            }
            
            if ($request->has('button_url')) {
                $seasonalBanner->button_url = $request->input('button_url');
            }
            
            if ($request->has('overlay_opacity')) {
                $seasonalBanner->overlay_opacity = $request->input('overlay_opacity');
            }
            
            if ($request->has('start_date')) {
                $seasonalBanner->start_date = $request->input('start_date');
            }
            
            if ($request->has('end_date')) {
                $seasonalBanner->end_date = $request->input('end_date');
            }
            
            if ($request->has('is_active')) {
                $seasonalBanner->is_active = $request->input('is_active');
            }
            
            if ($request->has('display_order')) {
                $seasonalBanner->display_order = $request->input('display_order');
            }
            
            if ($request->has('section_position')) {
                $seasonalBanner->section_position = $request->input('section_position');
            }
            
            if ($request->has('campaign_id')) {
                $seasonalBanner->campaign_id = $request->input('campaign_id');
            }
            
            if ($request->has('show_countdown')) {
                $seasonalBanner->show_countdown = $request->input('show_countdown');
            }
            
            if ($request->has('countdown_type')) {
                $seasonalBanner->countdown_type = $request->input('countdown_type');
            }
            
            if ($request->has('countdown_background')) {
                $seasonalBanner->countdown_background = $request->input('countdown_background');
            }
            
            if ($request->has('countdown_text_color')) {
                $seasonalBanner->countdown_text_color = $request->input('countdown_text_color');
            }
            
            if ($request->has('metadata')) {
                $seasonalBanner->metadata = $request->input('metadata');
            }
            
            $seasonalBanner->save();

            DB::commit();
            return $this->success(new SeasonalBannerResource($seasonalBanner), 'Seasonal banner updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Delete a seasonal banner
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $seasonalBanner = SeasonalBanner::find($id);

            if (!$seasonalBanner) {
                return $this->error('Seasonal banner not found', 'The requested seasonal banner does not exist', 404);
            }
            
            // Delete seasonal banner
            $seasonalBanner->delete();

            DB::commit();
            return $this->success(null, 'Seasonal banner deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Change seasonal banner status (activate/deactivate)
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function changeStatus(Request $request, int $id): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'is_active' => 'required|boolean'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $seasonalBanner = SeasonalBanner::find($id);

            if (!$seasonalBanner) {
                return $this->error('Seasonal banner not found', 'The requested seasonal banner does not exist', 404);
            }

            $seasonalBanner->is_active = $request->input('is_active');
            $seasonalBanner->save();

            return $this->success(new SeasonalBannerResource($seasonalBanner), 'Seasonal banner status updated successfully');
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Bulk update seasonal banner display order
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function updateDisplayOrder(Request $request): JsonResponse
    {
        DB::beginTransaction();
        try {
            $validator = Validator::make($request->all(), [
                'banners' => 'required|array',
                'banners.*.id' => 'required|integer|exists:seasonal_banners,id',
                'banners.*.display_order' => 'required|integer|min:0'
            ]);

            if ($validator->fails()) {
                return $this->error($validator->errors(), 'Validation failed', 422);
            }

            $banners = $request->input('banners');
            
            foreach ($banners as $banner) {
                SeasonalBanner::where('id', $banner['id'])
                    ->update(['display_order' => $banner['display_order']]);
            }

            DB::commit();
            return $this->success(null, 'Seasonal banner display orders updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get season types
     *
     * @return JsonResponse
     */
    public function getSeasonTypes(): JsonResponse
    {
        try {
            $seasonTypes = SeasonalBanner::distinct()->pluck('season_type')->values();
            
            return $this->success([
                'season_types' => $seasonTypes
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Get section positions
     *
     * @return JsonResponse
     */
    public function getSectionPositions(): JsonResponse
    {
        try {
            $sectionPositions = SeasonalBanner::distinct()->pluck('section_position')->values();
            
            return $this->success([
                'section_positions' => $sectionPositions
            ]);
        } catch (\Exception $e) {
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }

    /**
     * Duplicate a seasonal banner
     *
     * @param int $id
     * @return JsonResponse
     */
    public function duplicate(int $id): JsonResponse
    {
        DB::beginTransaction();
        try {
            $seasonalBanner = SeasonalBanner::find($id);

            if (!$seasonalBanner) {
                return $this->error('Seasonal banner not found', 'The requested seasonal banner does not exist', 404);
            }
            
            // Create new banner with the same properties
            $newBanner = $seasonalBanner->replicate();
            $newBanner->title = $seasonalBanner->title . ' (Copy)';
            $newBanner->is_active = false; // Set to inactive by default
            $newBanner->save();

            DB::commit();
            return $this->success(new SeasonalBannerResource($newBanner), 'Seasonal banner duplicated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('Server error', $e->getMessage(), 500);
        }
    }
} 