<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\User;
use App\Models\Integration;
use App\Models\IntegrationLog;
use App\Models\AvailableIntegration;
use Illuminate\Support\Str;
use Carbon\Carbon;

class ApiIntegrationsController extends ApiResponse
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get user integrations
     */
    public function getIntegrations(Request $request)
    {
        try {
            $integrations = Integration::where('user_id', auth()->id())->get();
            
            return $this->success([
                'integrations' => $integrations
            ]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch integrations',
                500
            );
        }
    }

    /**
     * Get integration details
     */
    public function getIntegration(Request $request, $id)
    {
        try {
            $integration = Integration::where('user_id', auth()->id())
                ->where('id', $id)
                ->first();
            
            if (!$integration) {
                return $this->error(
                    'NOT_FOUND',
                    'Integration not found',
                    404
                );
            }
            
            return $this->success($integration);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch integration details',
                500
            );
        }
    }

    /**
     * Connect integration
     */
    public function connectIntegration(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'providerId' => 'required|string',
            'authData' => 'required|array',
            'settings' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            // Check if available integration exists
            $availableIntegration = AvailableIntegration::where('id', $request->providerId)->first();
            
            if (!$availableIntegration) {
                return $this->error(
                    'NOT_FOUND',
                    'Integration provider not found',
                    404
                );
            }
            
            // Check if already connected
            $existingIntegration = Integration::where('user_id', auth()->id())
                ->where('provider', $availableIntegration->provider)
                ->first();
                
            if ($existingIntegration) {
                // Update existing integration
                $existingIntegration->status = 'connected';
                $existingIntegration->authData = json_encode($request->authData);
                $existingIntegration->settings = $request->has('settings') ? json_encode($request->settings) : $existingIntegration->settings;
                $existingIntegration->connectedAt = Carbon::now();
                $existingIntegration->save();
                
                // Log connection update
                IntegrationLog::create([
                    'integration_id' => $existingIntegration->id,
                    'event' => 'connection_updated',
                    'status' => 'success',
                    'message' => 'Integration connection updated',
                    'details' => null,
                    'timestamp' => Carbon::now()
                ]);
                
                return $this->success($existingIntegration);
            }
            
            // Create new integration
            $integration = Integration::create([
                'id' => (string) Str::uuid(),
                'user_id' => auth()->id(),
                'type' => $availableIntegration->type,
                'provider' => $availableIntegration->provider,
                'status' => 'connected',
                'name' => $availableIntegration->name,
                'description' => $availableIntegration->description,
                'authType' => $availableIntegration->authType,
                'iconUrl' => $availableIntegration->iconUrl,
                'authData' => json_encode($request->authData),
                'settings' => $request->has('settings') ? json_encode($request->settings) : null,
                'connectedAt' => Carbon::now(),
                'lastSyncedAt' => null,
                'expiresAt' => null,
                'metadata' => null
            ]);
            
            // Log new connection
            IntegrationLog::create([
                'integration_id' => $integration->id,
                'event' => 'connected',
                'status' => 'success',
                'message' => 'Integration connected successfully',
                'details' => null,
                'timestamp' => Carbon::now()
            ]);
            
            return $this->success($integration);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to connect integration',
                500
            );
        }
    }

    /**
     * Disconnect integration
     */
    public function disconnectIntegration(Request $request, $id)
    {
        try {
            $integration = Integration::where('user_id', auth()->id())
                ->where('id', $id)
                ->first();
            
            if (!$integration) {
                return $this->error(
                    'NOT_FOUND',
                    'Integration not found',
                    404
                );
            }
            
            $integration->status = 'disconnected';
            $integration->save();
            
            // Log disconnection
            IntegrationLog::create([
                'integration_id' => $integration->id,
                'event' => 'disconnected',
                'status' => 'success',
                'message' => 'Integration disconnected successfully',
                'details' => null,
                'timestamp' => Carbon::now()
            ]);
            
            return $this->success(['success' => true]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to disconnect integration',
                500
            );
        }
    }

    /**
     * Update integration settings
     */
    public function updateIntegrationSettings(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'settings' => 'required|array'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            $integration = Integration::where('user_id', auth()->id())
                ->where('id', $id)
                ->first();
            
            if (!$integration) {
                return $this->error(
                    'NOT_FOUND',
                    'Integration not found',
                    404
                );
            }
            
            $integration->settings = json_encode($request->settings);
            $integration->save();
            
            // Log settings update
            IntegrationLog::create([
                'integration_id' => $integration->id,
                'event' => 'settings_updated',
                'status' => 'success',
                'message' => 'Integration settings updated successfully',
                'details' => null,
                'timestamp' => Carbon::now()
            ]);
            
            return $this->success($integration);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to update integration settings',
                500
            );
        }
    }

    /**
     * Sync integration data
     */
    public function syncIntegration(Request $request, $id)
    {
        try {
            $integration = Integration::where('user_id', auth()->id())
                ->where('id', $id)
                ->first();
            
            if (!$integration) {
                return $this->error(
                    'NOT_FOUND',
                    'Integration not found',
                    404
                );
            }
            
            // Update last synced time
            $integration->lastSyncedAt = Carbon::now();
            $integration->save();
            
            // Log sync
            IntegrationLog::create([
                'integration_id' => $integration->id,
                'event' => 'sync_requested',
                'status' => 'success',
                'message' => 'Integration sync requested successfully',
                'details' => null,
                'timestamp' => Carbon::now()
            ]);
            
            return $this->success([
                'success' => true,
                'message' => 'Integration synced successfully'
            ]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to sync integration',
                500
            );
        }
    }

    /**
     * Get integration logs
     */
    public function getIntegrationLogs(Request $request, $id)
    {
        try {
            $integration = Integration::where('user_id', auth()->id())
                ->where('id', $id)
                ->first();
            
            if (!$integration) {
                return $this->error(
                    'NOT_FOUND',
                    'Integration not found',
                    404
                );
            }
            
            $logs = IntegrationLog::where('integration_id', $id)
                ->orderBy('timestamp', 'desc')
                ->get();
            
            return $this->success([
                'logs' => $logs
            ]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch integration logs',
                500
            );
        }
    }

    /**
     * Get available integrations
     */
    public function getAvailableIntegrations(Request $request)
    {
        try {
            $availableIntegrations = AvailableIntegration::all();
            
            return $this->success([
                'integrations' => $availableIntegrations
            ]);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to fetch available integrations',
                500
            );
        }
    }

    /**
     * Process OAuth callback
     */
    public function oauthCallback(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string',
            'state' => 'required|string',
            'provider' => 'required|string'
        ]);

        if ($validator->fails()) {
            return $this->validation_error(
                'VALIDATION_ERROR',
                'Please provide valid information',
                $validator->errors()->messages(),
                400
            );
        }

        try {
            // Find provider
            $availableIntegration = AvailableIntegration::where('provider', $request->provider)->first();
            
            if (!$availableIntegration) {
                return $this->error(
                    'NOT_FOUND',
                    'Integration provider not found',
                    404
                );
            }
            
            // Mock OAuth token exchange
            $authData = [
                'access_token' => 'mock_' . Str::random(32),
                'refresh_token' => 'mock_' . Str::random(32),
                'expires_in' => 3600,
                'token_type' => 'Bearer'
            ];
            
            // Check if integration exists for this provider
            $integration = Integration::where('user_id', auth()->id())
                ->where('provider', $request->provider)
                ->first();
                
            if ($integration) {
                // Update existing integration
                $integration->status = 'connected';
                $integration->authData = json_encode($authData);
                $integration->connectedAt = Carbon::now();
                $integration->expiresAt = Carbon::now()->addSeconds($authData['expires_in']);
                $integration->save();
            } else {
                // Create new integration
                $integration = Integration::create([
                    'id' => (string) Str::uuid(),
                    'user_id' => auth()->id(),
                    'type' => $availableIntegration->type,
                    'provider' => $availableIntegration->provider,
                    'status' => 'connected',
                    'name' => $availableIntegration->name,
                    'description' => $availableIntegration->description,
                    'authType' => $availableIntegration->authType,
                    'iconUrl' => $availableIntegration->iconUrl,
                    'authData' => json_encode($authData),
                    'settings' => null,
                    'connectedAt' => Carbon::now(),
                    'lastSyncedAt' => null,
                    'expiresAt' => Carbon::now()->addSeconds($authData['expires_in']),
                    'metadata' => null
                ]);
            }
            
            // Log OAuth connection
            IntegrationLog::create([
                'integration_id' => $integration->id,
                'event' => 'oauth_connected',
                'status' => 'success',
                'message' => 'OAuth authentication successful',
                'details' => null,
                'timestamp' => Carbon::now()
            ]);
            
            return $this->success($integration);
        } catch (\Exception $e) {
            return $this->error(
                'SERVER_ERROR',
                'Failed to process OAuth callback',
                500
            );
        }
    }
} 