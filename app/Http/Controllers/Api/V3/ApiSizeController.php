<?php

namespace App\Http\Controllers\Api\V3;

use App\Http\Controllers\Controller;
use App\Models\Size;
use App\Models\SizeChart;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ApiSizeController extends Controller
{
    /**
     * Get all sizes
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $sizes = Size::orderBy('display_order')->get();
        
        return response()->json([
            'success' => true,
            'message' => 'Sizes retrieved successfully',
            'data' => $sizes
        ]);
    }

    /**
     * Get all size charts
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAllSizeCharts()
    {
        $sizeCharts = SizeChart::with('categories')->get();
        
        return response()->json([
            'success' => true,
            'message' => 'Size charts retrieved successfully',
            'data' => $sizeCharts
        ]);
    }

    /**
     * Get size chart by category
     *
     * @param string $categoryId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSizeChartByCategory($categoryId)
    {
        $category = Category::find($categoryId);
        
        if (!$category) {
            return response()->json([
                'success' => false,
                'message' => 'Category not found'
            ], 404);
        }

        // Find size chart for this category or its parent categories
        $sizeChart = $this->findSizeChartForCategory($category);
        
        if (!$sizeChart) {
            return response()->json([
                'success' => false,
                'message' => 'No size chart found for this category'
            ], 404);
        }

        // Ensure the chart data has all the required fields for the frontend
        $sizeChart = $this->enrichSizeChartData($sizeChart, $category->name);

        return response()->json([
            'success' => true,
            'message' => 'Size chart retrieved successfully',
            'data' => $sizeChart
        ]);
    }

    /**
     * Get size chart by product
     *
     * @param string $productId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getSizeChartByProduct($productId)
    {
        $product = Product::find($productId);
        
        if (!$product) {
            return response()->json([
                'success' => false,
                'message' => 'Product not found'
            ], 404);
        }

        $category = Category::find($product->category_id);
        
        if (!$category) {
            return response()->json([
                'success' => false,
                'message' => 'Product category not found'
            ], 404);
        }

        // Find size chart for this category or its parent categories
        $sizeChart = $this->findSizeChartForCategory($category);
        
        if (!$sizeChart) {
            return response()->json([
                'success' => false,
                'message' => 'No size chart found for this product'
            ], 404);
        }

        // Ensure the chart data has all the required fields for the frontend
        $sizeChart = $this->enrichSizeChartData($sizeChart, $product->name);

        return response()->json([
            'success' => true,
            'message' => 'Size chart retrieved successfully',
            'data' => $sizeChart
        ]);
    }

    /**
     * Helper to find size chart for a category or its parents
     *
     * @param Category $category
     * @return SizeChart|null
     */
    private function findSizeChartForCategory($category)
    {
        // First check if there's a direct size chart for this category
        $sizeChart = SizeChart::whereHas('categories', function($query) use ($category) {
            $query->where('category_id', $category->id);
        })->first();
        
        if ($sizeChart) {
            return $sizeChart;
        }
        
        // If not found and this category has a parent, check parent category
        if ($category->parent_id) {
            $parentCategory = Category::find($category->parent_id);
            if ($parentCategory) {
                return $this->findSizeChartForCategory($parentCategory);
            }
        }
        
        return null;
    }

    /**
     * Enrich size chart data with all required fields for the frontend
     *
     * @param SizeChart $sizeChart
     * @param string $itemName
     * @return SizeChart
     */
    private function enrichSizeChartData(SizeChart $sizeChart, $itemName)
    {
        $chartData = $sizeChart->chart_data;
        $categoryTypes = ['clothing', 'shoes', 'accessories'];
        
        // Make sure we have the needed category types
        foreach ($categoryTypes as $type) {
            if (!isset($chartData[$type])) {
                $chartData[$type] = [];
            }
        }
        
        // Ensure measurement instructions exist
        if (!isset($chartData['measurement_instructions'])) {
            $chartData['measurement_instructions'] = [
                'clothing' => [
                    [
                        'title' => 'Chest',
                        'description' => 'Measure around the fullest part of your chest, keeping the tape measure horizontal.'
                    ],
                    [
                        'title' => 'Waist',
                        'description' => 'Measure around your natural waistline, keeping the tape measure horizontal.'
                    ],
                    [
                        'title' => 'Hips',
                        'description' => 'Measure around the fullest part of your hips, keeping the tape measure horizontal.'
                    ],
                    [
                        'title' => 'Sleeve',
                        'description' => 'Measure from the center back of your neck, across your shoulder and down to your wrist.'
                    ]
                ],
                'shoes' => [
                    [
                        'title' => 'Foot Length',
                        'description' => 'Place your foot on a flat surface with your heel against a straight edge. Measure the distance from the wall to the longest toe.'
                    ]
                ],
                'accessories' => [
                    [
                        'title' => 'Neck',
                        'description' => 'Measure around the base of your neck, where a collar would sit.'
                    ],
                    [
                        'title' => 'Wrist',
                        'description' => 'Measure around your wrist bone.'
                    ],
                    [
                        'title' => 'Head',
                        'description' => 'Measure around your head, just above the ears.'
                    ]
                ]
            ];
        }

        // Example size data to ensure compatibility with the frontend
        if (empty($chartData['clothing'])) {
            $chartData['clothing'] = $this->getDefaultClothingSizes();
        }

        if (empty($chartData['shoes'])) {
            $chartData['shoes'] = $this->getDefaultShoeSizes();
        }

        if (empty($chartData['accessories'])) {
            $chartData['accessories'] = $this->getDefaultAccessoriesSizes();
        }

        // Update the chart data
        $sizeChart->chart_data = $chartData;
        
        return $sizeChart;
    }

    /**
     * Get default clothing sizes
     *
     * @return array
     */
    private function getDefaultClothingSizes()
    {
        return [
            'XS' => [
                'chest_in' => '32-34',
                'chest_cm' => '81-86',
                'waist_in' => '26-28',
                'waist_cm' => '66-71',
                'hips_in' => '34-36',
                'hips_cm' => '86-91',
                'sleeve_in' => '31',
                'sleeve_cm' => '79'
            ],
            'S' => [
                'chest_in' => '35-37',
                'chest_cm' => '89-94',
                'waist_in' => '29-31',
                'waist_cm' => '74-79',
                'hips_in' => '37-39',
                'hips_cm' => '94-99',
                'sleeve_in' => '32',
                'sleeve_cm' => '81'
            ],
            'M' => [
                'chest_in' => '38-40',
                'chest_cm' => '97-102',
                'waist_in' => '32-34',
                'waist_cm' => '81-86',
                'hips_in' => '40-42',
                'hips_cm' => '102-107',
                'sleeve_in' => '33',
                'sleeve_cm' => '84'
            ],
            'L' => [
                'chest_in' => '41-43',
                'chest_cm' => '104-109',
                'waist_in' => '35-37',
                'waist_cm' => '89-94',
                'hips_in' => '43-45',
                'hips_cm' => '109-114',
                'sleeve_in' => '34',
                'sleeve_cm' => '86'
            ],
            'XL' => [
                'chest_in' => '44-46',
                'chest_cm' => '112-117',
                'waist_in' => '38-40',
                'waist_cm' => '97-102',
                'hips_in' => '46-48',
                'hips_cm' => '117-122',
                'sleeve_in' => '35',
                'sleeve_cm' => '89'
            ],
            '2XL' => [
                'chest_in' => '47-49',
                'chest_cm' => '119-124',
                'waist_in' => '41-43',
                'waist_cm' => '104-109',
                'hips_in' => '49-51',
                'hips_cm' => '124-130',
                'sleeve_in' => '36',
                'sleeve_cm' => '91'
            ]
        ];
    }

    /**
     * Get default shoe sizes
     *
     * @return array
     */
    private function getDefaultShoeSizes()
    {
        return [
            '6' => [
                'uk' => '5',
                'eu' => '38-39',
                'foot_in' => '9.25',
                'foot_cm' => '23.5'
            ],
            '7' => [
                'uk' => '6',
                'eu' => '39-40',
                'foot_in' => '9.625',
                'foot_cm' => '24.4'
            ],
            '8' => [
                'uk' => '7',
                'eu' => '41',
                'foot_in' => '10',
                'foot_cm' => '25.4'
            ],
            '9' => [
                'uk' => '8',
                'eu' => '42-43',
                'foot_in' => '10.375',
                'foot_cm' => '26.4'
            ],
            '10' => [
                'uk' => '9',
                'eu' => '44',
                'foot_in' => '10.75',
                'foot_cm' => '27.3'
            ],
            '11' => [
                'uk' => '10',
                'eu' => '45',
                'foot_in' => '11.125',
                'foot_cm' => '28.3'
            ],
            '12' => [
                'uk' => '11',
                'eu' => '46',
                'foot_in' => '11.5',
                'foot_cm' => '29.2'
            ],
        ];
    }

    /**
     * Get default accessories sizes
     *
     * @return array
     */
    private function getDefaultAccessoriesSizes()
    {
        return [
            'XS' => [
                'neck_in' => '13-13.5',
                'neck_cm' => '33-34',
                'wrist_in' => '5.5-6',
                'wrist_cm' => '14-15',
                'head_in' => '20-20.5',
                'head_cm' => '51-52'
            ],
            'S' => [
                'neck_in' => '14-14.5',
                'neck_cm' => '35-37',
                'wrist_in' => '6-6.5',
                'wrist_cm' => '15-16.5',
                'head_in' => '21-21.5',
                'head_cm' => '53-55'
            ],
            'M' => [
                'neck_in' => '15-15.5',
                'neck_cm' => '38-39',
                'wrist_in' => '6.5-7',
                'wrist_cm' => '16.5-18',
                'head_in' => '22-22.5',
                'head_cm' => '56-57'
            ],
            'L' => [
                'neck_in' => '16-16.5',
                'neck_cm' => '40-42',
                'wrist_in' => '7-7.5',
                'wrist_cm' => '18-19',
                'head_in' => '23-23.5',
                'head_cm' => '58-60'
            ],
            'XL' => [
                'neck_in' => '17-17.5',
                'neck_cm' => '43-44',
                'wrist_in' => '7.5-8',
                'wrist_cm' => '19-20',
                'head_in' => '24-24.5',
                'head_cm' => '61-62'
            ]
        ];
    }

    /**
     * Store a new size chart
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function storeSizeChart(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'chart_data' => 'required|array',
            'category_ids' => 'required|array',
            'category_ids.*' => 'exists:categories,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        DB::beginTransaction();
        
        try {
            $sizeChart = SizeChart::create([
                'name' => $request->name,
                'chart_data' => $request->chart_data,
                'is_active' => $request->input('is_active', true)
            ]);

            // Attach categories
            $sizeChart->categories()->attach($request->category_ids);
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => 'Size chart created successfully',
                'data' => $sizeChart
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to create size chart',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update a size chart
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateSizeChart(Request $request, $id)
    {
        $sizeChart = SizeChart::find($id);
        
        if (!$sizeChart) {
            return response()->json([
                'success' => false,
                'message' => 'Size chart not found'
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'string|max:255',
            'chart_data' => 'array',
            'category_ids' => 'array',
            'category_ids.*' => 'exists:categories,id',
            'is_active' => 'boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        DB::beginTransaction();
        
        try {
            if ($request->has('name')) {
                $sizeChart->name = $request->name;
            }
            
            if ($request->has('chart_data')) {
                $sizeChart->chart_data = $request->chart_data;
            }
            
            if ($request->has('is_active')) {
                $sizeChart->is_active = $request->is_active;
            }
            
            $sizeChart->save();

            // Update categories if provided
            if ($request->has('category_ids')) {
                $sizeChart->categories()->sync($request->category_ids);
            }
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => 'Size chart updated successfully',
                'data' => $sizeChart
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to update size chart',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a size chart
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteSizeChart($id)
    {
        $sizeChart = SizeChart::find($id);
        
        if (!$sizeChart) {
            return response()->json([
                'success' => false,
                'message' => 'Size chart not found'
            ], 404);
        }

        try {
            // This will automatically detach all relationships due to cascade
            $sizeChart->delete();
            
            return response()->json([
                'success' => true,
                'message' => 'Size chart deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete size chart',
                'error' => $e->getMessage()
            ], 500);
        }
    }
} 