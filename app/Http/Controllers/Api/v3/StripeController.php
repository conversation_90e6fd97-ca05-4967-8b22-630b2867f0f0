<?php

namespace App\Http\Controllers\Api\v3;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Stripe\PaymentIntent;
use Stripe\Stripe;

class StripeController extends Controller
{
    public function paymentSuccess(Request $request)
    {
        try {
            $paymentIntentId = $request->query('payment_intent');
            $redirectStatus = $request->query('redirect_status');

            if (!$paymentIntentId) {
                // For API calls, return JSON response
                if ($request->expectsJson() || $request->is('api/*')) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Payment intent ID is required',
                        'data' => null
                    ], 400);
                }
                
                // For web redirects, redirect to frontend with error
                $frontendUrl = config('app.frontend_url', 'http://localhost:3000');
                return redirect()->to($frontendUrl . '/checkout/payment/stripe-error?error=missing_payment_intent');
            }

            // Initialize Stripe with the secret key
            Stripe::setApiKey(config('services.stripe.secret'));

            // Retrieve the PaymentIntent
            try {
                $paymentIntent = PaymentIntent::retrieve($paymentIntentId);
            } catch (\Exception $e) {
                Log::error('Stripe PaymentIntent retrieval failed', [
                    'payment_intent_id' => $paymentIntentId,
                    'error' => $e->getMessage()
                ]);

                // For API calls, return JSON error
                if ($request->expectsJson() || $request->is('api/*')) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Failed to retrieve payment information',
                        'data' => null
                    ], 500);
                }

                // For web redirects, redirect to frontend with error
                $frontendUrl = config('app.frontend_url', 'http://localhost:3000');
                return redirect()->to($frontendUrl . '/checkout/payment/stripe-error?error=payment_not_found');
            }

            // Log successful payment retrieval
            Log::info('Stripe payment success processed', [
                'payment_intent_id' => $paymentIntentId,
                'status' => $paymentIntent->status,
                'redirect_status' => $redirectStatus
            ]);

            // For API calls, return JSON response
            if ($request->expectsJson() || $request->is('api/*')) {
                return response()->json([
                    'success' => true,
                    'message' => 'Payment processed successfully',
                    'data' => [
                        'payment_intent_id' => $paymentIntentId,
                        'status' => $paymentIntent->status,
                        'redirect_status' => $redirectStatus,
                        'amount' => $paymentIntent->amount,
                        'currency' => $paymentIntent->currency,
                        'payment_method' => $paymentIntent->payment_method
                    ]
                ], 200);
            }

            // For web redirects, this should not happen anymore as we use frontend URLs
            // But keeping as fallback - redirect to frontend success page
            $frontendUrl = config('app.frontend_url', 'http://localhost:3000');
            return redirect()->to($frontendUrl . '/checkout/payment/stripe-success?' . http_build_query([
                'payment_intent' => $paymentIntentId,
                'redirect_status' => $redirectStatus
            ]));

        } catch (\Exception $e) {
            Log::error('Stripe payment success handling failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // For API calls, return JSON error
            if ($request->expectsJson() || $request->is('api/*')) {
                return response()->json([
                    'success' => false,
                    'message' => 'An error occurred while processing payment confirmation',
                    'data' => null
                ], 500);
            }

            // For web redirects, redirect to frontend with error
            $frontendUrl = config('app.frontend_url', 'http://localhost:3000');
            return redirect()->to($frontendUrl . '/checkout/payment/stripe-error?error=processing_failed');
        }
    }

    public function paymentCancel(Request $request)
    {
        try {
            $paymentIntentId = $request->query('payment_intent');
            
            Log::info('Stripe payment cancelled', [
                'payment_intent_id' => $paymentIntentId,
                'user_agent' => $request->userAgent()
            ]);

            // For API calls, return JSON response
            if ($request->expectsJson() || $request->is('api/*')) {
                return response()->json([
                    'success' => true,
                    'message' => 'Payment was cancelled by user',
                    'data' => [
                        'payment_intent_id' => $paymentIntentId,
                        'status' => 'cancelled'
                    ]
                ], 200);
            }

            // For web redirects, this should not happen anymore as we use frontend URLs
            // But keeping as fallback - redirect to frontend cancel page
            $frontendUrl = config('app.frontend_url', 'http://localhost:3000');
            return redirect()->to($frontendUrl . '/checkout/payment/stripe-cancel?' . http_build_query([
                'payment_intent' => $paymentIntentId
            ]));

        } catch (\Exception $e) {
            Log::error('Stripe payment cancel handling failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // For API calls, return JSON error
            if ($request->expectsJson() || $request->is('api/*')) {
                return response()->json([
                    'success' => false,
                    'message' => 'An error occurred while processing payment cancellation',
                    'data' => null
                ], 500);
            }

            // For web redirects, redirect to frontend with error
            $frontendUrl = config('app.frontend_url', 'http://localhost:3000');
            return redirect()->to($frontendUrl . '/checkout/payment/stripe-error?error=cancel_failed');
        }
    }
} 