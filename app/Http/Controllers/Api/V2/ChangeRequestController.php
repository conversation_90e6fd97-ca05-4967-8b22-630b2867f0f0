<?php

namespace App\Http\Controllers\Api\V2;

use App\Models\ClubPoint;
use App\Http\Resources\V2\RefundRequestCollection;
use App\Models\ChangeRequest;
use App\Models\OrderDetail;
use App\Models\RefundRequest;
use App\Models\ReturnRequest;
use App\Models\User;
use App\Models\Wallet;
use Illuminate\Http\Request;

class ChangeRequestController extends Controller
{
    public function send(Request $request)
    {
        $order_detail = OrderDetail::where('id', $request->id)->first();
        $changeRequest = new ChangeRequest;
        $changeRequest->user_id = auth()->user()->id;
        $changeRequest->order_id = $order_detail->order_id;
        $changeRequest->order_detail_id = $order_detail->id;
        $changeRequest->seller_id = $order_detail->seller_id;
        $changeRequest->seller_approval = 0;
        $changeRequest->reason = $request->reason;
        $changeRequest->admin_approval = 0;
        $changeRequest->admin_seen = 0;
        $changeRequest->change_amount = $order_detail->price + $order_detail->tax;
        $changeRequest->change_status = 0;
        OrderDetail::where('id', $request->id)->update(['change_status' => 3]);
        $changeRequest->save();
        return response()->json([
            'success' => true,
            'message' => translate('Request Sent')
        ]);
    }
}
