<?php

namespace App\Http\Controllers\Api\V2;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

class PaymentController extends Controller
{
    public function cashOnDelivery(Request $request)
    {
        // $order = new OrderController;
        $order = App::make(OrderController::class);
        return $order->store($request);
    }

    public function manualPayment(Request $request)
    {
        // $order = new OrderController;
        $order = App::make(OrderController::class);
        return $order->store($request);
    }
}
