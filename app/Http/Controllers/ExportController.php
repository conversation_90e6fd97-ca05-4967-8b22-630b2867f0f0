<?php

namespace App\Http\Controllers;

use App\Exports\CustomersExport;
use App\Exports\OrdersExport;
use App\Models\Customer;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Http\Request;
use Mpdf\Cache;

//use Illuminate\Support\Facades\Mail; //added by suman

class ExportController extends Controller
{
    public function exportOrder()
    {
        return Excel::download(new OrdersExport, 'orders.xlsx');
    }
    public function exportCustomer(Request $request)
    {
        // dd($request->all());
        $start_date = $request->input('start_date');
        $end_date = $request->input('end_date');
        $search = $request->input('search');

        return Excel::download(new CustomersExport($start_date, $end_date, $search), 'customers.xlsx');
    }
}
