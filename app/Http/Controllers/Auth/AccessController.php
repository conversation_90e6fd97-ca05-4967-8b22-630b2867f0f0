<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class AccessController extends Controller
{
    public function getAccess()
    {
        $permissions = [
            'id'    => 'view_all_messages',
            'name'  => translate('View All Messages'),
            'parent'=> 'messaging_permission',
            'section'=> 'admin',
            'order'=> 28
        ];

        return response()->json(['permissions' => $permissions]);
    }
} 