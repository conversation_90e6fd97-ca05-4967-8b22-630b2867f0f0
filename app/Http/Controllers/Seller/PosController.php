<?php

namespace App\Http\Controllers\Seller;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use App\Models\Product;
use App\Models\ProductStock;
use App\Models\Category;
use App\Models\Brand;
use App\Models\User;
use App\Models\Address;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\CombinedOrder;
use App\Models\BusinessSetting;
use App\Enums\OrderStatus;
use Carbon\Carbon;

class PosController extends Controller
{
    /**
     * Seller POS Interface
     */
    public function index()
    {
        try {
            $seller = Auth::user();
            
            // Check if seller has POS access
            if (!$seller->seller) {
                flash(translate('Access denied. Only sellers can access POS.'))->error();
                return redirect()->route('seller.dashboard');
            }

            $categories = Category::where('level', 0)->get();
            $brands = Brand::all();
            
            // Get seller's products for POS
            $seller_products = Product::where('user_id', $seller->id)
                ->where('published', 1)
                ->where('approved', 1)
                ->where('auction_product', 0)
                ->with(['thumbnail', 'product_translations', 'stocks', 'taxes'])
                ->limit(20)
                ->get();
            
            return view('seller.pos.index', compact('categories', 'brands', 'seller_products'));
        } catch (\Exception $e) {
            Log::error('Seller POS index failed', [
                'error' => $e->getMessage(),
                'seller_id' => Auth::id()
            ]);
            
            flash(translate('Error loading POS interface'))->error();
            return redirect()->route('seller.dashboard');
        }
    }

    /**
     * POS Configuration for seller
     */
    public function configuration()
    {
        try {
            $seller = Auth::user();
            
            if (!$seller->seller) {
                flash(translate('Access denied. Only sellers can access POS configuration.'))->error();
                return redirect()->route('seller.dashboard');
            }

            return view('seller.pos.configuration');
        } catch (\Exception $e) {
            Log::error('Seller POS configuration failed', [
                'error' => $e->getMessage(),
                'seller_id' => Auth::id()
            ]);
            
            flash(translate('Error loading POS configuration'))->error();
            return redirect()->back();
        }
    }

    /**
     * Search seller's products for POS
     */
    public function search(Request $request)
    {
        try {
            $seller = Auth::user();
            
            if (!$seller->seller) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied'
                ], 403);
            }

            $keyword = $request->keyword;
            $category = $request->category;
            $brand = $request->brand;

            $products = Product::where('user_id', $seller->id)
                ->where('published', 1)
                ->where('approved', 1)
                ->where('auction_product', 0);

            if (!empty($keyword)) {
                $products = $products->where(function($q) use ($keyword) {
                    $q->where('name', 'like', '%' . $keyword . '%')
                      ->orWhere('slug', 'like', '%' . $keyword . '%')
                      ->orWhere('barcode', 'like', '%' . $keyword . '%')
                      ->orWhereHas('product_translations', function($query) use ($keyword) {
                          $query->where('name', 'like', '%' . $keyword . '%');
                      });
                });
            }

            if (!empty($category) && $category != 'all') {
                if (strpos($category, 'category-') === 0) {
                    $category_id = str_replace('category-', '', $category);
                    $products = $products->where('category_id', $category_id);
                }
            }

            if (!empty($brand) && $brand != 'all') {
                $products = $products->where('brand_id', $brand);
            }

            $products = $products->with(['thumbnail', 'product_translations', 'stocks', 'taxes'])
                ->limit(20)
                ->get();

            $html = '';
            foreach ($products as $product) {
                $stock_qty = $product->stocks->sum('qty');
                $price = home_base_price($product);
                
                $html .= '<div class="col-sm-6 col-md-4 col-xl-3 mb-3">
                    <div class="card card-product" onclick="addToCart(' . $product->id . ')">
                        <div class="card-body p-3 text-center">
                            <img src="' . uploaded_asset($product->thumbnail_img) . '" 
                                 class="img-fluid mb-2" style="height: 100px; object-fit: cover;">
                            <h6 class="fw-600 text-truncate">' . $product->getTranslation('name') . '</h6>
                            <p class="opacity-60 text-truncate">' . format_price($price) . '</p>
                            <p class="opacity-50 fs-12">Stock: ' . $stock_qty . '</p>
                        </div>
                    </div>
                </div>';
            }

            return $html;

        } catch (\Exception $e) {
            Log::error('Seller POS product search failed', [
                'error' => $e->getMessage(),
                'seller_id' => Auth::id(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Product search failed'
            ], 500);
        }
    }

    /**
     * Generate thermal printer invoice for seller
     */
    public function invoice($order_id)
    {
        try {
            $seller = Auth::user();
            
            if (!$seller->seller) {
                flash(translate('Access denied'))->error();
                return redirect()->back();
            }

            $order = Order::where('id', $order_id)
                ->where('seller_id', $seller->id)
                ->firstOrFail();
                
            $order_details = OrderDetail::where('order_id', $order->id)
                ->with(['product'])
                ->get();

            // Get seller shop information
            $shop = $seller->shop;

            return view('seller.pos.thermal_invoice', compact('order', 'order_details', 'shop', 'seller'));

        } catch (\Exception $e) {
            Log::error('Seller POS thermal invoice failed', [
                'error' => $e->getMessage(),
                'seller_id' => Auth::id(),
                'order_id' => $order_id
            ]);

            flash(translate('Error generating invoice'))->error();
            return redirect()->back();
        }
    }

    /**
     * Add product to seller POS cart
     */
    public function addToCart(Request $request)
    {
        try {
            $seller = Auth::user();
            
            if (!$seller->seller) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied'
                ], 403);
            }

            $validator = Validator::make($request->all(), [
                'product_id' => 'required|integer|exists:products,id',
                'quantity' => 'integer|min:1',
                'variant' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Verify product belongs to seller
            $product = Product::where('id', $request->product_id)
                ->where('user_id', $seller->id)
                ->firstOrFail();

            $quantity = $request->quantity ?? 1;
            $variant = $request->variant ?? '';

            // Check stock
            $stock = $this->getProductStock($product, $variant);
            if ($stock < $quantity) {
                return response()->json([
                    'success' => false,
                    'message' => translate('Insufficient stock')
                ], 400);
            }

            // Get or create seller POS cart session
            $cart_key = 'seller_pos_cart_' . $seller->id;
            $cart = Session::get($cart_key, []);
            $item_key = $product->id . '_' . $variant;

            if (isset($cart[$item_key])) {
                $cart[$item_key]['quantity'] += $quantity;
            } else {
                $cart[$item_key] = [
                    'product_id' => $product->id,
                    'quantity' => $quantity,
                    'variant' => $variant,
                    'price' => $this->getProductPrice($product, $variant),
                    'name' => $product->getTranslation('name'),
                    'image' => uploaded_asset($product->thumbnail_img)
                ];
            }

            Session::put($cart_key, $cart);

            Log::info('Seller POS item added to cart', [
                'seller_id' => $seller->id,
                'product_id' => $product->id,
                'quantity' => $quantity,
                'variant' => $variant
            ]);

            return response()->json([
                'success' => true,
                'message' => translate('Item added to cart'),
                'cart' => $cart,
                'cart_count' => count($cart)
            ]);

        } catch (\Exception $e) {
            Log::error('Seller POS add to cart failed', [
                'error' => $e->getMessage(),
                'seller_id' => Auth::id(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to add item to cart'
            ], 500);
        }
    }

    /**
     * Process seller POS order
     */
    public function processOrder(Request $request)
    {
        try {
            $seller = Auth::user();
            
            if (!$seller->seller) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied'
                ], 403);
            }

            $validator = Validator::make($request->all(), [
                'payment_method' => 'required|string|in:cash,card,digital_wallet',
                'customer_phone' => 'nullable|string',
                'customer_name' => 'nullable|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $cart_key = 'seller_pos_cart_' . $seller->id;
            $cart = Session::get($cart_key, []);
            
            if (empty($cart)) {
                return response()->json([
                    'success' => false,
                    'message' => translate('Cart is empty')
                ], 400);
            }

            DB::beginTransaction();

            // Create combined order
            $combined_order = new CombinedOrder();
            $combined_order->user_id = $seller->id; // Seller as user for POS orders
            $combined_order->shipping_address = json_encode([
                'name' => $request->customer_name ?? 'Walk-in Customer',
                'phone' => $request->customer_phone ?? '',
                'address' => 'POS Sale',
                'city' => $seller->city ?? '',
                'country' => $seller->country ?? ''
            ]);
            $combined_order->save();

            // Create order
            $order = new Order();
            $order->combined_order_id = $combined_order->id;
            $order->user_id = $seller->id;
            $order->seller_id = $seller->id;
            $order->code = 'SELLER-POS-' . date('Ymd') . '-' . rand(1000, 9999);
            $order->date = strtotime('now');
            $order->payment_type = $request->payment_method;
            $order->payment_status = 'paid';
            $order->delivery_status = 'delivered';
            $order->shipping_address = json_encode([
                'name' => $request->customer_name ?? 'Walk-in Customer',
                'phone' => $request->customer_phone ?? '',
                'address' => 'POS Sale',
                'city' => $seller->city ?? '',
                'country' => $seller->country ?? ''
            ]);
            $order->order_from = 'seller_pos';
            
            // Calculate totals
            $subtotal = 0;
            $total_tax = 0;

            foreach ($cart as $item) {
                $item_total = $item['price'] * $item['quantity'];
                $subtotal += $item_total;

                $product = Product::find($item['product_id']);
                if ($product && $product->taxes) {
                    foreach ($product->taxes as $tax) {
                        if ($tax->tax_type == 'percent') {
                            $total_tax += ($item_total * $tax->tax) / 100;
                        } else {
                            $total_tax += $tax->tax * $item['quantity'];
                        }
                    }
                }
            }

            $order->grand_total = $subtotal + $total_tax;
            $order->save();

            // Create order details and update stock
            foreach ($cart as $item) {
                $product = Product::find($item['product_id']);
                
                $order_detail = new OrderDetail();
                $order_detail->order_id = $order->id;
                $order_detail->product_id = $product->id;
                $order_detail->variation = $item['variant'] ?? '';
                $order_detail->price = $item['price'];
                $order_detail->quantity = $item['quantity'];
                $order_detail->save();

                // Update product stock
                if ($product->variant_product == 1 && !empty($item['variant'])) {
                    $product_stock = ProductStock::where('product_id', $product->id)
                        ->where('variant', $item['variant'])
                        ->first();
                    if ($product_stock) {
                        $product_stock->qty -= $item['quantity'];
                        $product_stock->save();
                    }
                } else {
                    $product->current_stock -= $item['quantity'];
                    $product->save();
                }

                // Update sales count
                $product->num_of_sale += $item['quantity'];
                $product->save();
            }

            // Clear seller POS cart
            Session::forget($cart_key);

            DB::commit();

            Log::info('Seller POS order created successfully', [
                'seller_id' => $seller->id,
                'order_id' => $order->id,
                'order_code' => $order->code,
                'total' => $order->grand_total
            ]);

            return response()->json([
                'success' => true,
                'message' => translate('Order placed successfully'),
                'order' => [
                    'id' => $order->id,
                    'code' => $order->code,
                    'total' => $order->grand_total
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Seller POS order creation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'seller_id' => Auth::id(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to place order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get seller's recent POS orders
     */
    public function getRecentOrders()
    {
        try {
            $seller = Auth::user();
            
            if (!$seller->seller) {
                return response()->json([
                    'success' => false,
                    'message' => 'Access denied'
                ], 403);
            }

            $orders = Order::where('seller_id', $seller->id)
                ->where('order_from', 'seller_pos')
                ->with(['orderDetails.product'])
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            return response()->json([
                'success' => true,
                'orders' => $orders
            ]);

        } catch (\Exception $e) {
            Log::error('Get seller recent POS orders failed', [
                'error' => $e->getMessage(),
                'seller_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get orders'
            ], 500);
        }
    }

    /**
     * Helper: Get product stock
     */
    protected function getProductStock($product, $variant = '')
    {
        if ($product->variant_product == 1 && !empty($variant)) {
            $stock = ProductStock::where('product_id', $product->id)
                ->where('variant', $variant)
                ->first();
            return $stock ? $stock->qty : 0;
        }
        
        return $product->current_stock ?? 0;
    }

    /**
     * Helper: Get product price
     */
    protected function getProductPrice($product, $variant = '')
    {
        if ($product->variant_product == 1 && !empty($variant)) {
            $stock = ProductStock::where('product_id', $product->id)
                ->where('variant', $variant)
                ->first();
            return $stock ? $stock->price : $product->unit_price;
        }
        
        return home_base_price($product) ?? $product->unit_price;
    }
} 