<?php

namespace App\Http\Controllers;

use App\Services\StripeService;
use App\Http\Controllers\Controller;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\CustomerPackageController;
use App\Http\Controllers\SellerPackageController;
use App\Http\Controllers\WalletController;
use Illuminate\Http\Request;
use App\Models\CombinedOrder;
use App\Models\CustomerPackage;
use App\Models\SellerPackage;
use App\Models\User;
use App\Models\Cart;
use Session;
use Auth;

class StripePaymentController extends Controller
{
    protected $stripeService;

    public function __construct(StripeService $stripeService)
    {
        $this->stripeService = $stripeService;
    }

    // public function storeCard(Request $request)
    // {
    //     try {
    //         $user = Auth::user();

    //         $stripeCustomer = $user->stripeCustomer;

    //         if (!$stripeCustomer) {
    //             $customer = $this->stripeService->createCustomer($user);
    //             // dd($customer);
    //             $user->stripeCustomer()->create([
    //                 'stripe_customer_id' => $customer->id,
    //             ]);
    //             $stripeCustomer = $user->stripeCustomer;
    //         }
    //         // Retrieve the new card details from Stripe
    //         $paymentMethod = $this->stripeService->retrieveCard($request->payment_method);

    //         // Check if this card already exists
    //         $existingCard = $user->stripeCards()->where('last4', $paymentMethod->card->last4)
    //             ->where('exp_month', $paymentMethod->card->exp_month)
    //             ->where('exp_year', $paymentMethod->card->exp_year)
    //             ->first();

    //         if ($existingCard) {
    //             return response()->json(['success' => false, 'message' => 'Card already exists'], 400);
    //         }

    //         $paymentMethod = $this->stripeService->addCard($stripeCustomer->stripe_customer_id, $request->payment_method);

    //         $user->stripeCards()->create([
    //             'stripe_card_id' => $paymentMethod->id,
    //             'brand' => $paymentMethod->card->brand,
    //             'last4' => $paymentMethod->card->last4,
    //             'exp_month' => $paymentMethod->card->exp_month,
    //             'exp_year' => $paymentMethod->card->exp_year,
    //         ]);

    //         return response()->json(['success' => true, 'message' => 'Card added successfully']);
    //     } catch (\Exception $e) {
    //         return response()->json(['success' => false, 'message' => $e->getMessage()]);
    //     }
    // }
    public function storeCard(Request $request)
    {
        try {
            $user = Auth::user();

            // Check if the user already has a Stripe customer record
            $stripeCustomer = $user->stripeCustomer;

            // If not, create a new Stripe customer
            if (!$stripeCustomer) {
                $customer = $this->stripeService->createCustomer($user);

                // Store the new Stripe customer ID in the database
                $stripeCustomer = $user->stripeCustomer()->create([
                    'stripe_customer_id' => $customer->id,
                ]);
            }

            // Retrieve the new card details from Stripe
            $paymentMethod = $this->stripeService->retrieveCard($request->payment_method);

            // Check if this card already exists for the user
            $existingCard = $user->stripeCards()
                ->where('last4', $paymentMethod->card->last4)
                ->where('exp_month', $paymentMethod->card->exp_month)
                ->where('exp_year', $paymentMethod->card->exp_year)
                ->first();

            if ($existingCard) {
                return response()->json(['success' => false, 'message' => 'Card already exists'], 400);
            }

            // Add the new card to Stripe and associate it with the user
            $paymentMethod = $this->stripeService->addCard($stripeCustomer->stripe_customer_id, $request->payment_method);

            // Save the card details in the database
            $user->stripeCards()->create([
                'stripe_card_id' => $paymentMethod->id,
                'brand' => $paymentMethod->card->brand,
                'last4' => $paymentMethod->card->last4,
                'exp_month' => $paymentMethod->card->exp_month,
                'exp_year' => $paymentMethod->card->exp_year,
            ]);

            return response()->json(['success' => true, 'message' => 'Card added successfully']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function getCards()
    {
        $user = Auth::user();
        $stripeCustomer = $user->stripeCustomer;

        if ($stripeCustomer) {
            $paymentMethods = $this->stripeService->getCustomerPaymentMethods($stripeCustomer->stripe_customer_id);
            $cards = $paymentMethods->data; // Ensure you get the 'data' array from the response
            if(empty($cards)){
                $paymentMethods = $this->stripeService->getCustomerPaymentMethods($stripeCustomer->stripe_customer_id);
                $cards = $paymentMethods->data; // Ensure you get the 'data' array from the response
            }
            return response()->json($cards);
        }

        return response()->json([]);
    }

    public function removeCard($cardId)
    {
        $user = Auth::user();
        $card = $user->stripeCards()->where('stripe_card_id', $cardId)->first();

        if ($card) {
            $this->stripeService->removeCard($cardId);
            $card->delete();

            return response()->json(['status' => true, 'message' => 'Card removed successfully']);
        }

        return response()->json(['status' => false, 'message' => 'Card not found'], 404);
    }

    public function payWithCard(Request $request)
    {
        $request->validate([
            'payment_method' => 'required|string',
            'amount' => 'required|numeric|min:0.01',
        ]);

        try {
            $user = Auth::user();
            $stripeCustomer = $user->stripeCustomer;

            if (!$stripeCustomer) {
                return response()->json(['success' => false, 'message' => 'Stripe customer not found.']);
            }
            $successUrl = route('stripe2.success');
            $cancelUrl = route('stripe2.cancel');
            $paymentIntent = $this->stripeService->payWithCard(
                $stripeCustomer->stripe_customer_id,
                $request->payment_method,
                $request->amount,
                $successUrl,
                $cancelUrl
            );

            if ($paymentIntent->status === 'succeeded') {
                // Extract the charge ID
                $chargeId = $paymentIntent->latest_charge ?? null;

                if ($chargeId) {
                    // Store the charge ID in the session
                    session(['charge_id' => $chargeId]);
                    session(['payment_type' => 'cart_payment']);
                }
                return response()->json([
                    'success' => true,
                    'message' => 'Payment successful!',
                    'charge_id' => $chargeId,
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Payment not successful. Please try again.',
                ]);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function success(Request $request)
    {

        try {
            $chargeId = Session::get('payment_charge_id') ?? Session::get('charge_id');
            $payment = ["status" => "Success"];
            $payment_type = Session::get('payment_type') ?? 'cart_payment';

            if ($payment_type == 'cart_payment') {
                return (new CheckoutController)->checkout_done(session()->get('combined_order_id'), json_encode($payment), $chargeId);
            } else if ($payment_type == 'wallet_payment') {
                return (new WalletController)->wallet_payment_done(session()->get('payment_data'), json_encode($payment));
            } else if ($payment_type == 'customer_package_payment') {
                return (new CustomerPackageController)->purchase_payment_done(session()->get('payment_data'), json_encode($payment));
            } else if ($payment_type == 'seller_package_payment') {
                return (new SellerPackageController)->purchase_payment_done(session()->get('payment_data'), json_encode($payment));
            }
        } catch (\Exception $e) {
            flash(translate('Payment failed'))->error();
            return redirect()->route('home');
        }
    }

    public function cancel(Request $request)
    {
        flash(translate('Payment is cancelled'))->error();
        return redirect()->route('home');
    }
}
