<?php

namespace App\Http\Controllers\dropshipper;

use App\Http\Requests\dropshipperRegistrationRequest;
use App\Models\User;
use App\Models\Customer;
use App\Models\B2BProfile;// dropshipper shop
use Auth;
use Hash;
use Carbon\Carbon;
use Illuminate\Http\Request;

class CustomerController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function view()
    {
        $data['user'] = Auth::user();
        //dd($data['user']);
        $data['shopModel'] =  Customer::where('create_by',$data['user']->id)->first();
        return view('dropshipper.customer.view', $data);
    }
    public function list(Request $request)
    {
        $data['user'] = Auth::user();
        //dd($data['user']);
        $sort_search = null;
        $approved = null;
        $customers = new Customer;
        $customers =  $customers->where('create_by',$data['user']->id);
        //$customers = Customer::where('create_by', $data['user']->id)->latest();
        $itemNo = 5;
        if($request->has('page') && $request->page > 1) {
            $data['serial'] = (($request->page * $itemNo)-$itemNo)+1;
        }else{
            $data['serial'] = 1;
        }
        $data['customers'] = $customers->paginate($itemNo);
        //dd($data['customers']);
        //dd('dd');
        return view('dropshipper.customer.customer_list', $data);
    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function add()
    {
        //dd('dd');
        return view('dropshipper.customer.dropshipper_form');

    }
        /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(dropshipperRegistrationRequest $request)
    {
        //dd($request->all());
        $user = new User;
        $user->user_name = $request->user_name;
        $user->name = $request->name; // first name
        $user->last_name = $request->last_name;
        $user->country_code = $request->country_code;
        $user->phone = $request->phone;
        $user->email = $request->email;
        $user->user_type = "customer";
        $user->email_verified_at = Carbon::now();
        $user->city = $request->state_province;
        $user->state= $request->state_name;
        //$user->news_letter= $request->news_letter;
        //$user->terms_conditions= $request->terms_conditions;
        $user->password = Hash::make($request->password);

        if ($user->save()) {
            $customerModel = new Customer;
            $customerModel->user_id = $user->id;
            $customerModel->customer_owner = 'dropshipper';
            //$customerModel->user_name = $request->user_name;
            //$customerModel->user_name = $request->user_name;
            $customerModel->name = $request->name; // first name
            $customerModel->last_name = $request->last_name;
            $customerModel->phone = $request->phone;
            $customerModel->email = $request->email;
            $customerModel->news_letter =  $request->news_letter;
            $createBy = Auth::user();
            $customerModel->create_by =  $createBy->id;
            $customerModel->save();

            flash(translate('Customer added'))->success();
            //return redirect()->route('dropshipper.shop.index');
            return redirect()->route('dropshipper.customer.list');
        }

        flash(translate('Sorry! Something went wrong.'))->error();
        return back();
    }
    public function profileModify()
    {
        $data['user'] = Auth::user();
        //dd($data['user']);
        $data['addresses'] = $data['user']->addresses;
        $data['shopModel'] =  B2BProfile::where('user_id',$data['user']->id)->first();
        return view('dropshipper.profile.profile-modify', $data);
    }
    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function customerView(Request $request)
    {
        // dd($request->id);
        $data["customer"] = Customer::findOrFail($request->id);
        //dd($data["customer"]->user_id);
        $data["user"] = User::findOrFail($data["customer"]->user_id);
        //dd($data["user"]);
        return view('dropshipper.customer.customer-view', $data);
    }
    public function customerUpdate(Request $request)
    {
        // dd($request->id);
        $data["user"] = Customer::findOrFail($request->id);
        //dd($data["user"]);
        return view('dropshipper.customer.customer-update', $data);
    }


    public function customerUpdateFinal(Request $request)
    {
        //dd($request->id);
                $customer = Customer::findOrFail($request->id);
                //dd($request->all());
                $customer->name = $request->name; // first name
                $customer->last_name = $request->last_name;
                //$user->news_letter= $request->news_letter;
                //$user->terms_conditions= $request->terms_conditions;


                if ($customer->save()) {

                    //$customer->password = Hash::make($request->password);
                    $user = User::findOrFail($customer->create_by);

                    $user->name = $request->name; // first name
                    $user->last_name = $request->last_name;

                    $user->save();

                    flash(translate('Customer Updated'))->success();
                    return redirect()->route('dropshipper.customer.list');

                }

                flash(translate('Sorry! Something went wrong.'))->error();
                return back();
    }
    public function dropshipper2PasswordChangeView()
    {
        $data['user'] = Auth::user();
        //dd($data['user']);
        $data['addresses'] = $data['user']->addresses;
        $data['shopModel'] =  B2BProfile::where('user_id',$data['user']->id)->first();
        return view('dropshipper.profile.update-pass', $data);
    }
    public function dropshipperUserPassChangeStore(Request $request)
    {
        //dd('dropshipper');
        //dd($request->all());
        $user = User::findOrFail($request->id);
        //dd($user);
        $this->validate($request, [
            //'current_password' => ['required','current_password'],
            //'password' => 'required|confirmed|string|min:8',
            'current_password' => ['required', function ($attr, $password, $validation) use ($user) {
                if (!\Hash::check($password, $user->password)) {
                    return $validation(__('The current password is incorrect.'));
                    flash(translate('The current password is incorrect.!'))->error();
                    return back();
                }
            }],
        ]);
        if($request->new_password != null && ($request->new_password == $request->confirm_password)){
            $user->password = Hash::make($request->new_password);
            $user->save();
            flash(translate('Your Password has been updated successfully!'))->success();
            return back();
        }else{
            flash(translate('The password does not match.!'))->error();
            return back();
        }


    }
}
