<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\DeliveryBoy;
use App\Models\DeliveryBoyPayment;
use App\Models\DeliveryBoyCollection;
use App\Models\DeliveryHistory;
use App\Models\User;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\BusinessSetting;
use Illuminate\Support\Facades\Hash;
use App\Notifications\EmailVerificationNotification;
use Auth;
use Cache;

class DeliveryBoyController extends Controller
{
    public function __construct()
    {
        // Staff Permission Check
        $this->middleware(['permission:view_all_delivery_boys'])->only('index');
        $this->middleware(['permission:add_delivery_boy'])->only('create', 'store');
        $this->middleware(['permission:edit_delivery_boy'])->only('edit', 'update');
        $this->middleware(['permission:delete_delivery_boy'])->only('destroy');
        $this->middleware(['permission:ban_delivery_boy'])->only('ban');
    }

    /**
     * Display a listing of delivery boys for admin.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $sort_search = null;
        $delivery_boys = User::where('user_type', 'delivery_boy')->latest();
        
        if ($request->has('search')) {
            $sort_search = $request->search;
            $delivery_boys->where(function ($q) use ($sort_search) {
                $q->where('name', 'like', '%' . $sort_search . '%')
                  ->orWhere('email', 'like', '%' . $sort_search . '%')
                  ->orWhere('phone', 'like', '%' . $sort_search . '%');
            });
        }
        
        $delivery_boys = $delivery_boys->paginate(15);
        return view('backend.delivery_boys.index', compact('delivery_boys', 'sort_search'));
    }

    /**
     * Show the form for creating a new delivery boy.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('backend.delivery_boys.create');
    }

    /**
     * Store a newly created delivery boy.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|min:6',
            'phone' => 'nullable|string|max:20',
        ]);

        if (User::where('email', $request->email)->first() != null) {
            flash(translate('Email already exists!'))->error();
            return back();
        }

        $user = new User;
        $user->name = $request->name;
        $user->email = $request->email;
        $user->phone = $request->phone;
        $user->user_type = "delivery_boy";
        $user->password = Hash::make($request->password);

        if ($user->save()) {
            if (get_setting('email_verification') != 1) {
                $user->email_verified_at = date('Y-m-d H:m:s');
            } else {
                $user->notify(new EmailVerificationNotification());
            }
            $user->save();

            $delivery_boy = new DeliveryBoy;
            $delivery_boy->user_id = $user->id;
            $delivery_boy->total_collection = 0;
            $delivery_boy->total_earning = 0;

            if ($delivery_boy->save()) {
                flash(translate('Delivery boy has been inserted successfully'))->success();
                return redirect()->route('delivery-boys.index');
            }
        }
        
        flash(translate('Something went wrong'))->error();
        return back();
    }

    /**
     * Display the specified delivery boy.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $delivery_boy = DeliveryBoy::where('user_id', $id)->first();
        if (!$delivery_boy) {
            flash(translate('Delivery boy not found'))->error();
            return redirect()->route('delivery-boys.index');
        }
        
        return view('backend.delivery_boys.show', compact('delivery_boy'));
    }

    /**
     * Show the form for editing the specified delivery boy.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $user = User::where('user_type', 'delivery_boy')->findOrFail($id);
        return view('backend.delivery_boys.edit', compact('user'));
    }

    /**
     * Update the specified delivery boy.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $id,
            'phone' => 'nullable|string|max:20',
        ]);

        $user = User::findOrFail($id);
        $user->name = $request->name;
        $user->email = $request->email;
        $user->phone = $request->phone;
        
        if (strlen($request->password) > 0) {
            $user->password = Hash::make($request->password);
        }
        
        if ($user->save()) {
            flash(translate('Delivery boy has been updated successfully'))->success();
            return redirect()->route('delivery-boys.index');
        }

        flash(translate('Something went wrong'))->error();
        return back();
    }

    /**
     * Remove the specified delivery boy.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $user = User::where('user_type', 'delivery_boy')->findOrFail($id);
        
        // Remove related delivery boy data
        DeliveryBoy::where('user_id', $user->id)->delete();
        DeliveryBoyPayment::where('user_id', $user->id)->delete();
        DeliveryBoyCollection::where('user_id', $user->id)->delete();
        DeliveryHistory::where('delivery_boy_id', $user->id)->delete();
        
        // Update orders assigned to this delivery boy
        Order::where('assign_delivery_boy', $user->id)->update(['assign_delivery_boy' => null]);

        if (User::destroy($user->id)) {
            flash(translate('Delivery boy has been deleted successfully'))->success();
            return redirect()->route('delivery-boys.index');
        } else {
            flash(translate('Something went wrong'))->error();
            return back();
        }
    }

    /**
     * Ban/Unban delivery boy
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function ban($id)
    {
        $user = User::where('user_type', 'delivery_boy')->findOrFail($id);

        if ($user->banned == 1) {
            $user->banned = 0;
            flash(translate('Delivery boy has been unbanned successfully'))->success();
        } else {
            $user->banned = 1;
            flash(translate('Delivery boy has been banned successfully'))->success();
        }

        $user->save();
        return back();
    }

    /**
     * Delivery boy configuration
     */
    public function delivery_boy_configure()
    {
        return view('backend.delivery_boys.configuration');
    }

    /**
     * Order collection form
     */
    public function order_collection_form(Request $request)
    {
        $delivery_boy_id = $request->delivery_boy_id;
        $delivery_boy = User::where('user_type', 'delivery_boy')->findOrFail($delivery_boy_id);
        
        return view('backend.delivery_boys.collection_modal', compact('delivery_boy'));
    }

    /**
     * Process collection from delivery boy
     */
    public function collection_from_delivery_boy(Request $request)
    {
        $delivery_boy = User::where('user_type', 'delivery_boy')->findOrFail($request->delivery_boy_id);
        $delivery_boy_profile = DeliveryBoy::where('user_id', $delivery_boy->id)->first();
        
        if ($delivery_boy_profile) {
            $collection = new DeliveryBoyCollection;
            $collection->user_id = $delivery_boy->id;
            $collection->collection_amount = $request->collection_amount;
            $collection->save();

            $delivery_boy_profile->total_collection -= $request->collection_amount;
            $delivery_boy_profile->save();

            flash(translate('Collection successful'))->success();
        }

        return back();
    }

    /**
     * Delivery earning form
     */
    public function delivery_earning_form(Request $request)
    {
        $delivery_boy_id = $request->delivery_boy_id;
        $delivery_boy = User::where('user_type', 'delivery_boy')->findOrFail($delivery_boy_id);
        
        return view('backend.delivery_boys.payment_modal', compact('delivery_boy'));
    }

    /**
     * Pay to delivery boy
     */
    public function paid_to_delivery_boy(Request $request)
    {
        $delivery_boy = User::where('user_type', 'delivery_boy')->findOrFail($request->delivery_boy_id);
        $delivery_boy_profile = DeliveryBoy::where('user_id', $delivery_boy->id)->first();
        
        if ($delivery_boy_profile) {
            $payment = new DeliveryBoyPayment;
            $payment->user_id = $delivery_boy->id;
            $payment->payment = $request->payment_amount;
            $payment->save();

            $delivery_boy_profile->total_earning -= $request->payment_amount;
            $delivery_boy_profile->save();

            flash(translate('Payment successful'))->success();
        }

        return back();
    }

    /**
     * Delivery boys payment histories
     */
    public function delivery_boys_payment_histories()
    {
        $payments = DeliveryBoyPayment::with('user')->latest()->paginate(15);
        return view('backend.delivery_boys.payment_history', compact('payments'));
    }

    /**
     * Delivery boys collection histories
     */
    public function delivery_boys_collection_histories()
    {
        $collections = DeliveryBoyCollection::with('user')->latest()->paginate(15);
        return view('backend.delivery_boys.collection_history', compact('collections'));
    }

    /**
     * Cancel request list for admin
     */
    public function cancel_request_list()
    {
        $cancel_requests = Order::where('cancel_request', 1)
            ->whereNotNull('assign_delivery_boy')
            ->with(['delivery_boy', 'user'])
            ->latest()
            ->paginate(15);
        
        return view('backend.delivery_boys.cancel_requests', compact('cancel_requests'));
    }

    // Delivery Boy User Methods

    /**
     * Assigned deliveries for delivery boy
     */
    public function assigned_delivery()
    {
        $orders = Order::where('assign_delivery_boy', Auth::user()->id)
            ->where('delivery_status', 'pending')
            ->where('cancel_request', '0')
            ->latest()
            ->paginate(15);
        
        return view('delivery_boy.assigned_deliveries', compact('orders'));
    }

    /**
     * Pickup deliveries for delivery boy
     */
    public function pickup_delivery()
    {
        $orders = Order::where('assign_delivery_boy', Auth::user()->id)
            ->where('delivery_status', 'picked_up')
            ->where('cancel_request', '0')
            ->latest()
            ->paginate(15);
        
        return view('delivery_boy.pickup_deliveries', compact('orders'));
    }

    /**
     * On the way deliveries
     */
    public function on_the_way_deliveries()
    {
        $orders = Order::where('assign_delivery_boy', Auth::user()->id)
            ->where('delivery_status', 'on_the_way')
            ->where('cancel_request', '0')
            ->latest()
            ->paginate(15);
        
        return view('delivery_boy.on_the_way_deliveries', compact('orders'));
    }

    /**
     * Completed deliveries
     */
    public function completed_delivery()
    {
        $orders = Order::where('assign_delivery_boy', Auth::user()->id)
            ->where('delivery_status', 'delivered')
            ->latest()
            ->paginate(15);
        
        return view('delivery_boy.completed_deliveries', compact('orders'));
    }

    /**
     * Pending deliveries
     */
    public function pending_delivery()
    {
        $orders = Order::where('assign_delivery_boy', Auth::user()->id)
            ->where('delivery_status', '!=', 'delivered')
            ->where('delivery_status', '!=', 'cancelled')
            ->where('cancel_request', '0')
            ->latest()
            ->paginate(15);
        
        return view('delivery_boy.pending_deliveries', compact('orders'));
    }

    /**
     * Cancelled deliveries
     */
    public function cancelled_delivery()
    {
        $orders = Order::where('assign_delivery_boy', Auth::user()->id)
            ->where('delivery_status', 'cancelled')
            ->latest()
            ->paginate(15);
        
        return view('delivery_boy.cancelled_deliveries', compact('orders'));
    }

    /**
     * Total collection for delivery boy
     */
    public function total_collection()
    {
        $delivery_boy = DeliveryBoy::where('user_id', Auth::user()->id)->first();
        $collections = DeliveryBoyCollection::where('user_id', Auth::user()->id)->latest()->paginate(15);
        
        return view('delivery_boy.total_collection', compact('delivery_boy', 'collections'));
    }

    /**
     * Total earnings for delivery boy
     */
    public function total_earning()
    {
        $delivery_boy = DeliveryBoy::where('user_id', Auth::user()->id)->first();
        $earnings = DeliveryBoyPayment::where('user_id', Auth::user()->id)->latest()->paginate(15);
        
        return view('delivery_boy.total_earnings', compact('delivery_boy', 'earnings'));
    }

    /**
     * Cancel request for specific order
     */
    public function cancel_request($id)
    {
        $order = Order::findOrFail($id);
        
        if ($order->assign_delivery_boy == Auth::user()->id) {
            $order->cancel_request = 1;
            $order->save();
            
            flash(translate('Cancel request submitted successfully'))->success();
        } else {
            flash(translate('Unauthorized action'))->error();
        }
        
        return back();
    }

    /**
     * Delivery boys cancel request list for delivery boy
     */
    public function delivery_boys_cancel_request_list()
    {
        $cancel_requests = Order::where('assign_delivery_boy', Auth::user()->id)
            ->where('cancel_request', 1)
            ->latest()
            ->paginate(15);
        
        return view('delivery_boy.cancel_request_list', compact('cancel_requests'));
    }

    /**
     * Order detail for delivery boy
     */
    public function order_detail($id)
    {
        $order = Order::findOrFail($id);
        
        if ($order->assign_delivery_boy != Auth::user()->id) {
            flash(translate('Unauthorized access'))->error();
            return redirect()->back();
        }
        
        return view('delivery_boy.order_detail', compact('order'));
    }
} 