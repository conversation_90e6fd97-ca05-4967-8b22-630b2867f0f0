<?php

namespace App\Http\Controllers;

use App\Models\Collection;
use Illuminate\Http\Request;
use App\Models\Search;
use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Color;
use App\Models\Shop;
use App\Models\Attribute;
use App\Models\AttributeCategory;
use App\Utility\CategoryUtility;

class CustomerCollectionsController extends Controller
{
    public function index(Request $request, $category_id = null, $brand_id = null, $collection_id = null, $tag = null)
    {

        $collection_id = $request->collection_id;
        $tag_in = $request->tag;
        $query = $request->keyword;
        $sort_by = $request->sort_by;
        $min_price = $request->min_price;
        $max_price = $request->max_price;
        $seller_id = $request->seller_id;
        $supplier_id = $request->supplier_id;
        $attributes = Attribute::all();
        $selected_attribute_values = array();
        $colors = Color::all();
        $selected_color = null;
        $conditions = [];

        $category_ids[] = $category_id;

        $products = Product::query();

        if ($category_id != 'all') {
            $products->whereIn('category_id', $category_ids);
        }

        if ($collection_id == 'all') {
            $collections = Collection::all();
            $products->where(function ($p) use ($collections) {
                foreach ($collections as $collection) {
                    if ($collection->type === 'automated') {
                        $conditions = $collection->conditions;

                        if (!empty($conditions['tags']) && $conditions['tags']) {
                            $tags = preg_split('/\s*,\s*(?![^(]*\))/', $conditions['tags']);
                            foreach ($tags as $tag) {
                                $p->orWhere('tags', 'like', "%{$tag}%");
                            }
                        }
                    } else {
                        $p->whereIn('id', $collection->product_ids);
                    }
                }
            });
        } else {
            $collection = Collection::findOrFail($collection_id);
            if ($collection->type === 'automated') {
                $conditions = $collection->conditions;

                if (!empty($conditions['tags']) && $conditions['tags']) {
                    $tags = preg_split('/\s*,\s*(?![^(]*\))/', $conditions['tags']);
                    $products->where(function ($p) use ($tags) {
                        foreach ($tags as $tag) {
                            $p->orWhere('tags', 'like', "%{$tag}%");
                        }
                    });
                }
            } else {
                $products->whereIn('id', $collection->product_ids);
            }
        }

        if ($min_price != null && $max_price != null) {
            $products->where('unit_price', '>=', $min_price)->where('unit_price', '<=', $max_price);
        }

        if ($tag_in) {
            $products->where('tags', 'like', "%{$tag_in}%");
        }

        switch ($sort_by) {
            case 'newest':
                $products->orderBy('created_at', 'desc');
                break;
            case 'oldest':
                $products->orderBy('created_at', 'asc');
                break;
            case 'price-asc':
                $products->orderBy('unit_price', 'asc');
                break;
            case 'price-desc':
                $products->orderBy('unit_price', 'desc');
                break;
            default:
                $products->orderBy('id', 'desc');
                break;
        }

        $products = filter_products($products)->with('taxes')->paginate(24)->appends(request()->query());

        $collections = Collection::all();
        $brands = Brand::all();

        return view('frontend.collections', compact('products', 'query', 'category_id', 'collections', 'min_price', 'max_price', 'collection_id', 'brands', 'tag_in', 'sort_by'));
    }

    public function listing(Request $request)
    {
        return $this->index($request);
    }

    public function listingByCategory(Request $request,  $collection_id, $category_slug,)
    {
        $category = Category::where('slug', $category_slug)->firstOrFail();
        $collection = Collection::findOrFail($collection_id);
        if ($category != null) {
            return $this->index($request, $category->id, null, $collection_id);
        }
        abort(404);
    }

    public function listingByCategoryAndCollection(Request $request, $collection_id, $category_slug)
    {
        // dd($collection_id, $category_slug);
        if ($category_slug === 'all') {
            return $this->index($request,  'all', $collection_id);
        }
        $category =  Category::where('slug', $category_slug)->firstOrFail();
        if ($category != null) {
            return $this->index($request, $category->id, $collection_id);
        }
        abort(404);
    }
    public function show(Request $request, $collection_id)
    {
        if ($collection_id != null) {
            return $this->index($request, null, null, $collection_id);
        }
        abort(404);
    }
    public function listingByBrand(Request $request, $brand_slug)
    {
        $brand = Brand::where('slug', $brand_slug)->first();
        if ($brand != null) {
            return $this->index($request, null, $brand->id);
        }
        abort(404);
    }
    public function listingByTags(Request $request, $tags)
    {
        if ($tags != null) {
            return $this->index($request, null, null, null, $tags);
        }
        abort(404);
    }
    // Suggestional Search
    public function ajax_search(Request $request)
    {
        $keywords = array();
        $query = $request->search;
        $products = Product::where('published', 1)->where('tags', 'like', '%' . $query . '%')->get();
        foreach ($products as $key => $product) {
            foreach (explode(',', $product->tags) as $key => $tag) {
                if (stripos($tag, $query) !== false) {
                    if (sizeof($keywords) > 5) {
                        break;
                    } else {
                        if (!in_array(strtolower($tag), $keywords)) {
                            array_push($keywords, strtolower($tag));
                        }
                    }
                }
            }
        }

        $products_query = filter_products(Product::query());

        $products_query = $products_query->where('published', 1)
            ->where(function ($q) use ($query) {
                foreach (explode(' ', trim($query)) as $word) {
                    $q->where('name', 'like', '%' . $word . '%')
                        ->orWhere('tags', 'like', '%' . $word . '%')
                        ->orWhereHas('product_translations', function ($q) use ($word) {
                            $q->where('name', 'like', '%' . $word . '%');
                        })
                        ->orWhereHas('stocks', function ($q) use ($word) {
                            $q->where('sku', 'like', '%' . $word . '%');
                        });
                }
            });
        $case1 = $query . '%';
        $case2 = '%' . $query . '%';

        $products_query->orderByRaw("CASE
                WHEN name LIKE '$case1' THEN 1
                WHEN name LIKE '$case2' THEN 2
                ELSE 3
                END");
        $products = $products_query->limit(3)->get();

        $categories = Category::where('name', 'like', '%' . $query . '%')->get()->take(3);

        $shops = Shop::whereIn('user_id', verified_sellers_id())->where('name', 'like', '%' . $query . '%')->get()->take(3);

        if (sizeof($keywords) > 0 || sizeof($categories) > 0 || sizeof($products) > 0 || sizeof($shops) > 0) {
            return view('frontend.partials.search_content', compact('products', 'categories', 'keywords', 'shops'));
        }
        return '0';
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $search = Search::where('query', $request->keyword)->first();
        if ($search != null) {
            $search->count = $search->count + 1;
            $search->save();
        } else {
            $search = new Search;
            $search->query = $request->keyword;
            $search->save();
        }
    }
}
