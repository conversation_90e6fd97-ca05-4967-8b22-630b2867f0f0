<?php

// app/Http/Controllers/SupplierController.php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Supplier;
use App\Models\User; // Assuming you have a User model for the added_by relationship

class SupplierController extends Controller
{
    public function index(Request $request)
    {
        $sort_search =null;
        $suppliers = Supplier::orderBy('created_at', 'desc');
        if ($request->has('search')){
            $sort_search = $request->search;
            $suppliers = $suppliers->where('name', 'like', '%'.$sort_search.'%');
        }
        $suppliers = $suppliers->paginate(15);
        return view('backend.suppliers.index', compact('suppliers'));
    }

    public function create()
    {
        return view('backend.suppliers.create');
    }

    public function store(Request $request)
    {
        // Validate the incoming request data
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'added_by' => 'required|exists:users,id', // Validate added_by as an existing user ID
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string|max:255',
            'phone_number' => 'nullable|string|max:20',
        ]);

        try {
            // Create a new Supplier instance with the validated data
            $supplier = new Supplier();
            $supplier->name = $request->input('name');
            $supplier->description = $request->input('description');
            $supplier->added_by = $request->input('added_by');
            $supplier->email = $request->input('email');
            $supplier->address = $request->input('address');
            $supplier->phone_number = $request->input('phone_number');

            // Save the new Supplier to the database
            $supplier->save();

            flash(translate('Supplier created successfully'))->success();
            return redirect()->route('suppliers.index');
        } catch (\Exception $e) {
            flash(translate('Failed to create supplier'))->error()->important();
            return redirect()->back()->withInput();
        }
    }


    public function show(Supplier $supplier)
    {
        return view('backend.suppliers.show', compact('supplier'));
    }

    public function edit(Supplier $supplier)
    {
        return view('backend.suppliers.edit', compact('supplier'));
    }

    public function update(Request $request, Supplier $supplier)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'added_by' => 'required|exists:users,id',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string|max:255',
            'phone_number' => 'nullable|string|max:20',
        ]);

        try {
            // Update the supplier instance with the validated data
            $supplier->name = $request->input('name');
            $supplier->description = $request->input('description');
            $supplier->added_by = $request->input('added_by');
            $supplier->email = $request->input('email');
            $supplier->address = $request->input('address');
            $supplier->phone_number = $request->input('phone_number');

            // Save the updated supplier to the database
            $supplier->save();

        // Redirect to the index page with a success message
            flash(translate('Supplier updated successfully'))->success();
            return redirect()->route('suppliers.index');
        } catch (\Exception $e) {
            // If an error occurs, display an error message and redirect back with input
            flash(translate('Failed to update supplier'))->error()->important();
            return redirect()->back()->withInput();
        }
    }

    public function destroy(Supplier $supplier)
    {
        $supplier->delete();
        flash(translate('Supplier deleted successfully.'))->success();
        return redirect()->route('suppliers.index');
    }
}
