<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\ReturnRequest;
use App\Notifications\order\refund\OrderRefundRequestNotification;
use App\Notifications\order\return_exchange\OrderReturnExchangeRequestNotification;
use App\Notifications\order\return_exchange\ReturnApprovedNotification;
use App\Notifications\order\return_exchange\ReturnExchangeRequestDeclineNotification;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use App\Models\OrderDetail;
use App\Models\RefundRequest;
use Artisan;
use Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ReturnRequestController extends Controller
{
    public function __construct()
    {
        //  Staff Permission Check
        $this->middleware(['permission:view_refund_requests'])->only('admin_index');
        $this->middleware(['permission:view_approved_refund_requests'])->only('paid_index');
        $this->middleware(['permission:view_rejected_refund_requests'])->only('rejected_index');
        $this->middleware(['permission:refund_request_configuration'])->only('refund_config');
    }


    //Store Customer return Request

    /**
     * @param Request $request
     * @param $id
     * @return RedirectResponse
     */
    public function request_store(Request $request, $id)
    {
        try {
            DB::beginTransaction();
            $order_detail = OrderDetail::where('id', $id)->first();
            $return = new ReturnRequest;
            $return->user_id = Auth::user()->id;
            $return->order_id = $order_detail->order_id;
            $return->order_detail_id = $order_detail->id;
            $return->seller_id = $order_detail->seller_id;
            $return->seller_approval = 0;
            $return->reason = $request->reason;
            $return->admin_approval = 0;
            $return->admin_seen = 0;
            $return->return_amount = $order_detail->price + $order_detail->tax;
            $return->return_status = 0;
            OrderDetail::where('id', $id)->update(['return_status' => 3]);
            $return->save();
            DB::commit();
            $array = array();
            $order = Order::where('id', $order_detail->order_id)->first();
            $array['order_code'] = $order->code;
            $array['user_name'] = $order->user->name;
            $array['type'] = 'Return';
            $array['subject'] = translate('We’ve Received Your Return Request – ') .$order->code . " ." ;
            $array['product_name'] = $order_detail?->product?->name;

            try {
                $order->user->notify(new OrderReturnExchangeRequestNotification($array));
                $command = 'php ' . base_path() . '/artisan queue:work --verbose --tries=1 --timeout=220 > /dev/null &';
                exec($command);
            } catch (\Exception $e) {
                Log::channel('email_logs')->error('Error occurred while sending  Order Return request email in ReturnRequestController : ' . $e->getMessage());
            }


            flash(translate("Return Request has been sent successfully"))->success();
            return redirect()->route('purchase_history.index');
        } catch (\Exception $e) {
            DB::rollBack();
            flash(translate("something wrong"))->error();
            return redirect()->route('purchase_history.index');
        }
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function admin_index()
    {
        $returns = ReturnRequest::where('return_status', 0)->latest()->paginate(15);
        return view('return_request.index', compact('returns'));
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */

    public function customer_index()
    {
        $returns = ReturnRequest::where('user_id', Auth::user()->id)->latest()->paginate(10);
        return view('return_request.frontend.return_request.index', compact('returns'));
    }

    public function approvedReturn()
    {
        $returns = ReturnRequest::where('return_status', 1)->latest()->paginate(15);
        return view('return_request.approve_return', compact('returns'));
    }

    public function rejected_index()
    {
        $returns = ReturnRequest::where('return_status', 2)->latest()->paginate(15);
        return view('return_request.rejected_return', compact('returns'));
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|void
     */
    public function returnRequestStatusChange(Request $request): RedirectResponse
    {
        try {
            DB::beginTransaction();
            // Update ReturnRequest
            ReturnRequest::where('id', $request->return_id)->update(['return_status' => 1]);
            // Update OrderDetail
            $returnRequest = ReturnRequest::findOrFail($request->return_id);
            OrderDetail::where('id', $returnRequest->order_detail_id)->update(['return_status' => 1,]);

            $order_detail = OrderDetail::where('id', $returnRequest->order_detail_id)->first();
            $refund = new RefundRequest;
            $refund->user_id = Auth::user()->id;
            $refund->order_id = $order_detail->order_id;
            $refund->order_detail_id = $order_detail->id;
            $refund->seller_id = $order_detail->seller_id;
            $refund->seller_approval = 0;
            $refund->reason = $request->reason;
            $refund->admin_approval = 0;
            $refund->admin_seen = 0;
            $refund->refund_amount = $order_detail->price + $order_detail->tax;
            $refund->refund_status = 0;
            $refund->save();
            if (Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'staff') {
                ReturnRequest::where('id', $request->return_id)->update(['seller_approval' => 1, 'admin_approval' => 1]);
            }

            $array = array();
            $order = Order::where('id', $order_detail->order_id)->first();
            $array['order_code'] = $order->code;
            $array['user_name'] = $order->user->name;
            $array['type'] = 'Return';
            $array['subject'] = translate(' Your Return Request Has Been Approved  – ') .$order->code . " ." ;
            $array['product_name'] = $order_detail?->product?->name;

            try {
                $order->user->notify(new ReturnApprovedNotification($array));
                $command = 'php ' . base_path() . '/artisan queue:work --verbose --tries=1 --timeout=220 > /dev/null &';
                exec($command);
            } catch (\Exception $e) {
                Log::channel('email_logs')->error('Error occurred while sending  Order Return Approved  email in ReturnRequestController : ' . $e->getMessage());
            }

            DB::commit();
            flash(translate("Return & Refund Request has been sent successfully"))->success();
            return back();
        } catch (\Exception $e) {
            DB::rollBack();
            flash(translate("something wrong"))->error();
            return back();
        }
    }

    public function reject_return_request(Request $request)
    {
        try {
            DB::beginTransaction();
            $return = ReturnRequest::findOrFail($request->return_id);
            if (Auth::user()->user_type == 'admin' || Auth::user()->user_type == 'staff') {
                $return->admin_approval = 2;
                $return->return_status  = 2;
                $return->reject_reason  = $request->reject_reason;
            } else {
                $return->seller_approval = 2;
                $return->reject_reason  = $request->reject_reason;
            }
            $return->save();
            OrderDetail::where('id', $return->order_detail_id)->update(['return_status' => 2]);
            DB::commit();

            $array = array();
            $order = Order::where('id', $return->order_id)->first();
            $order_detail = OrderDetail::where('id', $return->order_detail_id)->first();
            $array['order_code'] = $order->code;
            $array['user_name'] = $order->user->name;
            $array['type'] = 'Return';
            $array['subject'] = translate('  Update on Your Return Request   – ') .$order->code . " ." ;
            $array['reject_reason'] = $return->reject_reason;
            //dd($array);

            try {
                $order->user->notify(new ReturnExchangeRequestDeclineNotification($array));
                $command = 'php ' . base_path() . '/artisan queue:work --verbose --tries=1 --timeout=220 > /dev/null &';
                exec($command);
            } catch (\Exception $e) {
                Log::channel('email_logs')->error('Error occurred while sending  Order Return Approved  email in ReturnRequestController : ' . $e->getMessage());
            }


            return back();
        } catch (\Exception $e) {
            DB::rollBack();
            flash(translate("something wrong"))->error();
            return back();
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function return_request_send_page($id)
    {
        $order_detail = OrderDetail::findOrFail($id);
        if ($order_detail->product != null && $order_detail->product->refundable == 1) {
            return view('return_request.frontend.return_request.create', compact('order_detail'));
        } else {
            return back();
        }
    }

    /**
     * @param $id
     * @return mixed
     */
    public function reject_reason_view($id)
    {
        $return = ReturnRequest::findOrFail($id);
        return $return->reject_reason;
    }
}
