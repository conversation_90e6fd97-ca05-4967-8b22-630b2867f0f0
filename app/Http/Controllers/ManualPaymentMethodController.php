<?php

namespace App\Http\Controllers;

use App\Models\ManualPaymentMethod;
use App\Models\Order;
use App\Models\CustomerPackage;
use App\Models\SellerPackage;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ManualPaymentMethodController extends Controller
{
    /**
     * Display a listing of manual payment methods
     */
    public function index()
    {
        $manual_payment_methods = ManualPaymentMethod::latest()->paginate(20);
        return view('backend.manual_payment_methods.index', compact('manual_payment_methods'));
    }

    /**
     * Show the form for creating a new manual payment method
     */
    public function create()
    {
        return view('backend.manual_payment_methods.create');
    }

    /**
     * Store a newly created manual payment method
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|string|max:255',
            'heading' => 'required|string|max:255',
            'description' => 'nullable|string',
            'bank_info' => 'nullable|string',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        if ($validator->fails()) {
            flash(translate('Please check your inputs and try again'))->error();
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $manual_payment_method = new ManualPaymentMethod;
        $manual_payment_method->type = $request->type;
        $manual_payment_method->heading = $request->heading;
        $manual_payment_method->description = $request->description;
        $manual_payment_method->bank_info = $request->bank_info;
        
        if ($request->hasFile('photo')) {
            $manual_payment_method->photo = $request->file('photo')->store('uploads/manual_payment_methods');
        }
        
        $manual_payment_method->save();

        flash(translate('Manual payment method created successfully'))->success();
        return redirect()->route('manual_payment_methods.index');
    }

    /**
     * Display the specified manual payment method
     */
    public function show($id)
    {
        $manual_payment_method = ManualPaymentMethod::findOrFail($id);
        return view('backend.manual_payment_methods.show', compact('manual_payment_method'));
    }

    /**
     * Show the form for editing the specified manual payment method
     */
    public function edit($id)
    {
        $manual_payment_method = ManualPaymentMethod::findOrFail($id);
        return view('backend.manual_payment_methods.edit', compact('manual_payment_method'));
    }

    /**
     * Update the specified manual payment method
     */
    public function update(Request $request, $id)
    {
        $manual_payment_method = ManualPaymentMethod::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'type' => 'required|string|max:255',
            'heading' => 'required|string|max:255',
            'description' => 'nullable|string',
            'bank_info' => 'nullable|string',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        if ($validator->fails()) {
            flash(translate('Please check your inputs and try again'))->error();
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $manual_payment_method->type = $request->type;
        $manual_payment_method->heading = $request->heading;
        $manual_payment_method->description = $request->description;
        $manual_payment_method->bank_info = $request->bank_info;
        
        if ($request->hasFile('photo')) {
            $manual_payment_method->photo = $request->file('photo')->store('uploads/manual_payment_methods');
        }
        
        $manual_payment_method->save();

        flash(translate('Manual payment method updated successfully'))->success();
        return redirect()->route('manual_payment_methods.index');
    }

    /**
     * Remove the specified manual payment method
     */
    public function destroy($id)
    {
        $manual_payment_method = ManualPaymentMethod::findOrFail($id);
        
        // Delete associated photo if exists
        if ($manual_payment_method->photo && file_exists(storage_path('app/' . $manual_payment_method->photo))) {
            unlink(storage_path('app/' . $manual_payment_method->photo));
        }
        
        $manual_payment_method->delete();

        flash(translate('Manual payment method deleted successfully'))->success();
        return redirect()->route('manual_payment_methods.index');
    }

    /**
     * Show payment modal for order payment
     */
    public function show_payment_modal(Request $request)
    {
        $request->validate([
            'order_id' => 'required|exists:orders,id'
        ]);

        $order = Order::findOrFail($request->order_id);
        $manual_payment_methods = ManualPaymentMethod::all();
        
        return view('frontend.partials.payment_modal', compact('order', 'manual_payment_methods'));
    }

    /**
     * Submit offline payment for order
     */
    public function submit_offline_payment(Request $request)
    {
        $request->validate([
            'order_id' => 'required|exists:orders,id',
            'manual_payment_method_id' => 'required|exists:manual_payment_methods,id',
            'amount' => 'required|numeric|min:0',
            'trx_id' => 'required|string|max:255',
            'photo' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $order = Order::findOrFail($request->order_id);
        
        // Store payment proof
        $photo_path = $request->file('photo')->store('uploads/offline_payments');
        
        // Create offline payment record
        $order->manual_payment = 1;
        $order->manual_payment_type = $request->manual_payment_method_id;
        $order->manual_payment_data = json_encode([
            'amount' => $request->amount,
            'trx_id' => $request->trx_id,
            'photo' => $photo_path,
            'submitted_at' => now()
        ]);
        $order->save();

        flash(translate('Payment submitted successfully. Please wait for approval.'))->success();
        return redirect()->route('purchase_history.index');
    }

    /**
     * Show offline wallet recharge modal
     */
    public function offline_recharge_modal(Request $request)
    {
        $manual_payment_methods = ManualPaymentMethod::all();
        return view('frontend.partials.wallet_recharge_modal', compact('manual_payment_methods'));
    }

    /**
     * Show offline customer package purchase modal
     */
    public function offline_customer_package_purchase_modal(Request $request)
    {
        $request->validate([
            'package_id' => 'required|exists:customer_packages,id'
        ]);

        $package = CustomerPackage::findOrFail($request->package_id);
        $manual_payment_methods = ManualPaymentMethod::all();
        
        return view('frontend.partials.customer_package_payment_modal', compact('package', 'manual_payment_methods'));
    }

    /**
     * Show offline seller package purchase modal
     */
    public function offline_seller_package_purchase_modal(Request $request)
    {
        $request->validate([
            'package_id' => 'required|exists:seller_packages,id'
        ]);

        $package = SellerPackage::findOrFail($request->package_id);
        $manual_payment_methods = ManualPaymentMethod::all();
        
        return view('frontend.partials.seller_package_payment_modal', compact('package', 'manual_payment_methods'));
    }

    /**
     * Get manual payment methods for API
     */
    public function getPaymentMethods()
    {
        $methods = ManualPaymentMethod::all();
        return response()->json([
            'success' => true,
            'data' => $methods
        ]);
    }

    /**
     * Process offline payment (internal method)
     */
    public function processOfflinePayment($payment_data)
    {
        try {
            // Process the offline payment based on payment_data
            // This method can be called from other controllers
            
            return [
                'success' => true,
                'message' => 'Payment processed successfully'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Payment processing failed: ' . $e->getMessage()
            ];
        }
    }
} 