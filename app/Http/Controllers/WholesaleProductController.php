<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\ProductStock;
use App\Models\ProductTax;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Tax;
use App\Models\User;
use App\Models\ProductTranslation;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class WholesaleProductController extends Controller
{
    public function __construct()
    {
        // Staff Permission Check for admin routes
        $this->middleware(['permission:view_all_wholesale_products'])->only([
            'all_wholesale_products', 'in_house_wholesale_products', 'seller_wholesale_products'
        ]);
        $this->middleware(['permission:add_wholesale_product'])->only([
            'product_create_admin', 'product_store_admin'
        ]);
        $this->middleware(['permission:edit_wholesale_product'])->only([
            'product_edit_admin', 'product_update_admin'
        ]);
        $this->middleware(['permission:delete_wholesale_product'])->only([
            'product_destroy_admin'
        ]);
    }

    // Admin Methods

    /**
     * Show all wholesale products for admin
     */
    public function all_wholesale_products(Request $request)
    {
        $sort_search = null;
        $products = Product::where('wholesale_product', 1)->latest();

        if ($request->has('search')) {
            $sort_search = $request->search;
            $products = $products->where('name', 'like', '%' . $sort_search . '%');
        }

        $products = $products->paginate(15);
        return view('backend.wholesale_products.index', compact('products', 'sort_search'));
    }

    /**
     * Show in-house wholesale products for admin
     */
    public function in_house_wholesale_products(Request $request)
    {
        $sort_search = null;
        $products = Product::where('wholesale_product', 1)
            ->where('added_by', 'admin')
            ->latest();

        if ($request->has('search')) {
            $sort_search = $request->search;
            $products = $products->where('name', 'like', '%' . $sort_search . '%');
        }

        $products = $products->paginate(15);
        return view('backend.wholesale_products.inhouse', compact('products', 'sort_search'));
    }

    /**
     * Show seller wholesale products for admin
     */
    public function seller_wholesale_products(Request $request)
    {
        $sort_search = null;
        $products = Product::where('wholesale_product', 1)
            ->where('added_by', 'seller')
            ->latest();

        if ($request->has('search')) {
            $sort_search = $request->search;
            $products = $products->where('name', 'like', '%' . $sort_search . '%');
        }

        $products = $products->paginate(15);
        return view('backend.wholesale_products.seller', compact('products', 'sort_search'));
    }

    /**
     * Show admin wholesale product create form
     */
    public function product_create_admin()
    {
        $categories = Category::where('level', 0)->get();
        $brands = Brand::all();
        $taxes = Tax::all();
        return view('backend.wholesale_products.create', compact('categories', 'brands', 'taxes'));
    }

    /**
     * Store admin wholesale product
     */
    public function product_store_admin(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'unit_price' => 'required|numeric|min:0',
            'min_qty' => 'required|integer|min:1',
        ]);

        DB::beginTransaction();
        
        try {
            $product = new Product;
            $product->name = $request->name;
            $product->category_id = $request->category_id;
            $product->brand_id = $request->brand_id;
            $product->photos = $request->photos;
            $product->thumbnail_img = $request->thumbnail_img;
            $product->description = $request->description;
            $product->unit_price = $request->unit_price;
            $product->wholesale_product = 1;
            $product->min_qty = $request->min_qty;
            $product->slug = Str::slug($request->name) . '-' . Str::random(5);
            $product->user_id = 1; // Admin
            $product->added_by = 'admin';
            $product->published = 1;
            $product->unit = $request->unit ?? 'pc';
            $product->meta_title = $request->meta_title;
            $product->meta_description = $request->meta_description;

            if ($product->save()) {
                // Create stock entry
                $stock = new ProductStock;
                $stock->product_id = $product->id;
                $stock->variant = '';
                $stock->sku = $product->slug;
                $stock->price = $request->unit_price;
                $stock->qty = $request->quantity ?? 1;
                $stock->save();

                // Handle taxes
                if ($request->has('tax_id') && $request->tax_id) {
                    foreach ($request->tax_id as $key => $tax_id) {
                        $product_tax = new ProductTax;
                        $product_tax->tax_id = $tax_id;
                        $product_tax->product_id = $product->id;
                        $product_tax->tax = $request->tax[$key];
                        $product_tax->tax_type = $request->tax_type[$key];
                        $product_tax->save();
                    }
                }

                // Create product translation
                $product_translation = new ProductTranslation;
                $product_translation->product_id = $product->id;
                $product_translation->name = $request->name;
                $product_translation->unit = $request->unit ?? 'pc';
                $product_translation->description = $request->description;
                $product_translation->lang = env('DEFAULT_LANGUAGE', 'en');
                $product_translation->save();

                DB::commit();
                flash(translate('Wholesale product has been created successfully'))->success();
                return redirect()->route('wholesale_products.all');
            }
        } catch (\Exception $e) {
            DB::rollback();
            flash(translate('Something went wrong: ') . $e->getMessage())->error();
            return back();
        }

        flash(translate('Something went wrong'))->error();
        return back();
    }

    /**
     * Show admin wholesale product edit form
     */
    public function product_edit_admin($id)
    {
        $product = Product::where('wholesale_product', 1)->findOrFail($id);
        $categories = Category::where('level', 0)->get();
        $brands = Brand::all();
        $taxes = Tax::all();
        return view('backend.wholesale_products.edit', compact('product', 'categories', 'brands', 'taxes'));
    }

    /**
     * Update admin wholesale product
     */
    public function product_update_admin(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'unit_price' => 'required|numeric|min:0',
            'min_qty' => 'required|integer|min:1',
        ]);

        DB::beginTransaction();
        
        try {
            $product = Product::where('wholesale_product', 1)->findOrFail($id);
            $product->name = $request->name;
            $product->category_id = $request->category_id;
            $product->brand_id = $request->brand_id;
            $product->photos = $request->photos;
            $product->thumbnail_img = $request->thumbnail_img;
            $product->description = $request->description;
            $product->unit_price = $request->unit_price;
            $product->min_qty = $request->min_qty;
            $product->unit = $request->unit ?? 'pc';
            $product->meta_title = $request->meta_title;
            $product->meta_description = $request->meta_description;

            if ($product->save()) {
                // Update stock
                $stock = ProductStock::where('product_id', $product->id)->first();
                if ($stock) {
                    $stock->price = $request->unit_price;
                    $stock->qty = $request->quantity ?? $stock->qty;
                    $stock->save();
                }

                // Update taxes
                ProductTax::where('product_id', $product->id)->delete();
                if ($request->has('tax_id') && $request->tax_id) {
                    foreach ($request->tax_id as $key => $tax_id) {
                        $product_tax = new ProductTax;
                        $product_tax->tax_id = $tax_id;
                        $product_tax->product_id = $product->id;
                        $product_tax->tax = $request->tax[$key];
                        $product_tax->tax_type = $request->tax_type[$key];
                        $product_tax->save();
                    }
                }

                // Update translation
                $product_translation = ProductTranslation::where('product_id', $product->id)
                    ->where('lang', env('DEFAULT_LANGUAGE', 'en'))
                    ->first();
                
                if ($product_translation) {
                    $product_translation->name = $request->name;
                    $product_translation->unit = $request->unit ?? 'pc';
                    $product_translation->description = $request->description;
                    $product_translation->save();
                }

                DB::commit();
                flash(translate('Wholesale product has been updated successfully'))->success();
                return redirect()->route('wholesale_products.all');
            }
        } catch (\Exception $e) {
            DB::rollback();
            flash(translate('Something went wrong: ') . $e->getMessage())->error();
            return back();
        }

        flash(translate('Something went wrong'))->error();
        return back();
    }

    /**
     * Delete admin wholesale product
     */
    public function product_destroy_admin($id)
    {
        $product = Product::where('wholesale_product', 1)->findOrFail($id);
        
        DB::beginTransaction();
        
        try {
            // Delete related data
            ProductStock::where('product_id', $product->id)->delete();
            ProductTax::where('product_id', $product->id)->delete();
            ProductTranslation::where('product_id', $product->id)->delete();

            if ($product->delete()) {
                DB::commit();
                flash(translate('Wholesale product has been deleted successfully'))->success();
            } else {
                DB::rollback();
                flash(translate('Something went wrong'))->error();
            }
        } catch (\Exception $e) {
            DB::rollback();
            flash(translate('Something went wrong: ') . $e->getMessage())->error();
        }

        return back();
    }

    // Seller Methods

    /**
     * Show seller wholesale products
     */
    public function wholesale_products_list_seller(Request $request)
    {
        $sort_search = null;
        $products = Product::where('wholesale_product', 1)
            ->where('user_id', Auth::id())
            ->latest();

        if ($request->has('search')) {
            $sort_search = $request->search;
            $products = $products->where('name', 'like', '%' . $sort_search . '%');
        }

        $products = $products->paginate(15);
        return view('seller.wholesale_products.index', compact('products', 'sort_search'));
    }

    /**
     * Show seller wholesale product create form
     */
    public function product_create_seller()
    {
        $categories = Category::where('level', 0)->get();
        $brands = Brand::all();
        $taxes = Tax::all();
        return view('seller.wholesale_products.create', compact('categories', 'brands', 'taxes'));
    }

    /**
     * Store seller wholesale product
     */
    public function product_store_seller(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'unit_price' => 'required|numeric|min:0',
            'min_qty' => 'required|integer|min:1',
        ]);

        // Check seller package limits
        if (addon_is_activated('seller_subscription')) {
            $shop = Auth::user()->shop;
            if ($shop && $shop->seller_package) {
                if ($shop->seller_package->product_upload_limit <= Auth::user()->products()->count()) {
                    flash(translate('Upload limit has been reached. Please upgrade your package.'))->error();
                    return back();
                }
            }
        }

        DB::beginTransaction();
        
        try {
            $product = new Product;
            $product->name = $request->name;
            $product->category_id = $request->category_id;
            $product->brand_id = $request->brand_id;
            $product->photos = $request->photos;
            $product->thumbnail_img = $request->thumbnail_img;
            $product->description = $request->description;
            $product->unit_price = $request->unit_price;
            $product->wholesale_product = 1;
            $product->min_qty = $request->min_qty;
            $product->slug = Str::slug($request->name) . '-' . Str::random(5);
            $product->user_id = Auth::id();
            $product->added_by = 'seller';
            $product->published = 0; // Needs approval
            $product->unit = $request->unit ?? 'pc';
            $product->meta_title = $request->meta_title;
            $product->meta_description = $request->meta_description;

            if ($product->save()) {
                // Create stock entry
                $stock = new ProductStock;
                $stock->product_id = $product->id;
                $stock->variant = '';
                $stock->sku = $product->slug;
                $stock->price = $request->unit_price;
                $stock->qty = $request->quantity ?? 1;
                $stock->save();

                // Handle taxes
                if ($request->has('tax_id') && $request->tax_id) {
                    foreach ($request->tax_id as $key => $tax_id) {
                        $product_tax = new ProductTax;
                        $product_tax->tax_id = $tax_id;
                        $product_tax->product_id = $product->id;
                        $product_tax->tax = $request->tax[$key];
                        $product_tax->tax_type = $request->tax_type[$key];
                        $product_tax->save();
                    }
                }

                // Create product translation
                $product_translation = new ProductTranslation;
                $product_translation->product_id = $product->id;
                $product_translation->name = $request->name;
                $product_translation->unit = $request->unit ?? 'pc';
                $product_translation->description = $request->description;
                $product_translation->lang = env('DEFAULT_LANGUAGE', 'en');
                $product_translation->save();

                DB::commit();
                flash(translate('Wholesale product has been created successfully and is pending approval'))->success();
                return redirect()->route('seller.wholesale_products_list');
            }
        } catch (\Exception $e) {
            DB::rollback();
            flash(translate('Something went wrong: ') . $e->getMessage())->error();
            return back();
        }

        flash(translate('Something went wrong'))->error();
        return back();
    }

    /**
     * Show seller wholesale product edit form
     */
    public function product_edit_seller($id)
    {
        $product = Product::where('wholesale_product', 1)
            ->where('user_id', Auth::id())
            ->findOrFail($id);
        $categories = Category::where('level', 0)->get();
        $brands = Brand::all();
        $taxes = Tax::all();
        return view('seller.wholesale_products.edit', compact('product', 'categories', 'brands', 'taxes'));
    }

    /**
     * Update seller wholesale product
     */
    public function product_update_seller(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'unit_price' => 'required|numeric|min:0',
            'min_qty' => 'required|integer|min:1',
        ]);

        DB::beginTransaction();
        
        try {
            $product = Product::where('wholesale_product', 1)
                ->where('user_id', Auth::id())
                ->findOrFail($id);
            
            $product->name = $request->name;
            $product->category_id = $request->category_id;
            $product->brand_id = $request->brand_id;
            $product->photos = $request->photos;
            $product->thumbnail_img = $request->thumbnail_img;
            $product->description = $request->description;
            $product->unit_price = $request->unit_price;
            $product->min_qty = $request->min_qty;
            $product->unit = $request->unit ?? 'pc';
            $product->meta_title = $request->meta_title;
            $product->meta_description = $request->meta_description;

            if ($product->save()) {
                // Update stock
                $stock = ProductStock::where('product_id', $product->id)->first();
                if ($stock) {
                    $stock->price = $request->unit_price;
                    $stock->qty = $request->quantity ?? $stock->qty;
                    $stock->save();
                }

                // Update taxes
                ProductTax::where('product_id', $product->id)->delete();
                if ($request->has('tax_id') && $request->tax_id) {
                    foreach ($request->tax_id as $key => $tax_id) {
                        $product_tax = new ProductTax;
                        $product_tax->tax_id = $tax_id;
                        $product_tax->product_id = $product->id;
                        $product_tax->tax = $request->tax[$key];
                        $product_tax->tax_type = $request->tax_type[$key];
                        $product_tax->save();
                    }
                }

                // Update translation
                $product_translation = ProductTranslation::where('product_id', $product->id)
                    ->where('lang', env('DEFAULT_LANGUAGE', 'en'))
                    ->first();
                
                if ($product_translation) {
                    $product_translation->name = $request->name;
                    $product_translation->unit = $request->unit ?? 'pc';
                    $product_translation->description = $request->description;
                    $product_translation->save();
                }

                DB::commit();
                flash(translate('Wholesale product has been updated successfully'))->success();
                return redirect()->route('seller.wholesale_products_list');
            }
        } catch (\Exception $e) {
            DB::rollback();
            flash(translate('Something went wrong: ') . $e->getMessage())->error();
            return back();
        }

        flash(translate('Something went wrong'))->error();
        return back();
    }

    /**
     * Delete seller wholesale product
     */
    public function product_destroy_seller($id)
    {
        $product = Product::where('wholesale_product', 1)
            ->where('user_id', Auth::id())
            ->findOrFail($id);
        
        DB::beginTransaction();
        
        try {
            // Delete related data
            ProductStock::where('product_id', $product->id)->delete();
            ProductTax::where('product_id', $product->id)->delete();
            ProductTranslation::where('product_id', $product->id)->delete();

            if ($product->delete()) {
                DB::commit();
                flash(translate('Wholesale product has been deleted successfully'))->success();
            } else {
                DB::rollback();
                flash(translate('Something went wrong'))->error();
            }
        } catch (\Exception $e) {
            DB::rollback();
            flash(translate('Something went wrong: ') . $e->getMessage())->error();
        }

        return back();
    }

    // API Methods (for API routes)

    /**
     * API: Get wholesale products list
     */
    public function wholesale_products()
    {
        $products = Product::where('wholesale_product', 1)
            ->where('user_id', auth()->id())
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return response()->json([
            'success' => true,
            'data' => $products
        ]);
    }

    /**
     * API: Store wholesale product
     */
    public function product_store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'unit_price' => 'required|numeric|min:0',
            'min_qty' => 'required|integer|min:1',
        ]);

        // Check seller package limits
        if (addon_is_activated('seller_subscription')) {
            $shop = auth()->user()->shop;
            if ($shop && $shop->seller_package) {
                if ($shop->seller_package->product_upload_limit <= auth()->user()->products()->count()) {
                    return response()->json([
                        'success' => false,
                        'message' => translate('Upload limit has been reached. Please upgrade your package.')
                    ], 400);
                }
            }
        }

        DB::beginTransaction();
        
        try {
            $product = new Product;
            $product->name = $request->name;
            $product->category_id = $request->category_id;
            $product->brand_id = $request->brand_id;
            $product->photos = $request->photos;
            $product->thumbnail_img = $request->thumbnail_img;
            $product->description = $request->description;
            $product->unit_price = $request->unit_price;
            $product->wholesale_product = 1;
            $product->min_qty = $request->min_qty;
            $product->slug = Str::slug($request->name) . '-' . Str::random(5);
            $product->user_id = auth()->id();
            $product->added_by = 'seller';
            $product->published = 0; // Needs approval
            $product->unit = $request->unit ?? 'pc';

            if ($product->save()) {
                // Create stock entry
                $stock = new ProductStock;
                $stock->product_id = $product->id;
                $stock->variant = '';
                $stock->sku = $product->slug;
                $stock->price = $request->unit_price;
                $stock->qty = $request->quantity ?? 1;
                $stock->save();

                // Create product translation
                $product_translation = new ProductTranslation;
                $product_translation->product_id = $product->id;
                $product_translation->name = $request->name;
                $product_translation->unit = $request->unit ?? 'pc';
                $product_translation->description = $request->description;
                $product_translation->lang = env('DEFAULT_LANGUAGE', 'en');
                $product_translation->save();

                DB::commit();
                return response()->json([
                    'success' => true,
                    'message' => translate('Product successfully created.'),
                    'data' => $product
                ]);
            }
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => translate('Something went wrong: ') . $e->getMessage()
            ], 500);
        }

        return response()->json([
            'success' => false,
            'message' => translate('Something went wrong')
        ], 500);
    }

    /**
     * API: Get wholesale product for editing
     */
    public function product_edit($id)
    {
        $product = Product::where('wholesale_product', 1)
            ->where('user_id', auth()->id())
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $product
        ]);
    }

    /**
     * API: Update wholesale product
     */
    public function product_update(Request $request, $id)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
            'unit_price' => 'required|numeric|min:0',
            'min_qty' => 'required|integer|min:1',
        ]);

        DB::beginTransaction();
        
        try {
            $product = Product::where('wholesale_product', 1)
                ->where('user_id', auth()->id())
                ->findOrFail($id);
            
            $product->name = $request->name;
            $product->category_id = $request->category_id;
            $product->brand_id = $request->brand_id;
            $product->photos = $request->photos;
            $product->thumbnail_img = $request->thumbnail_img;
            $product->description = $request->description;
            $product->unit_price = $request->unit_price;
            $product->min_qty = $request->min_qty;
            $product->unit = $request->unit ?? 'pc';

            if ($product->save()) {
                // Update stock
                $stock = ProductStock::where('product_id', $product->id)->first();
                if ($stock) {
                    $stock->price = $request->unit_price;
                    $stock->qty = $request->quantity ?? $stock->qty;
                    $stock->save();
                }

                // Update translation
                $product_translation = ProductTranslation::where('product_id', $product->id)
                    ->where('lang', env('DEFAULT_LANGUAGE', 'en'))
                    ->first();
                
                if ($product_translation) {
                    $product_translation->name = $request->name;
                    $product_translation->unit = $request->unit ?? 'pc';
                    $product_translation->description = $request->description;
                    $product_translation->save();
                }

                DB::commit();
                return response()->json([
                    'success' => true,
                    'message' => translate('Product has been updated successfully'),
                    'data' => $product
                ]);
            }
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => translate('Something went wrong: ') . $e->getMessage()
            ], 500);
        }

        return response()->json([
            'success' => false,
            'message' => translate('Something went wrong')
        ], 500);
    }

    /**
     * API: Delete wholesale product
     */
    public function product_destroy($id)
    {
        $product = Product::where('wholesale_product', 1)
            ->where('user_id', auth()->id())
            ->findOrFail($id);
        
        DB::beginTransaction();
        
        try {
            // Delete related data
            ProductStock::where('product_id', $product->id)->delete();
            ProductTax::where('product_id', $product->id)->delete();
            ProductTranslation::where('product_id', $product->id)->delete();

            if ($product->delete()) {
                DB::commit();
                return response()->json([
                    'success' => true,
                    'message' => translate('Product successfully deleted.')
                ]);
            } else {
                DB::rollback();
                return response()->json([
                    'success' => false,
                    'message' => translate('Something went wrong')
                ], 500);
            }
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => translate('Something went wrong: ') . $e->getMessage()
            ], 500);
        }
    }
} 