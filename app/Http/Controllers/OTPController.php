<?php

namespace App\Http\Controllers;

use App\Models\OtpConfiguration;
use App\Models\BusinessSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class OTPController extends Controller
{
    /**
     * Show OTP configuration page
     */
    public function configure_index()
    {
        return view('backend.otp_systems.configurations.index');
    }

    /**
     * Show OTP credentials configuration page
     */
    public function credentials_index()
    {
        return view('backend.otp_systems.configurations.credentials');
    }

    /**
     * Update OTP activation settings
     */
    public function updateActivationSettings(Request $request)
    {
        $request->validate([
            'otp_system' => 'nullable|in:0,1'
        ]);

        $otp_system = $request->has('otp_system') ? 1 : 0;

        $business_setting = BusinessSetting::where('type', 'otp_system')->first();
        if ($business_setting) {
            $business_setting->value = $otp_system;
            $business_setting->save();
        } else {
            $business_setting = new BusinessSetting;
            $business_setting->type = 'otp_system';
            $business_setting->value = $otp_system;
            $business_setting->save();
        }

        // Clear cache to reflect changes
        try {
            Artisan::call('cache:clear');
        } catch (\Exception $e) {
            // Cache clearing failed, but continue
        }

        flash(translate('OTP system settings updated successfully'))->success();
        return redirect()->back();
    }

    /**
     * Update OTP service credentials
     */
    public function update_credentials(Request $request)
    {
        $request->validate([
            'otp_method' => 'required|in:twilio,nexmo,ssl_wireless,fast2sms,mimo',
        ]);

        // Update OTP method
        $business_setting = BusinessSetting::where('type', 'otp_method')->first();
        if ($business_setting) {
            $business_setting->value = $request->otp_method;
            $business_setting->save();
        } else {
            $business_setting = new BusinessSetting;
            $business_setting->type = 'otp_method';
            $business_setting->value = $request->otp_method;
            $business_setting->save();
        }

        // Update method-specific credentials
        switch ($request->otp_method) {
            case 'twilio':
                $this->updateTwilioCredentials($request);
                break;
            case 'nexmo':
                $this->updateNexmoCredentials($request);
                break;
            case 'ssl_wireless':
                $this->updateSslWirelessCredentials($request);
                break;
            case 'fast2sms':
                $this->updateFast2smsCredentials($request);
                break;
            case 'mimo':
                $this->updateMimoCredentials($request);
                break;
        }

        flash(translate('OTP credentials updated successfully'))->success();
        return redirect()->back();
    }

    /**
     * Update Twilio credentials
     */
    private function updateTwilioCredentials(Request $request)
    {
        $credentials = [
            'twilio_sid' => $request->twilio_sid,
            'twilio_auth_token' => $request->twilio_auth_token,
            'twilio_phone' => $request->twilio_phone,
        ];

        foreach ($credentials as $type => $value) {
            $business_setting = BusinessSetting::where('type', $type)->first();
            if ($business_setting) {
                $business_setting->value = $value;
                $business_setting->save();
            } else {
                $business_setting = new BusinessSetting;
                $business_setting->type = $type;
                $business_setting->value = $value;
                $business_setting->save();
            }
        }
    }

    /**
     * Update Nexmo credentials
     */
    private function updateNexmoCredentials(Request $request)
    {
        $credentials = [
            'nexmo_key' => $request->nexmo_key,
            'nexmo_secret' => $request->nexmo_secret,
            'nexmo_from' => $request->nexmo_from,
        ];

        foreach ($credentials as $type => $value) {
            $business_setting = BusinessSetting::where('type', $type)->first();
            if ($business_setting) {
                $business_setting->value = $value;
                $business_setting->save();
            } else {
                $business_setting = new BusinessSetting;
                $business_setting->type = $type;
                $business_setting->value = $value;
                $business_setting->save();
            }
        }
    }

    /**
     * Update SSL Wireless credentials
     */
    private function updateSslWirelessCredentials(Request $request)
    {
        $credentials = [
            'ssl_wireless_user' => $request->ssl_wireless_user,
            'ssl_wireless_pass' => $request->ssl_wireless_pass,
            'ssl_wireless_sid' => $request->ssl_wireless_sid,
        ];

        foreach ($credentials as $type => $value) {
            $business_setting = BusinessSetting::where('type', $type)->first();
            if ($business_setting) {
                $business_setting->value = $value;
                $business_setting->save();
            } else {
                $business_setting = new BusinessSetting;
                $business_setting->type = $type;
                $business_setting->value = $value;
                $business_setting->save();
            }
        }
    }

    /**
     * Update Fast2SMS credentials
     */
    private function updateFast2smsCredentials(Request $request)
    {
        $credentials = [
            'fast2sms_api_key' => $request->fast2sms_api_key,
        ];

        foreach ($credentials as $type => $value) {
            $business_setting = BusinessSetting::where('type', $type)->first();
            if ($business_setting) {
                $business_setting->value = $value;
                $business_setting->save();
            } else {
                $business_setting = new BusinessSetting;
                $business_setting->type = $type;
                $business_setting->value = $value;
                $business_setting->save();
            }
        }
    }

    /**
     * Update Mimo credentials
     */
    private function updateMimoCredentials(Request $request)
    {
        $credentials = [
            'mimo_username' => $request->mimo_username,
            'mimo_password' => $request->mimo_password,
            'mimo_sender_id' => $request->mimo_sender_id,
        ];

        foreach ($credentials as $type => $value) {
            $business_setting = BusinessSetting::where('type', $type)->first();
            if ($business_setting) {
                $business_setting->value = $value;
                $business_setting->save();
            } else {
                $business_setting = new BusinessSetting;
                $business_setting->type = $type;
                $business_setting->value = $value;
                $business_setting->save();
            }
        }
    }
} 