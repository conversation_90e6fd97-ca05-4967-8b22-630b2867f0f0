networks:
  frontend:
    driver: ${NETWORKS_DRIVER}
  backend:
    driver: ${NETWORKS_DRIVER}
volumes:
  mysql:
    driver: ${VOLUMES_DRIVER}
  percona:
    driver: ${VOLUMES_DRIVER}
  mssql:
    driver: ${VOLUMES_DRIVER}
  postgres:
    driver: ${VOLUMES_DRIVER}
  memcached:
    driver: ${VOLUMES_DRIVER}
  redis:
    driver: ${VOLUMES_DRIVER}
  neo4j:
    driver: ${VOLUMES_DRIVER}
  mariadb:
    driver: ${VOLUMES_DRIVER}
  mongo:
    driver: ${VOLUMES_DRIVER}
  minio:
    driver: ${VOLUMES_DRIVER}
  rethinkdb:
    driver: ${VOLUMES_DRIVER}
  phpmyadmin:
    driver: ${VOLUMES_DRIVER}
  adminer:
    driver: ${VOLUMES_DRIVER}
  aerospike:
    driver: ${VOLUMES_DRIVER}
  caddy:
    driver: ${VOLUMES_DRIVER}
  meilisearch:
    driver: ${VOLUMES_DRIVER}
  elasticsearch:
    driver: ${VOLUMES_DRIVER}
  mosquitto:
    driver: ${VOLUMES_DRIVER}
  confluence:
    driver: ${VOLUMES_DRIVER}
  sonarqube:
    driver: ${VOLUMES_DRIVER}
  cassandra:
    driver: ${VOLUMES_DRIVER}
  graylog:
    driver: ${VOLUMES_DRIVER}
  docker-in-docker:
    driver: ${VOLUMES_DRIVER}
  react:
    driver: ${VOLUMES_DRIVER}

services:

### Workspace Utilities ##################################
    workspace:
      restart: always
      build:
        context: ./workspace
        args:
          - CHANGE_SOURCE=${CHANGE_SOURCE}
          - SHELL_OH_MY_ZSH=${SHELL_OH_MY_ZSH}
          - SHELL_OH_MY_ZSH_AUTOSUGESTIONS=${SHELL_OH_MY_ZSH_AUTOSUGESTIONS}
          - SHELL_OH_MY_ZSH_ALIASES=${SHELL_OH_MY_ZSH_ALIASES}
          - BASE_IMAGE_TAG_PREFIX=${WORKSPACE_BASE_IMAGE_TAG_PREFIX}
          - LARADOCK_PHP_VERSION=${PHP_VERSION}
          - LARADOCK_PHALCON_VERSION=${PHALCON_VERSION}
          - INSTALL_SUBVERSION=${WORKSPACE_INSTALL_SUBVERSION}
          - INSTALL_BZ2=${WORKSPACE_INSTALL_BZ2}
          - INSTALL_GMP=${WORKSPACE_INSTALL_GMP}
          - INSTALL_GNUPG=${WORKSPACE_INSTALL_GNUPG}
          - INSTALL_XDEBUG=${WORKSPACE_INSTALL_XDEBUG}
          - XDEBUG_PORT=${WORKSPACE_XDEBUG_PORT}
          - INSTALL_PCOV=${WORKSPACE_INSTALL_PCOV}
          - INSTALL_PHPDBG=${WORKSPACE_INSTALL_PHPDBG}
          - INSTALL_BLACKFIRE=${INSTALL_BLACKFIRE}
          - INSTALL_SSH2=${WORKSPACE_INSTALL_SSH2}
          - INSTALL_SOAP=${WORKSPACE_INSTALL_SOAP}
          - INSTALL_XSL=${WORKSPACE_INSTALL_XSL}
          - INSTALL_LDAP=${WORKSPACE_INSTALL_LDAP}
          - INSTALL_SMB=${WORKSPACE_INSTALL_SMB}
          - INSTALL_IMAP=${WORKSPACE_INSTALL_IMAP}
          - INSTALL_MONGO=${WORKSPACE_INSTALL_MONGO}
          - INSTALL_AMQP=${WORKSPACE_INSTALL_AMQP}
          - INSTALL_CASSANDRA=${WORKSPACE_INSTALL_CASSANDRA}
          - INSTALL_ZMQ=${WORKSPACE_INSTALL_ZMQ}
          - INSTALL_GEARMAN=${WORKSPACE_INSTALL_GEARMAN}
          - INSTALL_PHPREDIS=${WORKSPACE_INSTALL_PHPREDIS}
          - INSTALL_MSSQL=${WORKSPACE_INSTALL_MSSQL}
          - NVM_NODEJS_ORG_MIRROR=${WORKSPACE_NVM_NODEJS_ORG_MIRROR}
          - INSTALL_NODE=${WORKSPACE_INSTALL_NODE}
          - NPM_REGISTRY=${WORKSPACE_NPM_REGISTRY}
          - NPM_FETCH_RETRIES=${WORKSPACE_NPM_FETCH_RETRIES}
          - NPM_FETCH_RETRY_FACTOR=${WORKSPACE_NPM_FETCH_RETRY_FACTOR}
          - NPM_FETCH_RETRY_MINTIMEOUT=${WORKSPACE_NPM_FETCH_RETRY_MINTIMEOUT}
          - NPM_FETCH_RETRY_MAXTIMEOUT=${WORKSPACE_NPM_FETCH_RETRY_MAXTIMEOUT}
          - INSTALL_PNPM=${WORKSPACE_INSTALL_PNPM}
          - INSTALL_YARN=${WORKSPACE_INSTALL_YARN}
          - INSTALL_NPM_GULP=${WORKSPACE_INSTALL_NPM_GULP}
          - INSTALL_NPM_BOWER=${WORKSPACE_INSTALL_NPM_BOWER}
          - INSTALL_NPM_VUE_CLI=${WORKSPACE_INSTALL_NPM_VUE_CLI}
          - INSTALL_NPM_ANGULAR_CLI=${WORKSPACE_INSTALL_NPM_ANGULAR_CLI}
          - INSTALL_NPM_CHECK_UPDATES_CLI=${WORKSPACE_INSTALL_NPM_CHECK_UPDATES_CLI}
          - INSTALL_DRUSH=${WORKSPACE_INSTALL_DRUSH}
          - INSTALL_WP_CLI=${WORKSPACE_INSTALL_WP_CLI}
          - INSTALL_DRUPAL_CONSOLE=${WORKSPACE_INSTALL_DRUPAL_CONSOLE}
          - INSTALL_AEROSPIKE=${WORKSPACE_INSTALL_AEROSPIKE}
          - INSTALL_OCI8=${WORKSPACE_INSTALL_OCI8}
          - INSTALL_V8JS=${WORKSPACE_INSTALL_V8JS}
          - COMPOSER_GLOBAL_INSTALL=${WORKSPACE_COMPOSER_GLOBAL_INSTALL}
          - COMPOSER_VERSION=${WORKSPACE_COMPOSER_VERSION}
          - COMPOSER_AUTH_JSON=${WORKSPACE_COMPOSER_AUTH_JSON}
          - COMPOSER_REPO_PACKAGIST=${WORKSPACE_COMPOSER_REPO_PACKAGIST}
          - INSTALL_WORKSPACE_SSH=${WORKSPACE_INSTALL_WORKSPACE_SSH}
          - INSTALL_LARAVEL_ENVOY=${WORKSPACE_INSTALL_LARAVEL_ENVOY}
          - INSTALL_LARAVEL_INSTALLER=${WORKSPACE_INSTALL_LARAVEL_INSTALLER}
          - INSTALL_XLSWRITER=${WORKSPACE_INSTALL_XLSWRITER}
          - INSTALL_DEPLOYER=${WORKSPACE_INSTALL_DEPLOYER}
          - INSTALL_PRESTISSIMO=${WORKSPACE_INSTALL_PRESTISSIMO}
          - INSTALL_LINUXBREW=${WORKSPACE_INSTALL_LINUXBREW}
          - INSTALL_MC=${WORKSPACE_INSTALL_MC}
          - INSTALL_SYMFONY=${WORKSPACE_INSTALL_SYMFONY}
          - INSTALL_PYTHON=${WORKSPACE_INSTALL_PYTHON}
          - INSTALL_PYTHON3=${WORKSPACE_INSTALL_PYTHON3}
          - INSTALL_IMAGE_OPTIMIZERS=${WORKSPACE_INSTALL_IMAGE_OPTIMIZERS}
          - INSTALL_IMAGEMAGICK=${WORKSPACE_INSTALL_IMAGEMAGICK}
          - INSTALL_TERRAFORM=${WORKSPACE_INSTALL_TERRAFORM}
          - INSTALL_DUSK_DEPS=${WORKSPACE_INSTALL_DUSK_DEPS}
          - INSTALL_PG_CLIENT=${WORKSPACE_INSTALL_PG_CLIENT}
          - PG_CLIENT_VERSION=${POSTGRES_CLIENT_VERSION}
          - INSTALL_PHALCON=${WORKSPACE_INSTALL_PHALCON}
          - INSTALL_SWOOLE=${WORKSPACE_INSTALL_SWOOLE}
          - INSTALL_TAINT=${WORKSPACE_INSTALL_TAINT}
          - INSTALL_LIBPNG=${WORKSPACE_INSTALL_LIBPNG}
          - INSTALL_GRAPHVIZ=${WORKSPACE_INSTALL_GRAPHVIZ}
          - INSTALL_IONCUBE=${WORKSPACE_INSTALL_IONCUBE}
          - INSTALL_APCU=${WORKSPACE_INSTALL_APCU}
          - INSTALL_MYSQL_CLIENT=${WORKSPACE_INSTALL_MYSQL_CLIENT}
          - INSTALL_PING=${WORKSPACE_INSTALL_PING}
          - INSTALL_SSHPASS=${WORKSPACE_INSTALL_SSHPASS}
          - INSTALL_INOTIFY=${WORKSPACE_INSTALL_INOTIFY}
          - INSTALL_FSWATCH=${WORKSPACE_INSTALL_FSWATCH}
          - INSTALL_AST=${WORKSPACE_INSTALL_AST}
          - INSTALL_YAML=${WORKSPACE_INSTALL_YAML}
          - INSTALL_RDKAFKA=${WORKSPACE_INSTALL_RDKAFKA}
          - INSTALL_MAILPARSE=${WORKSPACE_INSTALL_MAILPARSE}
          - INSTALL_GIT_PROMPT=${WORKSPACE_INSTALL_GIT_PROMPT}
          - INSTALL_XMLRPC=${WORKSPACE_INSTALL_XMLRPC}
          - PUID=${WORKSPACE_PUID}
          - PGID=${WORKSPACE_PGID}
          - CHROME_DRIVER_VERSION=${WORKSPACE_CHROME_DRIVER_VERSION}
          - NODE_VERSION=${WORKSPACE_NODE_VERSION}
          - YARN_VERSION=${WORKSPACE_YARN_VERSION}
          - DRUSH_VERSION=${WORKSPACE_DRUSH_VERSION}
          - AST_VERSION=${WORKSPACE_AST_VERSION}
          - IMAGEMAGICK_VERSION=${WORKSPACE_IMAGEMAGICK_VERSION}
          - TZ=${WORKSPACE_TIMEZONE}
          - BLACKFIRE_CLIENT_ID=${BLACKFIRE_CLIENT_ID}
          - BLACKFIRE_CLIENT_TOKEN=${BLACKFIRE_CLIENT_TOKEN}
          - INSTALL_POWERLINE=${WORKSPACE_INSTALL_POWERLINE}
          - INSTALL_SUPERVISOR=${WORKSPACE_INSTALL_SUPERVISOR}
          - INSTALL_FFMPEG=${WORKSPACE_INSTALL_FFMPEG}
          - INSTALL_AUDIOWAVEFORM=${WORKSPACE_INSTALL_AUDIOWAVEFORM}
          - INSTALL_WKHTMLTOPDF=${WORKSPACE_INSTALL_WKHTMLTOPDF}
          - WKHTMLTOPDF_VERSION=${WORKSPACE_WKHTMLTOPDF_VERSION}
          - INSTALL_GNU_PARALLEL=${WORKSPACE_INSTALL_GNU_PARALLEL}
          - INSTALL_LNAV=${WORKSPACE_INSTALL_LNAV}
          - INSTALL_PROTOC=${WORKSPACE_INSTALL_PROTOC}
          - INSTALL_PHPDECIMAL=${WORKSPACE_INSTALL_PHPDECIMAL}
          - INSTALL_ZOOKEEPER=${WORKSPACE_INSTALL_ZOOKEEPER}
          - INSTALL_SSDB=${WORKSPACE_INSTALL_SSDB}
          - INSTALL_TRADER=${WORKSPACE_INSTALL_TRADER}
          - PROTOC_VERSION=${WORKSPACE_PROTOC_VERSION}
          - INSTALL_DOCKER_CLIENT=${WORKSPACE_INSTALL_DOCKER_CLIENT}
          - INSTALL_MEMCACHED=${WORKSPACE_INSTALL_MEMCACHED}
          - INSTALL_EVENT=${WORKSPACE_INSTALL_EVENT}
          - INSTALL_DNSUTILS=${WORKSPACE_INSTALL_DNSUTILS}
          - INSTALL_POPPLER_UTILS=${WORKSPACE_INSTALL_POPPLER_UTILS}
          - INSTALL_JDK=${WORKSPACE_INSTALL_JDK}
          - INSTALL_GITHUB_CLI=${WORKSPACE_INSTALL_GITHUB_CLI}
          - ORACLE_INSTANT_CLIENT_MIRROR=${ORACLE_INSTANT_CLIENT_MIRROR}
          - ORACLE_INSTANT_CLIENT_ARCH=${ORACLE_INSTANT_CLIENT_ARCH}
          - ORACLE_INSTANT_CLIENT_MAJOR=${ORACLE_INSTANT_CLIENT_MAJOR}
          - ORACLE_INSTANT_CLIENT_MINOR=${ORACLE_INSTANT_CLIENT_MINOR}
          - http_proxy
          - https_proxy
          - no_proxy
      volumes:
        - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
        - docker-in-docker:/certs/client
        - ./php-worker/supervisord.d:/etc/supervisord.d
      extra_hosts:
        - "dockerhost:${DOCKER_HOST_IP}"
      ports:
        - "${WORKSPACE_SSH_PORT}:22"
        - "${WORKSPACE_BROWSERSYNC_HOST_PORT}:3000"
        - "${WORKSPACE_BROWSERSYNC_UI_HOST_PORT}:3001"
        - "${WORKSPACE_VUE_CLI_SERVE_HOST_PORT}:8080"
        - "${WORKSPACE_VUE_CLI_UI_HOST_PORT}:8000"
        - "${WORKSPACE_ANGULAR_CLI_SERVE_HOST_PORT}:4200"
        - "${WORKSPACE_VITE_PORT}:5173"
      tty: true
      environment:
        - PHP_IDE_CONFIG=${PHP_IDE_CONFIG}
        - DOCKER_HOST=tcp://docker-in-docker:2376
        - DOCKER_TLS_VERIFY=1
        - DOCKER_TLS_CERTDIR=/certs
        - DOCKER_CERT_PATH=/certs/client
        - CHOKIDAR_USEPOLLING=true
      networks:
        - frontend
        - backend
      links:
        - docker-in-docker

### PHP-FPM ##############################################
    php-fpm:
      restart: always
      build:
        context: ./php-fpm
        args:
          - CHANGE_SOURCE=${CHANGE_SOURCE}
          - BASE_IMAGE_TAG_PREFIX=${PHP_FPM_BASE_IMAGE_TAG_PREFIX}
          - LARADOCK_PHP_VERSION=${PHP_VERSION}
          - LARADOCK_PHALCON_VERSION=${PHALCON_VERSION}
          - INSTALL_BZ2=${PHP_FPM_INSTALL_BZ2}
          - INSTALL_ENCHANT=${PHP_FPM_INSTALL_ENCHANT}
          - INSTALL_GMP=${PHP_FPM_INSTALL_GMP}
          - INSTALL_GNUPG=${PHP_FPM_INSTALL_GNUPG}
          - INSTALL_XDEBUG=${PHP_FPM_INSTALL_XDEBUG}
          - XDEBUG_PORT=${PHP_FPM_XDEBUG_PORT}
          - INSTALL_PCOV=${PHP_FPM_INSTALL_PCOV}
          - INSTALL_PHPDBG=${PHP_FPM_INSTALL_PHPDBG}
          - INSTALL_BLACKFIRE=${INSTALL_BLACKFIRE}
          - INSTALL_SSH2=${PHP_FPM_INSTALL_SSH2}
          - INSTALL_SOAP=${PHP_FPM_INSTALL_SOAP}
          - INSTALL_XSL=${PHP_FPM_INSTALL_XSL}
          - INSTALL_SMB=${PHP_FPM_INSTALL_SMB}
          - INSTALL_IMAP=${PHP_FPM_INSTALL_IMAP}
          - INSTALL_MONGO=${PHP_FPM_INSTALL_MONGO}
          - INSTALL_AMQP=${PHP_FPM_INSTALL_AMQP}
          - INSTALL_CASSANDRA=${PHP_FPM_INSTALL_CASSANDRA}
          - INSTALL_ZMQ=${PHP_FPM_INSTALL_ZMQ}
          - INSTALL_GEARMAN=${PHP_FPM_INSTALL_GEARMAN}
          - INSTALL_MSSQL=${PHP_FPM_INSTALL_MSSQL}
          - INSTALL_BCMATH=${PHP_FPM_INSTALL_BCMATH}
          - INSTALL_PHPREDIS=${PHP_FPM_INSTALL_PHPREDIS}
          - INSTALL_MEMCACHED=${PHP_FPM_INSTALL_MEMCACHED}
          - INSTALL_OPCACHE=${PHP_FPM_INSTALL_OPCACHE}
          - INSTALL_EXIF=${PHP_FPM_INSTALL_EXIF}
          - INSTALL_AEROSPIKE=${PHP_FPM_INSTALL_AEROSPIKE}
          - INSTALL_OCI8=${PHP_FPM_INSTALL_OCI8}
          - INSTALL_MYSQLI=${PHP_FPM_INSTALL_MYSQLI}
          - INSTALL_PGSQL=${PHP_FPM_INSTALL_PGSQL}
          - INSTALL_PG_CLIENT=${PHP_FPM_INSTALL_PG_CLIENT}
          - PG_CLIENT_VERSION=${POSTGRES_CLIENT_VERSION}
          - INSTALL_POSTGIS=${PHP_FPM_INSTALL_POSTGIS}
          - INSTALL_INTL=${PHP_FPM_INSTALL_INTL}
          - INSTALL_GHOSTSCRIPT=${PHP_FPM_INSTALL_GHOSTSCRIPT}
          - INSTALL_LDAP=${PHP_FPM_INSTALL_LDAP}
          - INSTALL_PHALCON=${PHP_FPM_INSTALL_PHALCON}
          - INSTALL_SWOOLE=${PHP_FPM_INSTALL_SWOOLE}
          - INSTALL_TAINT=${PHP_FPM_INSTALL_TAINT}
          - INSTALL_IMAGE_OPTIMIZERS=${PHP_FPM_INSTALL_IMAGE_OPTIMIZERS}
          - INSTALL_IMAGEMAGICK=${PHP_FPM_INSTALL_IMAGEMAGICK}
          - INSTALL_CALENDAR=${PHP_FPM_INSTALL_CALENDAR}
          - INSTALL_XLSWRITER=${PHP_FPM_INSTALL_XLSWRITER}
          - INSTALL_FAKETIME=${PHP_FPM_INSTALL_FAKETIME}
          - INSTALL_IONCUBE=${PHP_FPM_INSTALL_IONCUBE}
          - INSTALL_APCU=${PHP_FPM_INSTALL_APCU}
          - INSTALL_CACHETOOL=${PHP_FPM_INSTALL_CACHETOOL}
          - INSTALL_YAML=${PHP_FPM_INSTALL_YAML}
          - INSTALL_RDKAFKA=${PHP_FPM_INSTALL_RDKAFKA}
          - INSTALL_GETTEXT=${PHP_FPM_INSTALL_GETTEXT}
          - INSTALL_ADDITIONAL_LOCALES=${PHP_FPM_INSTALL_ADDITIONAL_LOCALES}
          - INSTALL_MYSQL_CLIENT=${PHP_FPM_INSTALL_MYSQL_CLIENT}
          - INSTALL_PING=${PHP_FPM_INSTALL_PING}
          - INSTALL_SSHPASS=${PHP_FPM_INSTALL_SSHPASS}
          - INSTALL_MAILPARSE=${PHP_FPM_INSTALL_MAILPARSE}
          - INSTALL_PCNTL=${PHP_FPM_INSTALL_PCNTL}
          - ADDITIONAL_LOCALES=${PHP_FPM_ADDITIONAL_LOCALES}
          - INSTALL_FFMPEG=${PHP_FPM_FFMPEG}
          - INSTALL_AUDIOWAVEFORM=${PHP_FPM_AUDIOWAVEFORM}
          - INSTALL_WKHTMLTOPDF=${PHP_FPM_INSTALL_WKHTMLTOPDF}
          - WKHTMLTOPDF_VERSION=${PHP_FPM_WKHTMLTOPDF_VERSION}
          - INSTALL_XHPROF=${PHP_FPM_INSTALL_XHPROF}
          - INSTALL_XMLRPC=${PHP_FPM_INSTALL_XMLRPC}
          - INSTALL_PHPDECIMAL=${PHP_FPM_INSTALL_PHPDECIMAL}
          - INSTALL_ZOOKEEPER=${PHP_FPM_INSTALL_ZOOKEEPER}
          - INSTALL_SSDB=${PHP_FPM_INSTALL_SSDB}
          - INSTALL_TRADER=${PHP_FPM_INSTALL_TRADER}
          - INSTALL_EVENT=${PHP_FPM_INSTALL_EVENT}
          - LEGACY_OPENSSL=${PHP_LEGACY_OPENSSL}
          - DOWNGRADE_OPENSSL_TLS_AND_SECLEVEL=${PHP_DOWNGRADE_OPENSSL_TLS_AND_SECLEVEL}
          - DOWNGRADE_OPENSSL_TLS_VERSION=${PHP_DOWNGRADE_OPENSSL_TLS_VERSION}
          - PUID=${PHP_FPM_PUID}
          - PGID=${PHP_FPM_PGID}
          - IMAGEMAGICK_VERSION=${PHP_FPM_IMAGEMAGICK_VERSION}
          - LOCALE=${PHP_FPM_DEFAULT_LOCALE}
          - NEW_RELIC=${PHP_FPM_NEW_RELIC}
          - NEW_RELIC_KEY=${PHP_FPM_NEW_RELIC_KEY}
          - NEW_RELIC_APP_NAME=${PHP_FPM_NEW_RELIC_APP_NAME}
          - INSTALL_DOCKER_CLIENT=${PHP_FPM_INSTALL_DOCKER_CLIENT}
          - INSTALL_DNSUTILS=${PHP_FPM_INSTALL_DNSUTILS}
          - INSTALL_POPPLER_UTILS=${PHP_FPM_INSTALL_POPPLER_UTILS}
          - ORACLE_INSTANT_CLIENT_MIRROR=${ORACLE_INSTANT_CLIENT_MIRROR}
          - ORACLE_INSTANT_CLIENT_ARCH=${ORACLE_INSTANT_CLIENT_ARCH}
          - ORACLE_INSTANT_CLIENT_MAJOR=${ORACLE_INSTANT_CLIENT_MAJOR}
          - ORACLE_INSTANT_CLIENT_MINOR=${ORACLE_INSTANT_CLIENT_MINOR}
          - http_proxy
          - https_proxy
          - no_proxy
      volumes:
        - ./php-fpm/php${PHP_VERSION}.ini:/usr/local/etc/php/php.ini
        - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
        - docker-in-docker:/certs/client
      expose:
        - "9000"
      extra_hosts:
        - "dockerhost:${DOCKER_HOST_IP}"
      environment:
        - PHP_IDE_CONFIG=${PHP_IDE_CONFIG}
        - DOCKER_HOST=tcp://docker-in-docker:2376
        - DOCKER_TLS_VERIFY=1
        - DOCKER_TLS_CERTDIR=/certs
        - DOCKER_CERT_PATH=/certs/client
        - FAKETIME=${PHP_FPM_FAKETIME}
        - TZ=${WORKSPACE_TIMEZONE}

      depends_on:
        - workspace
      networks:
        - backend
      links:
        - docker-in-docker

### PHP Worker ############################################
    php-worker:
      restart: always
      build:
        context: ./php-worker
        args:
          - CHANGE_SOURCE=${CHANGE_SOURCE}
          - LARADOCK_PHP_VERSION=${PHP_VERSION}
          - PHALCON_VERSION=${PHALCON_VERSION}
          - LARADOCK_PHALCON_VERSION=${PHALCON_VERSION}
          - INSTALL_BZ2=${PHP_WORKER_INSTALL_BZ2}
          - INSTALL_GD=${PHP_WORKER_INSTALL_GD}
          - INSTALL_IMAGEMAGICK=${PHP_WORKER_INSTALL_IMAGEMAGICK}
          - INSTALL_GMP=${PHP_WORKER_INSTALL_GMP}
          - INSTALL_GNUPG=${PHP_WORKER_INSTALL_GNUPG}
          - INSTALL_LDAP=${PHP_WORKER_INSTALL_LDAP}
          - INSTALL_PGSQL=${PHP_WORKER_INSTALL_PGSQL}
          - INSTALL_MONGO=${PHP_WORKER_INSTALL_MONGO}
          - INSTALL_BCMATH=${PHP_WORKER_INSTALL_BCMATH}
          - INSTALL_MEMCACHED=${PHP_WORKER_INSTALL_MEMCACHED}
          - INSTALL_OCI8=${PHP_WORKER_INSTALL_OCI8}
          - INSTALL_MSSQL=${PHP_WORKER_INSTALL_MSSQL}
          - INSTALL_PHALCON=${PHP_WORKER_INSTALL_PHALCON}
          - INSTALL_APCU=${PHP_WORKER_INSTALL_APCU}
          - INSTALL_XLSWRITER=${PHP_WORKER_INSTALL_XLSWRITER}
          - INSTALL_SOAP=${PHP_WORKER_INSTALL_SOAP}
          - INSTALL_ZIP_ARCHIVE=${PHP_WORKER_INSTALL_ZIP_ARCHIVE}
          - INSTALL_MYSQL_CLIENT=${PHP_WORKER_INSTALL_MYSQL_CLIENT}
          - INSTALL_AMQP=${PHP_WORKER_INSTALL_AMQP}
          - INSTALL_CASSANDRA=${PHP_WORKER_INSTALL_CASSANDRA}
          - INSTALL_GEARMAN=${PHP_WORKER_INSTALL_GEARMAN}
          - INSTALL_GHOSTSCRIPT=${PHP_WORKER_INSTALL_GHOSTSCRIPT}
          - INSTALL_SWOOLE=${PHP_WORKER_INSTALL_SWOOLE}
          - INSTALL_TAINT=${PHP_WORKER_INSTALL_TAINT}
          - INSTALL_FFMPEG=${PHP_WORKER_INSTALL_FFMPEG}
          - INSTALL_AUDIOWAVEFORM=${PHP_WORKER_INSTALL_AUDIOWAVEFORM}
          - INSTALL_REDIS=${PHP_WORKER_INSTALL_REDIS}
          - INSTALL_IMAP=${PHP_WORKER_INSTALL_IMAP}
          - INSTALL_XMLRPC=${PHP_WORKER_INSTALL_XMLRPC}
          - INSTALL_SSDB=${PHP_WORKER_INSTALL_SSDB}
          - INSTALL_EVENT=${PHP_WORKER_INSTALL_EVENT}
          - INSTALL_INTL=${PHP_WORKER_INSTALL_INTL}
          - INSTALL_POPPLER_UTILS=${PHP_WORKER_INSTALL_POPPLER_UTILS}
          - PUID=${PHP_WORKER_PUID}
          - PGID=${PHP_WORKER_PGID}
          - IMAGEMAGICK_VERSION=${PHP_WORKER_IMAGEMAGICK_VERSION}
          - ORACLE_INSTANT_CLIENT_MIRROR=${ORACLE_INSTANT_CLIENT_MIRROR}
          - ORACLE_INSTANT_CLIENT_ARCH=${ORACLE_INSTANT_CLIENT_ARCH}
          - ORACLE_INSTANT_CLIENT_MAJOR=${ORACLE_INSTANT_CLIENT_MAJOR}
          - ORACLE_INSTANT_CLIENT_MINOR=${ORACLE_INSTANT_CLIENT_MINOR}
      volumes:
        - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
        - ./php-worker/supervisord.d:/etc/supervisord.d
      depends_on:
        - workspace
      environment:
          - TZ=${WORKSPACE_TIMEZONE}
      extra_hosts:
        - "dockerhost:${DOCKER_HOST_IP}"
      networks:
        - backend
### Laravel Horizon ############################################
    laravel-horizon:
      restart: always
      build:
        context: ./laravel-horizon
        args:
          - CHANGE_SOURCE=${CHANGE_SOURCE}
          - LARADOCK_PHP_VERSION=${PHP_VERSION}
          - INSTALL_BZ2=${LARAVEL_HORIZON_INSTALL_BZ2}
          - INSTALL_GD=${LARAVEL_HORIZON_INSTALL_GD}
          - INSTALL_GMP=${LARAVEL_HORIZON_INSTALL_GMP}
          - INSTALL_GNUPG=${LARAVEL_HORIZON_INSTALL_GNUPG}
          - INSTALL_LDAP=${LARAVEL_HORIZON_INSTALL_LDAP}
          - INSTALL_IMAGEMAGICK=${LARAVEL_HORIZON_INSTALL_IMAGEMAGICK}
          - INSTALL_PGSQL=${PHP_FPM_INSTALL_PGSQL}
          - INSTALL_ZIP_ARCHIVE=${LARAVEL_HORIZON_INSTALL_ZIP_ARCHIVE}
          - INSTALL_BCMATH=${PHP_FPM_INSTALL_BCMATH}
          - INSTALL_MEMCACHED=${PHP_FPM_INSTALL_MEMCACHED}
          - INSTALL_SOCKETS=${LARAVEL_HORIZON_INSTALL_SOCKETS}
          - INSTALL_YAML=${LARAVEL_HORIZON_INSTALL_YAML}
          - INSTALL_CASSANDRA=${LARAVEL_HORIZON_INSTALL_CASSANDRA}
          - INSTALL_PHPREDIS=${LARAVEL_HORIZON_INSTALL_PHPREDIS}
          - INSTALL_MONGO=${LARAVEL_HORIZON_INSTALL_MONGO}
          - INSTALL_FFMPEG=${LARAVEL_HORIZON_INSTALL_FFMPEG}
          - INSTALL_AUDIOWAVEFORM=${LARAVEL_HORIZON_INSTALL_AUDIOWAVEFORM}
          - INSTALL_POPPLER_UTILS=${LARAVEL_HORIZON_INSTALL_POPPLER_UTILS}
          - PUID=${LARAVEL_HORIZON_PUID}
          - PGID=${LARAVEL_HORIZON_PGID}
          - IMAGEMAGICK_VERSION=${LARAVEL_HORIZON_IMAGEMAGICK_VERSION}
      volumes:
        - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}
        - ./laravel-horizon/supervisord.d:/etc/supervisord.d
      depends_on:
        - workspace
      extra_hosts:
        - "dockerhost:${DOCKER_HOST_IP}"
      networks:
        - backend

  ### Soketi Server ##############################################
    soketi:
      build:
        context: ./soketi
        args:
          - NODE_VERSION=${SOKETI_NODE_VERSION}
          - BASE_IMAGE_TAG_PREFIX=${SOKETI_BASE_IMAGE_TAG_PREFIX}
      volumes:
        - ./soketi/config.json:/app/bin/config.json:ro
      ports:
        - "${SOKETI_PORT}:6001"
        - "${SOKETI_METRICS_SERVER_PORT}:9601"
      networks:
        - frontend
        - backend

### NGINX Server #########################################
    nginx:
      restart: always
      build:
        context: ./nginx
        args:
          - CHANGE_SOURCE=${CHANGE_SOURCE}
          - PHP_UPSTREAM_CONTAINER=${NGINX_PHP_UPSTREAM_CONTAINER}
          - PHP_UPSTREAM_PORT=${NGINX_PHP_UPSTREAM_PORT}
          - http_proxy
          - https_proxy
          - no_proxy
      volumes:
        - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
        - ${NGINX_HOST_LOG_PATH}:/var/log/nginx
        - ${NGINX_SITES_PATH}:/etc/nginx/sites-available
        - ${NGINX_SSL_PATH}:/etc/nginx/ssl
      ports:
        - "${NGINX_HOST_HTTP_PORT}:80"
        - "${NGINX_HOST_HTTPS_PORT}:443"
        - "${VARNISH_BACKEND_PORT}:81"
      depends_on:
        - php-fpm
      networks:
        - frontend
        - backend
      extra_hosts:
        - "host.docker.internal:host-gateway"  

### OpenResty Server #########################################
    openresty:
      restart: always
      build:
        context: ./openresty
        args:
          - CHANGE_SOURCE=${CHANGE_SOURCE}
          - PHP_UPSTREAM_CONTAINER=${OPENRESTY_PHP_UPSTREAM_CONTAINER}
          - PHP_UPSTREAM_PORT=${OPENRESTY_PHP_UPSTREAM_PORT}
          - http_proxy
          - https_proxy
          - no_proxy
      volumes:
        - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
        - ${OPENRESTY_HOST_LOG_PATH}:/var/log/nginx
        - ${OPENRESTY_SITES_PATH}:/etc/nginx/sites-available
        - ${OPENRESTY_SSL_PATH}:/etc/nginx/ssl
      ports:
        - "${OPENRESTY_HOST_HTTP_PORT}:80"
        - "${OPENRESTY_HOST_HTTPS_PORT}:443"
        - "${VARNISH_BACKEND_PORT}:81"
      depends_on:
        - php-fpm
      networks:
        - frontend
        - backend

### Blackfire ########################################
    blackfire:
      image: blackfire/blackfire
      environment:
        - BLACKFIRE_SERVER_ID=${BLACKFIRE_SERVER_ID}
        - BLACKFIRE_SERVER_TOKEN=${BLACKFIRE_SERVER_TOKEN}
      depends_on:
        - php-fpm
      networks:
        - backend

### Apache Server ########################################
    apache2:
      restart: always
      build:
        context: ./apache2
        args:
          - PHP_UPSTREAM_CONTAINER=${APACHE_PHP_UPSTREAM_CONTAINER}
          - PHP_UPSTREAM_PORT=${APACHE_PHP_UPSTREAM_PORT}
          - PHP_UPSTREAM_TIMEOUT=${APACHE_PHP_UPSTREAM_TIMEOUT}
          - DOCUMENT_ROOT=${APACHE_DOCUMENT_ROOT}
          - APACHE_INSTALL_HTTP2=${APACHE_INSTALL_HTTP2}
          - APACHE_FOR_MAC_M1=${APACHE_FOR_MAC_M1}
      volumes:
        - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
        - ${APACHE_HOST_LOG_PATH}:/var/log/apache2
        - ${APACHE_SITES_PATH}:/etc/apache2/sites-available
        - ${APACHE_SSL_PATH}:/etc/apache2/ssl
      ports:
        - "${APACHE_HOST_HTTP_PORT}:80"
        - "${APACHE_HOST_HTTPS_PORT}:443"
      depends_on:
        - php-fpm
      networks:
        - frontend
        - backend

### HHVM #################################################
    hhvm:
      restart: always
      build: ./hhvm
      volumes:
        - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
      expose:
        - "9000"
      depends_on:
        - workspace
      networks:
        - frontend
        - backend

### Minio ################################################
    minio:
      restart: always
      build: ./minio
      volumes:
        - ${DATA_PATH_HOST}/minio/data:/export
        - ${DATA_PATH_HOST}/minio/config:/root/.minio
      ports:
        - "${MINIO_PORT}:9000"
        - "${MINIO_CONSOLE_PORT}:9001"
      environment:
        - MINIO_ROOT_USER=${MINIO_ROOT_USER}
        - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD}
      networks:
        - frontend
        - backend

### MySQL ################################################
    mysql:
      restart: always
      build:
        context: ./mysql
        args:
          - MYSQL_VERSION=${MYSQL_VERSION}
      environment:
        - MYSQL_DATABASE=${MYSQL_DATABASE}
        - MYSQL_USER=${MYSQL_USER}
        - MYSQL_PASSWORD=${MYSQL_PASSWORD}
        - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
        - TZ=${WORKSPACE_TIMEZONE}
      volumes:
        - ${DATA_PATH_HOST}/mysql:/var/lib/mysql
        - ${MYSQL_ENTRYPOINT_INITDB}:/docker-entrypoint-initdb.d
      ports:
        - "${MYSQL_PORT}:3306"
      networks:
        - backend

### Percona ################################################
    percona:
      restart: always
      build:
        context: ./percona
      environment:
        - MYSQL_DATABASE=${PERCONA_DATABASE}
        - MYSQL_USER=${PERCONA_USER}
        - MYSQL_PASSWORD=${PERCONA_PASSWORD}
        - MYSQL_ROOT_PASSWORD=${PERCONA_ROOT_PASSWORD}
      volumes:
        - ${DATA_PATH_HOST}/percona:/var/lib/mysql
        - ${PERCONA_ENTRYPOINT_INITDB}:/docker-entrypoint-initdb.d
      ports:
        - "${PERCONA_PORT}:3306"
      networks:
        - backend

### MSSQL ################################################
    mssql:
      restart: always
      build:
        context: ./mssql
      environment:
        - MSSQL_PID=Express
        - MSSQL_DATABASE=${MSSQL_DATABASE}
        - SA_PASSWORD=${MSSQL_PASSWORD}
        - ACCEPT_EULA=Y
      volumes:
        - mssql:/var/opt/mssql
      ports:
        - "${MSSQL_PORT}:1433"
      networks:
        - backend

### MariaDB ##############################################
    mariadb:
      restart: always
      build:
        context: ./mariadb
        args:
          - http_proxy
          - https_proxy
          - no_proxy
          - MARIADB_VERSION=${MARIADB_VERSION}
      volumes:
        - ${DATA_PATH_HOST}/mariadb:/var/lib/mysql
        - ${MARIADB_ENTRYPOINT_INITDB}:/docker-entrypoint-initdb.d
      ports:
        - "${MARIADB_PORT}:3306"
      environment:
        - TZ=${WORKSPACE_TIMEZONE}
        - MYSQL_DATABASE=${MARIADB_DATABASE}
        - MYSQL_USER=${MARIADB_USER}
        - MYSQL_PASSWORD=${MARIADB_PASSWORD}
        - MYSQL_ROOT_PASSWORD=${MARIADB_ROOT_PASSWORD}
      networks:
        - backend

### PostgreSQL ###########################################
    postgres:
      restart: always
      build:
        context: ./postgres
        args:
          - POSTGRES_VERSION=${POSTGRES_VERSION}
      volumes:
        - ${DATA_PATH_HOST}/postgres:/var/lib/postgresql/data
        - ${POSTGRES_ENTRYPOINT_INITDB}:/docker-entrypoint-initdb.d
      ports:
        - "${POSTGRES_PORT}:5432"
      environment:
        - POSTGRES_DB=${POSTGRES_DB}
        - POSTGRES_USER=${POSTGRES_USER}
        - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
        - GITLAB_POSTGRES_INIT=${GITLAB_POSTGRES_INIT}
        - GITLAB_POSTGRES_USER=${GITLAB_POSTGRES_USER}
        - GITLAB_POSTGRES_PASSWORD=${GITLAB_POSTGRES_PASSWORD}
        - GITLAB_POSTGRES_DB=${GITLAB_POSTGRES_DB}
        - KEYCLOAK_POSTGRES_INIT=${KEYCLOAK_POSTGRES_INIT}
        - KEYCLOAK_POSTGRES_USER=${KEYCLOAK_POSTGRES_USER}
        - KEYCLOAK_POSTGRES_PASSWORD=${KEYCLOAK_POSTGRES_PASSWORD}
        - KEYCLOAK_POSTGRES_DB=${KEYCLOAK_POSTGRES_DB}
        - JUPYTERHUB_POSTGRES_INIT=${JUPYTERHUB_POSTGRES_INIT}
        - JUPYTERHUB_POSTGRES_USER=${JUPYTERHUB_POSTGRES_USER}
        - JUPYTERHUB_POSTGRES_PASSWORD=${JUPYTERHUB_POSTGRES_PASSWORD}
        - JUPYTERHUB_POSTGRES_DB=${JUPYTERHUB_POSTGRES_DB}
        - SONARQUBE_POSTGRES_INIT=${SONARQUBE_POSTGRES_INIT}
        - SONARQUBE_POSTGRES_DB=${SONARQUBE_POSTGRES_DB}
        - SONARQUBE_POSTGRES_USER=${SONARQUBE_POSTGRES_USER}
        - SONARQUBE_POSTGRES_PASSWORD=${SONARQUBE_POSTGRES_PASSWORD}
        - POSTGRES_CONFLUENCE_INIT=${CONFLUENCE_POSTGRES_INIT}
        - POSTGRES_CONFLUENCE_DB=${CONFLUENCE_POSTGRES_DB}
        - POSTGRES_CONFLUENCE_USER=${CONFLUENCE_POSTGRES_USER}
        - POSTGRES_CONFLUENCE_PASSWORD=${CONFLUENCE_POSTGRES_PASSWORD}
      networks:
        - backend
### pgbackups PostgreSQL ###########################################
    pgbackups:
      image: prodrigestivill/postgres-backup-local
      restart: always
      volumes:
        - ../backup:/backups
      links:
        - postgres
      environment:
        - POSTGRES_HOST={POSTGRES_HOST}
        - POSTGRES_DB=${POSTGRES_DB}
        - POSTGRES_USER=${POSTGRES_USER}
        - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      networks:
        - backend


### PostgreSQL PostGis ###################################
    postgres-postgis:
      restart: always
      build:
        context: ./postgres-postgis
        args:
          - POSTGIS_VERSION=${POSTGIS_VERSION}
          - INSTALL_PGSQL_HTTP_FOR_POSTGIS13=${POSTGIS_INSTALL_PGSQL_HTTP_FOR_POSTGIS13}
      volumes:
        - ${DATA_PATH_HOST}/postgres:/var/lib/postgresql/data
      ports:
        - "${POSTGRES_PORT}:5432"
      environment:
        - POSTGRES_DB=${POSTGRES_DB}
        - POSTGRES_USER=${POSTGRES_USER}
        - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      networks:
        - backend

### Neo4j ################################################
    neo4j:
      restart: always
      ports:
          - '7401:7474'
          - '7402:7687'
      environment:
          - NEO4J_AUTH=none
      volumes:
          - ${DATA_PATH_HOST}/neo4j/data:/data
          - ${DATA_PATH_HOST}/neo4j/logs:/logs
      image: 'neo4j:latest'
      networks:
        - backend
### Nats ################################################
    nats:
      build: ./nats
      ports:
        - "${NATS_CLIENT_PORT}:4222"
        - "${NATS_MONITORING_PORT}:6222"
        - "${NATS_ROUTE_PORT}:8222"
      networks:
        - backend

### MongoDB ##############################################
    mongo:
      restart: always
      build: ./mongo
      ports:
        - "${MONGODB_PORT}:27017"
      environment:
        - MONGO_INITDB_ROOT_USERNAME=${MONGO_USERNAME}
        - MONGO_INITDB_ROOT_PASSWORD=${MONGO_PASSWORD}
      volumes:
        - ${DATA_PATH_HOST}/mongo:/data/db
        - ${DATA_PATH_HOST}/mongo_config:/data/configdb
      networks:
        - backend

### RethinkDB ##############################################
    rethinkdb:
      restart: always
      build: ./rethinkdb
      ports:
        - "${RETHINKDB_PORT}:8080"
      volumes:
        - ${DATA_PATH_HOST}/rethinkdb:/data/rethinkdb_data
      networks:
        - backend

  ### ClickHouse #############################################
    clickhouse:
      build:
        context: ./clickhouse
        args:
          - CLICKHOUSE_VERSION=${CLICKHOUSE_VERSION}
          - CLICKHOUSE_GOSU_VERSION=${CLICKHOUSE_GOSU_VERSION}
      environment:
        - CLICKHOUSE_USER=${CLICKHOUSE_USER}
        - CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD}
      volumes:
        - ${DATA_PATH_HOST}/clickhouse:/var/lib/clickhouse
        - ${CLICKHOUSE_CUSTOM_CONFIG}:/etc/clickhouse-server/config.xml
        - ${CLICKHOUSE_USERS_CUSTOM_CONFIG}:/etc/clickhouse-server/users.xml
        - ${CLICKHOUSE_HOST_LOG_PATH}:/var/log/clickhouse
        - ${CLICKHOUSE_ENTRYPOINT_INITDB}:/docker-entrypoint-initdb.d
      links:
        - workspace
      ports:
        - "${CLICKHOUSE_HTTP_PORT}:8123"
        - "${CLICKHOUSE_CLIENT_PORT}:9000"
        - "${CLICKHOUSE_NATIVE_PORT}:9009"
      ulimits:
        nproc: 65535
        nofile:
          soft: 262144
          hard: 262144
      networks:
#        - frontend
        - backend

  ### Redis ################################################
    redis:
      restart: always
      build: ./redis
      volumes:
        - ${DATA_PATH_HOST}/redis:/data
      command: --requirepass ${REDIS_PASSWORD}
      ports:
        - "${REDIS_PORT}:6379"
      networks:
        - backend

### Redis Cluster ##########################################
    redis-cluster:
      restart: always
      build: ./redis-cluster
      ports:
        - "${REDIS_CLUSTER_PORT_RANGE}:7000-7005"
      networks:
        - backend
### SSDB ################################################
    ssdb:
      restart: always
      build: ./ssdb
      volumes:
        - ${DATA_PATH_HOST}/ssdb:/data
      ports:
        - "${SSDB_PORT}:8888"
      networks:
        - backend

### ZooKeeper #########################################
    zookeeper:
      restart: always
      build: ./zookeeper
      volumes:
        - ${DATA_PATH_HOST}/zookeeper/data:/data
        - ${DATA_PATH_HOST}/zookeeper/datalog:/datalog
      ports:
        - "${ZOOKEEPER_PORT}:2181"
      networks:
        - backend

### Aerospike ##########################################
    aerospike:
      restart: always
      build: ./aerospike
      volumes:
        - workspace
        - ${DATA_PATH_HOST}/aerospike:/opt/aerospike/data
      ports:
        - "${AEROSPIKE_SERVICE_PORT}:3000"
        - "${AEROSPIKE_FABRIC_PORT}:3001"
        - "${AEROSPIKE_HEARTBEAT_PORT}:3002"
        - "${AEROSPIKE_INFO_PORT}:3003"
      environment:
        - STORAGE_GB=${AEROSPIKE_STORAGE_GB}
        - MEM_GB=${AEROSPIKE_MEM_GB}
        - NAMESPACE=${AEROSPIKE_NAMESPACE}
      networks:
        - backend

### Memcached ############################################
    memcached:
      restart: always
      build: ./memcached
      volumes:
        - ${DATA_PATH_HOST}/memcached:/var/lib/memcached
      ports:
        - "${MEMCACHED_HOST_PORT}:11211"
      depends_on:
        - php-fpm
      networks:
        - backend

### Beanstalkd ###########################################
    beanstalkd:
      restart: always
      build: ./beanstalkd
      ports:
        - "${BEANSTALKD_HOST_PORT}:11300"
      privileged: true
      depends_on:
        - php-fpm
      networks:
        - backend

### SQS #############################################
    sqs:
      restart: always
      build: ./sqs
      ports:
        - "${SQS_NODE_HOST_PORT}:9324"
        - "${SQS_MANAGEMENT_HTTP_HOST_PORT}:9325"
      privileged: true
      hostname: laradock-sqs
      volumes:
        - ${DATA_PATH_HOST}/sqs:/opt/custom
      networks:
        - frontend
        - backend

### RabbitMQ #############################################
    rabbitmq:
      restart: always
      build: ./rabbitmq
      ports:
        - "${RABBITMQ_NODE_HOST_PORT}:5672"
        - "${RABBITMQ_MANAGEMENT_HTTP_HOST_PORT}:15672"
        - "${RABBITMQ_MANAGEMENT_HTTPS_HOST_PORT}:15671"
        - "${RABBITMQ_WEB_STOMP_HOST_PORT}:15674"
      privileged: true
      hostname: laradock-rabbitmq
      volumes:
        - ${DATA_PATH_HOST}/rabbitmq:/var/lib/rabbitmq
        - ./rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
        - ./rabbitmq/management_agent.disable_metrics_collector.conf:/etc/rabbitmq/conf.d/management_agent.disable_metrics_collector.conf
      depends_on:
        - php-fpm
      networks:
        - backend

### Mercure #############################################
    mercure:
      restart: always
      build: ./mercure
      ports:
        - "${MERCURE_NODE_HOST_HTTP_PORT}:80"
        - "${MERCURE_NODE_HOST_HTTPS_PORT}:443"
      privileged: true
      environment:
        - MERCURE_PUBLISHER_JWT_KEY=${MERCURE_PUBLISHER_JWT_KEY}
        - MERCURE_SUBSCRIBER_JWT_KEY=${MERCURE_SUBSCRIBER_JWT_KEY}
        - DEBUG=${MERCURE_DEBUG}
        - SERVER_NAME=${MERCURE_SERVER_NAME}
        - MERCURE_EXTRA_DIRECTIVES=cors_allowed_origins *\npublish_allowed_origins http://localhost:${MERCURE_NODE_HOST_HTTP_PORT} https://localhost:${MERCURE_NODE_HOST_HTTP_PORT}
      hostname: laradock-mercure
      networks:
        - frontend
        - backend

### Cassandra ############################################
    cassandra:
      restart: always
      build: ./cassandra
      ports:
        - "${CASSANDRA_TRANSPORT_PORT_NUMBER}:7000"
        - "${CASSANDRA_JMX_PORT_NUMBER}:7199"
        - "${CASSANDRA_CQL_PORT_NUMBER}:9042"
      privileged: true
      environment:
        - CASSANDRA_VERSION=${CASSANDRA_VERSION}
        - CASSANDRA_TRANSPORT_PORT_NUMBER=${CASSANDRA_TRANSPORT_PORT_NUMBER}
        - CASSANDRA_JMX_PORT_NUMBER=${CASSANDRA_JMX_PORT_NUMBER}
        - CASSANDRA_CQL_PORT_NUMBER=${CASSANDRA_CQL_PORT_NUMBER}
        - CASSANDRA_USER=${CASSANDRA_USER}
        - CASSANDRA_PASSWORD_SEEDER=${CASSANDRA_PASSWORD_SEEDER}
        - CASSANDRA_PASSWORD=${CASSANDRA_PASSWORD}
        - CASSANDRA_NUM_TOKENS=${CASSANDRA_NUM_TOKENS}
        - CASSANDRA_HOST=${CASSANDRA_HOST}
        - CASSANDRA_CLUSTER_NAME=${CASSANDRA_CLUSTER_NAME}
        - CASSANDRA_SEEDS=${CASSANDRA_SEEDS}
        - CASSANDRA_ENDPOINT_SNITCH=${CASSANDRA_ENDPOINT_SNITCH}
        - CASSANDRA_ENABLE_RPC=${CASSANDRA_ENABLE_RPC}
        - CASSANDRA_DATACENTER=${CASSANDRA_DATACENTER}
        - CASSANDRA_RACK=${CASSANDRA_RACK}
      hostname: laradock-cassandra
      volumes:
        - ${DATA_PATH_HOST}/cassandra:/var/lib/cassandra
      depends_on:
        - php-fpm
      networks:
        - backend

### Gearman ############################################
    gearman:
      restart: always
      build: ./gearman
      ports:
        - "${GEARMAN_PORT}:4730"
      privileged: true
      environment:
        - GEARMAN_VERSION=${GEARMAN_VERSION}
        - GEARMAN_VERBOSE=${GEARMAN_VERBOSE}
        - GEARMAN_QUEUE_TYPE=${GEARMAN_QUEUE_TYPE}
        - GEARMAN_THREADS=${GEARMAN_THREADS}
        - GEARMAN_BACKLOG=${GEARMAN_BACKLOG}
        - GEARMAN_FILE_DESCRIPTORS=${GEARMAN_FILE_DESCRIPTORS}
        - GEARMAN_JOB_RETRIES=${GEARMAN_JOB_RETRIES}
        - GEARMAN_ROUND_ROBIN=${GEARMAN_ROUND_ROBIN}
        - GEARMAN_WORKER_WAKEUP=${GEARMAN_WORKER_WAKEUP}
        - GEARMAN_KEEPALIVE=${GEARMAN_KEEPALIVE}
        - GEARMAN_KEEPALIVE_IDLE=${GEARMAN_KEEPALIVE_IDLE}
        - GEARMAN_KEEPALIVE_INTERVAL=${GEARMAN_KEEPALIVE_INTERVAL}
        - GEARMAN_KEEPALIVE_COUNT=${GEARMAN_KEEPALIVE_COUNT}
        - GEARMAN_MYSQL_HOST=${GEARMAN_MYSQL_HOST}
        - GEARMAN_MYSQL_PORT=${GEARMAN_MYSQL_PORT}
        - GEARMAN_MYSQL_USER=${GEARMAN_MYSQL_USER}
        - GEARMAN_MYSQL_PASSWORD=${GEARMAN_MYSQL_PASSWORD}
        - GEARMAN_MYSQL_PASSWORD_FILE=${GEARMAN_MYSQL_PASSWORD_FILE}
        - GEARMAN_MYSQL_DB=${GEARMAN_MYSQL_DB}
        - GEARMAN_MYSQL_TABLE=${GEARMAN_MYSQL_TABLE}
      hostname: laradock-gearman
      depends_on:
        - php-fpm
      networks:
        - backend

### Beanstalkd Console ###################################
    beanstalkd-console:
      restart: always
      build: ./beanstalkd-console
      ports:
        - "${BEANSTALKD_CONSOLE_HOST_PORT}:2080"
      depends_on:
        - beanstalkd
      networks:
        - backend

### Caddy Server #########################################
    caddy:
      restart: always
      build: ./caddy
      volumes:
        - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
        - ${CADDY_CONFIG_PATH}:/etc/caddy
        - ${CADDY_HOST_LOG_PATH}:/var/log/caddy
        - ${DATA_PATH_HOST}:/root/.caddy
      ports:
        - "${CADDY_HOST_HTTP_PORT}:80"
        - "${CADDY_HOST_HTTPS_PORT}:443"
      depends_on:
        - php-fpm
      networks:
        - frontend
        - backend

### phpMyAdmin ###########################################
    phpmyadmin:
      restart: always
      build: ./phpmyadmin
      environment:
        - PMA_ARBITRARY=1
        - MYSQL_USER=${PMA_USER}
        - MYSQL_PASSWORD=${PMA_PASSWORD}
        - MYSQL_ROOT_PASSWORD=${PMA_ROOT_PASSWORD}
        - MAX_EXECUTION_TIME=${PMA_MAX_EXECUTION_TIME}
        - MEMORY_LIMIT=${PMA_MEMORY_LIMIT}
        - UPLOAD_LIMIT=${PMA_UPLOAD_LIMIT}
      ports:
        - "${PMA_PORT}:80"
      depends_on:
        - "${PMA_DB_ENGINE}"
      networks:
        - frontend
        - backend

### Adminer ###########################################
    adminer:
      restart: always
      build:
        context: ./adminer
        args:
          - INSTALL_MSSQL=${ADM_INSTALL_MSSQL}
      environment:
          - ADMINER_PLUGINS=${ADM_PLUGINS}
          - ADMINER_DESIGN=${ADM_DESIGN}
          - ADMINER_DEFAULT_SERVER=${ADM_DEFAULT_SERVER}
      ports:
        - "${ADM_PORT}:8080"
      # depends_on:
      #   - php-fpm
      networks:
        - frontend
        - backend

### pgAdmin ##############################################
    pgadmin:
      restart: always
      image: dpage/pgadmin4:latest
      environment:
        - "PGADMIN_DEFAULT_EMAIL=${PGADMIN_DEFAULT_EMAIL}"
        - "PGADMIN_DEFAULT_PASSWORD=${PGADMIN_DEFAULT_PASSWORD}"
      ports:
        - "${PGADMIN_PORT}:80"
      volumes:
        - ${DATA_PATH_HOST}/pgadmin:/var/lib/pgadmin
      depends_on:
        - postgres
      networks:
        - frontend
        - backend

### MeiliSearch ##########################################
    meilisearch:
      image: getmeili/meilisearch:latest
      volumes:
        - ${DATA_PATH_HOST}/meilisearch:/data.ms
      ports:
        - "${MEILISEARCH_HOST_PORT}:7700"
      environment:
        - MEILI_MASTER_KEY=${MEILISEARCH_KEY}
      networks:
        - frontend
        - backend

### ElasticSearch ########################################
    elasticsearch:
      restart: always
      build:
        context: ./elasticsearch
        args:
          - ELK_VERSION=${ELK_VERSION}
      volumes:
        - elasticsearch:/usr/share/elasticsearch/data
      environment:
        - cluster.name=laradock-cluster
        - node.name=laradock-node
        - bootstrap.memory_lock=true
        - xpack.security.enabled=false
        - xpack.security.transport.ssl.enabled=false
        - indices.id_field_data.enabled=true
        - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
        - cluster.initial_master_nodes=laradock-node
      ulimits:
        memlock:
          soft: -1
          hard: -1
        nofile:
          soft: 65536
          hard: 65536
      ports:
        - "${ELASTICSEARCH_HOST_HTTP_PORT}:9200"
        - "${ELASTICSEARCH_HOST_TRANSPORT_PORT}:9300"
      depends_on:
        - php-fpm
      networks:
        - frontend
        - backend

### Logstash ##############################################
    logstash:
      restart: always
      build:
        context: ./logstash
        args:
          - ELK_VERSION=${ELK_VERSION}
      volumes:
        - './logstash/config/logstash.yml:/usr/share/logstash/config/logstash.yml'
        - './logstash/pipeline:/usr/share/logstash/pipeline'
      ports:
        - '5001:5001'
      environment:
        LS_JAVA_OPTS: '-Xmx1g -Xms1g'
      env_file:
        - .env
      networks:
        - frontend
        - backend
      depends_on:
        - elasticsearch

### Kibana ##############################################
    kibana:
      restart: always
      build:
        context: ./kibana
        args:
          - ELK_VERSION=${ELK_VERSION}
      ports:
        - "${KIBANA_HTTP_PORT}:5601"
      depends_on:
        - elasticsearch
      networks:
        - frontend
        - backend

### Dejavu ##############################################
    dejavu:
      restart: always
      build:
        context: ./dejavu
      ports:
        - "${DEJAVU_HTTP_PORT}:1358"
      depends_on:
        - elasticsearch
      networks:
        - frontend
        - backend

### Certbot #########################################
    certbot:
      build:
        context: ./certbot
      volumes:
        - ./data/certbot/certs/:/var/certs
        - ./certbot/letsencrypt/:/var/www/letsencrypt
      environment:
        - CN="fake.domain.com"
        - EMAIL="<EMAIL>"
      networks:
        - frontend

### MailCatcher ################################################
    mailcatcher:
      restart: always
      build: ./mailcatcher
      ports:
        - "1025:1025"
        - "1080:1080"
      networks:
        - frontend
        - backend

### Mailhog ################################################
    mailhog:
      restart: always
      build: ./mailhog
      ports:
        - "1025:1025"
        - "8025:8025"
      networks:
        - frontend
        - backend

### MailDev ##############################################
    maildev:
      restart: always
      build: ./maildev
      ports:
        - "${MAILDEV_HTTP_PORT}:80"
        - "${MAILDEV_SMTP_PORT}:25"
      networks:
        - frontend
        - backend

### Mailpit ##############################################
    mailpit:
      restart: always
      build: ./mailpit
      ports:
        - "${MAILPIT_HTTP_PORT}:8025"
        - "${MAILPIT_SMTP_PORT}:1025"
      networks:
        - frontend
        - backend

### Selenium ###############################################
    selenium:
      restart: always
      build: ./selenium
      ports:
        - "${SELENIUM_PORT}:4444"
      volumes:
        - /dev/shm:/dev/shm
      networks:
        - frontend

### Varnish ##########################################
    proxy:
      restart: always
      container_name: proxy
      build: ./varnish
      expose:
        - ${VARNISH_PORT}
      environment:
        - VARNISH_CONFIG=${VARNISH_CONFIG}
        - CACHE_SIZE=${VARNISH_PROXY1_CACHE_SIZE}
        - VARNISHD_PARAMS=${VARNISHD_PARAMS}
        - VARNISH_PORT=${VARNISH_PORT}
        - BACKEND_HOST=${VARNISH_PROXY1_BACKEND_HOST}
        - BACKEND_PORT=${VARNISH_BACKEND_PORT}
        - VARNISH_SERVER=${VARNISH_PROXY1_SERVER}
      ports:
        - "${VARNISH_PORT}:${VARNISH_PORT}"
      links:
        - workspace
      networks:
        - frontend

    proxy2:
      container_name: proxy2
      build: ./varnish
      expose:
        - ${VARNISH_PORT}
      environment:
        - VARNISH_CONFIG=${VARNISH_CONFIG}
        - CACHE_SIZE=${VARNISH_PROXY2_CACHE_SIZE}
        - VARNISHD_PARAMS=${VARNISHD_PARAMS}
        - VARNISH_PORT=${VARNISH_PORT}
        - BACKEND_HOST=${VARNISH_PROXY2_BACKEND_HOST}
        - BACKEND_PORT=${VARNISH_BACKEND_PORT}
        - VARNISH_SERVER=${VARNISH_PROXY2_SERVER}
      ports:
        - "${VARNISH_PORT}:${VARNISH_PORT}"
      links:
        - workspace
      networks:
        - frontend

### HAProxy ####################################
    haproxy:
      restart: always
      build: ./haproxy
      ports:
        - "${HAPROXY_HOST_HTTP_PORT}:8085"
      volumes:
        - /var/run/docker.sock:/var/run/docker.sock
      links:
        - proxy
        - proxy2

### Jenkins ###################################################
    jenkins:
      restart: always
      build: ./jenkins
      environment:
        JAVA_OPTS: "-Djava.awt.headless=true"
      ports:
        - "${JENKINS_HOST_SLAVE_AGENT_PORT}:50000"
        - "${JENKINS_HOST_HTTP_PORT}:8080"
      privileged: true
      volumes:
        - ${JENKINS_HOME}:/var/jenkins_home
        - /var/run/docker.sock:/var/run/docker.sock
      networks:
        - frontend
        - backend

### Grafana ################################################
    grafana:
      restart: always
      build:
        context: ./grafana
      volumes:
        - ${DATA_PATH_HOST}/grafana:/var/lib/grafana
      ports:
        - "${GRAFANA_PORT}:3000"
      networks:
        - backend

### Graylog #######################################
    graylog:
      restart: always
      build: ./graylog
      environment:
        - GRAYLOG_PASSWORD_SECRET=${GRAYLOG_PASSWORD}
        - GRAYLOG_ROOT_PASSWORD_SHA2=${GRAYLOG_SHA256_PASSWORD}
        - GRAYLOG_HTTP_EXTERNAL_URI=http://127.0.0.1:${GRAYLOG_PORT}/
      links:
        - mongo
        - elasticsearch
      depends_on:
        - mongo
        - elasticsearch
      ports:
        # Graylog web interface and REST API
        - ${GRAYLOG_PORT}:9000
        # Syslog TCP
        - ${GRAYLOG_SYSLOG_TCP_PORT}:514
        # Syslog UDP
        - ${GRAYLOG_SYSLOG_UDP_PORT}:514/udp
        # GELF TCP
        - ${GRAYLOG_GELF_TCP_PORT}:12201
        # GELF UDP
        - ${GRAYLOG_GELF_UDP_PORT}:12201/udp
      user: graylog
      volumes:
        - ${DATA_PATH_HOST}/graylog:/usr/share/graylog/data
      networks:
        - backend

### Laravel Echo Server #######################################
    laravel-echo-server:
      restart: always
      build:
        context: ./laravel-echo-server
        args:
          - CHANGE_SOURCE=${CHANGE_SOURCE}
      volumes:
        - ./laravel-echo-server/laravel-echo-server.json:/app/laravel-echo-server.json:ro
      ports:
        - "${LARAVEL_ECHO_SERVER_PORT}:6001"
      links:
        - redis
      networks:
        - frontend
        - backend

### Solr ################################################
    solr:
      restart: always
      build:
        context: ./solr
        args:
          - SOLR_VERSION=${SOLR_VERSION}
          - SOLR_DATAIMPORTHANDLER_MYSQL=${SOLR_DATAIMPORTHANDLER_MYSQL}
          - SOLR_DATAIMPORTHANDLER_MSSQL=${SOLR_DATAIMPORTHANDLER_MSSQL}
      volumes:
        - ${DATA_PATH_HOST}/solr:/opt/solr/server/solr/mycores
      ports:
        - "${SOLR_PORT}:8983"
      networks:
        - backend

### Thumbor #########################################
    thumbor:
      restart: always
      build: ./thumbor
      volumes:
        - ${DATA_PATH_HOST}/thumbor/data:/data
        - ${DATA_PATH_HOST}/thumbor/data:/logs
      ports:
        - "${THUMBOR_PORT}:8000"
      environment:
        - THUMBOR_LOG_FORMAT=${THUMBOR_LOG_FORMAT}
        - THUMBOR_LOG_DATE_FORMAT=${THUMBOR_LOG_DATE_FORMAT}
        - MAX_WIDTH=${MAX_WIDTH}
        - MAX_HEIGHT=${MAX_HEIGHT}
        - MIN_WIDTH=${MIN_WIDTH}
        - MIN_HEIGHT=${MIN_HEIGHT}
        - ALLOWED_SOURCES=${ALLOWED_SOURCES}
        - QUALITY=${QUALITY}
        - WEBP_QUALITY=${WEBP_QUALITY}
        - PNG_COMPRESSION_LEVEL=${PNG_COMPRESSION_LEVEL}
        - AUTO_WEBP=${AUTO_WEBP}
        - MAX_AGE=${MAX_AGE}
        - MAX_AGE_TEMP_IMAGE=${MAX_AGE_TEMP_IMAGE}
        - RESPECT_ORIENTATION=${RESPECT_ORIENTATION}
        - IGNORE_SMART_ERRORS=${IGNORE_SMART_ERRORS}
        - PRESERVE_EXIF_INFO=${PRESERVE_EXIF_INFO}
        - ALLOW_ANIMATED_GIFS=${ALLOW_ANIMATED_GIFS}
        - USE_GIFSICLE_ENGINE=${USE_GIFSICLE_ENGINE}
        - USE_BLACKLIST=${USE_BLACKLIST}
        - LOADER=${LOADER}
        - STORAGE=${STORAGE}
        - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
        - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
        - RESULT_STORAGE=${RESULT_STORAGE}
        - ENGINE=${ENGINE}
        - SECURITY_KEY=${SECURITY_KEY}
        - ALLOW_UNSAFE_URL=${ALLOW_UNSAFE_URL}
        - ALLOW_OLD_URLS=${ALLOW_OLD_URLS}
        - FILE_LOADER_ROOT_PATH=${FILE_LOADER_ROOT_PATH}
        - HTTP_LOADER_CONNECT_TIMEOUT=${HTTP_LOADER_CONNECT_TIMEOUT}
        - HTTP_LOADER_REQUEST_TIMEOUT=${HTTP_LOADER_REQUEST_TIMEOUT}
        - HTTP_LOADER_FOLLOW_REDIRECTS=${HTTP_LOADER_FOLLOW_REDIRECTS}
        - HTTP_LOADER_MAX_REDIRECTS=${HTTP_LOADER_MAX_REDIRECTS}
        - HTTP_LOADER_FORWARD_USER_AGENT=${HTTP_LOADER_FORWARD_USER_AGENT}
        - HTTP_LOADER_DEFAULT_USER_AGENT=${HTTP_LOADER_DEFAULT_USER_AGENT}
        - HTTP_LOADER_PROXY_HOST=${HTTP_LOADER_PROXY_HOST}
        - HTTP_LOADER_PROXY_PORT=${HTTP_LOADER_PROXY_PORT}
        - HTTP_LOADER_PROXY_USERNAME=${HTTP_LOADER_PROXY_USERNAME}
        - HTTP_LOADER_PROXY_PASSWORD=${HTTP_LOADER_PROXY_PASSWORD}
        - HTTP_LOADER_CA_CERTS=${HTTP_LOADER_CA_CERTS}
        - HTTP_LOADER_VALIDATE_CERTS=${HTTP_LOADER_VALIDATE_CERTS}
        - HTTP_LOADER_CLIENT_KEY=${HTTP_LOADER_CLIENT_KEY}
        - HTTP_LOADER_CLIENT_CERT=${HTTP_LOADER_CLIENT_CERT}
        - HTTP_LOADER_CURL_ASYNC_HTTP_CLIENT=${HTTP_LOADER_CURL_ASYNC_HTTP_CLIENT}
        - STORAGE_EXPIRATION_SECONDS=${STORAGE_EXPIRATION_SECONDS}
        - STORES_CRYPTO_KEY_FOR_EACH_IMAGE=${STORES_CRYPTO_KEY_FOR_EACH_IMAGE}
        - FILE_STORAGE_ROOT_PATH=${FILE_STORAGE_ROOT_PATH}
        - UPLOAD_MAX_SIZE=${UPLOAD_MAX_SIZE}
        - UPLOAD_ENABLED=${UPLOAD_ENABLED}
        - UPLOAD_PHOTO_STORAGE=${UPLOAD_PHOTO_STORAGE}
        - UPLOAD_DELETE_ALLOWED=${UPLOAD_DELETE_ALLOWED}
        - UPLOAD_PUT_ALLOWED=${UPLOAD_PUT_ALLOWED}
        - UPLOAD_DEFAULT_FILENAME=${UPLOAD_DEFAULT_FILENAME}
        - MONGO_STORAGE_SERVER_HOST=${MONGO_STORAGE_SERVER_HOST}
        - MONGO_STORAGE_SERVER_PORT=${MONGO_STORAGE_SERVER_PORT}
        - MONGO_STORAGE_SERVER_DB=${MONGO_STORAGE_SERVER_DB}
        - MONGO_STORAGE_SERVER_COLLECTION=${MONGO_STORAGE_SERVER_COLLECTION}
        - REDIS_STORAGE_SERVER_HOST=${REDIS_STORAGE_SERVER_HOST}
        - REDIS_STORAGE_SERVER_PORT=${REDIS_STORAGE_SERVER_PORT}
        - REDIS_STORAGE_SERVER_DB=${REDIS_STORAGE_SERVER_DB}
        - REDIS_STORAGE_SERVER_PASSWORD=${REDIS_STORAGE_SERVER_PASSWORD}
        - REDIS_RESULT_STORAGE_SERVER_HOST=${REDIS_RESULT_STORAGE_SERVER_HOST}
        - REDIS_RESULT_STORAGE_SERVER_PORT=${REDIS_RESULT_STORAGE_SERVER_PORT}
        - REDIS_RESULT_STORAGE_SERVER_DB=${REDIS_RESULT_STORAGE_SERVER_DB}
        - REDIS_RESULT_STORAGE_SERVER_PASSWORD=${REDIS_RESULT_STORAGE_SERVER_PASSWORD}
        - MEMCACHE_STORAGE_SERVERS=${MEMCACHE_STORAGE_SERVERS}
        - MIXED_STORAGE_FILE_STORAGE=${MIXED_STORAGE_FILE_STORAGE}
        - MIXED_STORAGE_CRYPTO_STORAGE=${MIXED_STORAGE_CRYPTO_STORAGE}
        - MIXED_STORAGE_DETECTOR_STORAGE=${MIXED_STORAGE_DETECTOR_STORAGE}
        - META_CALLBACK_NAME=${META_CALLBACK_NAME}
        - DETECTORS=${DETECTORS}
        - FACE_DETECTOR_CASCADE_FILE=${FACE_DETECTOR_CASCADE_FILE}
        - OPTIMIZERS=${OPTIMIZERS}
        - JPEGTRAN_PATH=${JPEGTRAN_PATH}
        - PROGRESSIVE_JPEG=${PROGRESSIVE_JPEG}
        - RESULT_STORAGE_EXPIRATION_SECONDS=${RESULT_STORAGE_EXPIRATION_SECONDS}
        - RESULT_STORAGE_FILE_STORAGE_ROOT_PATH=${RESULT_STORAGE_FILE_STORAGE_ROOT_PATH}
        - RESULT_STORAGE_STORES_UNSAFE=${RESULT_STORAGE_STORES_UNSAFE}
        - REDIS_QUEUE_SERVER_HOST=${REDIS_QUEUE_SERVER_HOST}
        - REDIS_QUEUE_SERVER_PORT=${REDIS_QUEUE_SERVER_PORT}
        - REDIS_QUEUE_SERVER_DB=${REDIS_QUEUE_SERVER_DB}
        - REDIS_QUEUE_SERVER_PASSWORD=${REDIS_QUEUE_SERVER_PASSWORD}
        - SQS_QUEUE_KEY_ID=${SQS_QUEUE_KEY_ID}
        - SQS_QUEUE_KEY_SECRET=${SQS_QUEUE_KEY_SECRET}
        - SQS_QUEUE_REGION=${SQS_QUEUE_REGION}
        - USE_CUSTOM_ERROR_HANDLING=${USE_CUSTOM_ERROR_HANDLING}
        - ERROR_HANDLER_MODULE=${ERROR_HANDLER_MODULE}
        - ERROR_FILE_LOGGER=${ERROR_FILE_LOGGER}
        - ERROR_FILE_NAME_USE_CONTEXT=${ERROR_FILE_NAME_USE_CONTEXT}
        - SENTRY_DSN_URL=${SENTRY_DSN_URL}
        - TC_AWS_REGION=${TC_AWS_REGION}
        - TC_AWS_ENDPOINT=${TC_AWS_ENDPOINT}
        - TC_AWS_STORAGE_BUCKET=${TC_AWS_STORAGE_BUCKET}
        - TC_AWS_STORAGE_ROOT_PATH=${TC_AWS_STORAGE_ROOT_PATH}
        - TC_AWS_LOADER_BUCKET=${TC_AWS_LOADER_BUCKET}
        - TC_AWS_LOADER_ROOT_PATH=${TC_AWS_LOADER_ROOT_PATH}
        - TC_AWS_RESULT_STORAGE_BUCKET=${TC_AWS_RESULT_STORAGE_BUCKET}
        - TC_AWS_RESULT_STORAGE_ROOT_PATH=${TC_AWS_RESULT_STORAGE_ROOT_PATH}
        - TC_AWS_STORAGE_SSE=${TC_AWS_STORAGE_SSE}
        - TC_AWS_STORAGE_RRS=${TC_AWS_STORAGE_RRS}
        - TC_AWS_ENABLE_HTTP_LOADER=${TC_AWS_ENABLE_HTTP_LOADER}
        - TC_AWS_ALLOWED_BUCKETS=${TC_AWS_ALLOWED_BUCKETS}
        - TC_AWS_STORE_METADATA=${TC_AWS_STORE_METADATA}
      networks:
        - frontend
        - backend

### AWS EB-CLI ################################################
    aws:
      restart: always
      build:
        context: ./aws-eb-cli
      volumes:
        - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}${APP_CODE_CONTAINER_FLAG}
      depends_on:
        - workspace
      tty: true

### Portainer ################################################
    portainer:
      restart: always
      build:
        context: ./portainer
      volumes:
        - ${DATA_PATH_HOST}/portainer_data:/data
        - /var/run/docker.sock:/var/run/docker.sock
      extra_hosts:
        - "dockerhost:${DOCKER_HOST_IP}"
      ports:
        - 9010:9000
      networks:
        - backend

### Gitlab ################################################
    gitlab:
      build:
        context: ./gitlab
      environment:
        GITLAB_OMNIBUS_CONFIG: |
          external_url '${GITLAB_DOMAIN_NAME}'
          redis['enable'] = false
          nginx['listen_https'] = false
          nginx['listen_port'] = 80
          nginx['custom_gitlab_server_config'] = "set_real_ip_from *********/8;\nreal_ip_header X-Real-IP;\nreal_ip_recursive on;"
          postgresql['enable'] = false
          gitlab_rails['trusted_proxies'] = ['caddy','nginx','apache2']
          gitlab_rails['redis_host'] = 'redis'
          gitlab_rails['redis_database'] = 8
          gitlab_rails['db_host'] = '${GITLAB_POSTGRES_HOST}'
          gitlab_rails['db_username'] = '${GITLAB_POSTGRES_USER}'
          gitlab_rails['db_password'] = '${GITLAB_POSTGRES_PASSWORD}'
          gitlab_rails['db_database'] = '${GITLAB_POSTGRES_DB}'
          gitlab_rails['initial_root_password'] = '${GITLAB_ROOT_PASSWORD}'
          gitlab_rails['gitlab_shell_ssh_port'] = ${GITLAB_HOST_SSH_PORT}
      volumes:
        - ${DATA_PATH_HOST}/gitlab/config:/etc/gitlab
        - ${DATA_PATH_HOST}/gitlab/data:/var/opt/gitlab
        - ${GITLAB_HOST_LOG_PATH}:/var/log/gitlab
      ports:
        - "${GITLAB_HOST_HTTP_PORT}:80"
        - "${GITLAB_HOST_HTTPS_PORT}:443"
        - "${GITLAB_HOST_SSH_PORT}:22"
      networks:
        - backend
      depends_on:
        - redis
        - postgres
    gitlab-runner:
      image: gitlab/gitlab-runner:latest
      environment:
        - CI_SERVER_URL=${GITLAB_CI_SERVER_URL}
        - REGISTRATION_TOKEN=${GITLAB_RUNNER_REGISTRATION_TOKEN}
        - RUNNER_NAME=${COMPOSE_PROJECT_NAME}-runner
        - REGISTER_NON_INTERACTIVE=${GITLAB_REGISTER_NON_INTERACTIVE}
        - RUNNER_EXECUTOR=shell
      volumes:
        - ${DATA_PATH_HOST}/gitlab/runner:/etc/gitlab-runner
        - /var/run/docker.sock:/var/run/docker.sock:rw
      restart: always

### JupyterHub #########################################
    jupyterhub:
      restart: always
      build:
        context: ./jupyterhub
      depends_on:
        - postgres
        - jupyterhub-user
      volumes:
        - /var/run/docker.sock:/var/run/docker.sock:rw
        - ${DATA_PATH_HOST}/jupyterhub/:/data
        - ${JUPYTERHUB_CUSTOM_CONFIG}:/jupyterhub_config.py
        - ${JUPYTERHUB_USER_DATA}:/user-data
        - ${JUPYTERHUB_USER_LIST}:/userlist
      networks:
        - backend
      ports:
        - "${JUPYTERHUB_PORT}:80"
      environment:
        - TERM=xterm
        - JUPYTERHUB_USER_DATA=${JUPYTERHUB_USER_DATA}
        - JUPYTERHUB_POSTGRES_DB=${JUPYTERHUB_POSTGRES_DB}
        - JUPYTERHUB_POSTGRES_USER=${JUPYTERHUB_POSTGRES_USER}
        - JUPYTERHUB_POSTGRES_HOST=${JUPYTERHUB_POSTGRES_HOST}
        - JUPYTERHUB_POSTGRES_PASSWORD=${JUPYTERHUB_POSTGRES_PASSWORD}
        - JUPYTERHUB_OAUTH_CALLBACK_URL=${JUPYTERHUB_OAUTH_CALLBACK_URL}
        - JUPYTERHUB_OAUTH_CLIENT_ID=${JUPYTERHUB_OAUTH_CLIENT_ID}
        - JUPYTERHUB_OAUTH_CLIENT_SECRET=${JUPYTERHUB_OAUTH_CLIENT_SECRET}
        - JUPYTERHUB_LOCAL_NOTEBOOK_IMAGE=${COMPOSE_PROJECT_NAME}_jupyterhub-user
        - JUPYTERHUB_ENABLE_NVIDIA=${JUPYTERHUB_ENABLE_NVIDIA}
    jupyterhub-user:
      build:
        context: ./jupyterhub
        dockerfile: Dockerfile.user
      command: ["sh", "-c", "echo \"build only\""]

### IPython #########################################
    ipython-controller:
      restart: always
      build:
        context: ./ipython
        dockerfile: Dockerfile.controller
      networks:
        - backend
      extra_hosts:
        - "laradock-ipython:${LARADOCK_IPYTHON_CONTROLLER_IP}"
      ports:
        - "33327-33338:33327-33338"
    ipython-engine:
      build:
        context: ./ipython
        dockerfile: Dockerfile.engine
      networks:
        - backend
      extra_hosts:
        - "laradock-ipython:${LARADOCK_IPYTHON_CONTROLLER_IP}"

### Docker-in-Docker ################################################
    docker-in-docker:
      restart: always
      image: docker:20.10-dind
      environment:
        DOCKER_TLS_SAN: DNS:docker-in-docker
      privileged: true
      volumes:
        - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}
        - docker-in-docker:/certs/client
      expose:
        - 2375
      networks:
        - backend

### NetData ################################################
    netdata:
      restart: always
      image: netdata/netdata:latest
      cap_add:
        - SYS_PTRACE
      volumes:
        - /proc:/host/proc:ro
        - /sys:/host/sys:ro
        - /var/run/docker.sock:/var/run/docker.sock:ro
      ports:
        - "${NETDATA_PORT}:19999"
      networks:
        - backend

### REDISWEBUI ################################################
    redis-webui:
      restart: always
      build:
        context: ./redis-webui
      environment:
        - ADMIN_USER=${REDIS_WEBUI_USERNAME}
        - ADMIN_PASS=${REDIS_WEBUI_PASSWORD}
        - REDIS_1_HOST=${REDIS_WEBUI_CONNECT_HOST}
        - REDIS_1_PORT=${REDIS_WEBUI_CONNECT_PORT}
        - REDIS_1_AUTH=${REDIS_PASSWORD}
      networks:
        - backend
      ports:
        - "${REDIS_WEBUI_PORT}:80"
      depends_on:
        - redis

### MongoWebUI ################################################
    mongo-webui:
      restart: always
      build:
        context: ./mongo-webui
      environment:
        - ROOT_URL=${MONGO_WEBUI_ROOT_URL}
        - MONGO_URL=${MONGO_WEBUI_MONGO_URL}
        - INSTALL_MONGO=${MONGO_WEBUI_INSTALL_MONGO}
      volumes:
        - ${DATA_PATH_HOST}/mongo-webui:/data/db
      ports:
        - "${MONGO_WEBUI_PORT}:3000"
      networks:
        - backend
      depends_on:
        - mongo

### Metabase #################################################
    metabase:
      restart: always
      image: metabase/metabase:latest
      environment:
        - MB_DB_FILE=/metabase-data/${METABASE_DB_FILE}
      ports:
        - ${METABASE_PORT}:3000
      volumes:
        - ${DATA_PATH_HOST}/metabase-data:/metabase-data
      networks:
        - backend

### Weaver (Athena PDF) #################################################
    weaver:
      image: arachnysdocker/athenapdf-service
      ports:
        - "8080:8080"
      env_file:
        - ./weaver/conf/sample.env
      networks:
        - backend
      depends_on:
        - workspace

### IDE-THEIA ################################################
    ide-theia:
      build:
        context: ./ide-theia
      volumes:
        - ${APP_CODE_PATH_HOST}:/home/<USER>
      ports:
        - "${IDE_THEIA_PORT}:3000"
      networks:
        - backend

### IDE-WEBIDE ################################################
    ide-webide:
      build:
        context: ./ide-webide
      volumes:
        - ${DATA_PATH_HOST}/ide/webide/ide.db:/root/.coding-ide/ide.db
      ports:
        - "${IDE_WEBIDE_PORT}:8080"
      networks:
        - backend

### IDE-CODIAD ################################################
    ide-codiad:
      build:
        context: ./ide-codiad
      environment:
        - APP_CODE_PATH_CONTAINER=${APP_CODE_PATH_CONTAINER}
        - TZ=${WORKSPACE_TIMEZONE}
        - PGID=1000
        - PUID=1000
      volumes:
        - /etc/localtime:/etc/localtime:ro
        - ${APP_CODE_PATH_HOST}:${APP_CODE_PATH_CONTAINER}
        - ${DATA_PATH_HOST}/ide/codiad:/config
      ports:
        - "${IDE_CODIAD_PORT}:80"
      networks:
        - backend

### IDE-ICECODER ################################################
    ide-icecoder:
      build:
        context: ./ide-icecoder
      environment:
        - DOCUMENT_ROOT=${APP_CODE_PATH_CONTAINER}
        - TZ=${WORKSPACE_TIMEZONE}
        - PGID=1000
        - PUID=1000
      volumes:
        - /etc/localtime:/etc/localtime:ro
        - ${APP_CODE_PATH_HOST}:/home/<USER>/ICEcoder/dev
      ports:
        - "${IDE_ICECODER_PORT}:8080"
      networks:
        - backend

### DOCKER-REGISTRY ################################################
    docker-registry:
      build:
        context: ./docker-registry
      volumes:
        - /etc/localtime:/etc/localtime:ro
        - ${DATA_PATH_HOST}/docker-registry:/var/lib/registry
      ports:
        - "${DOCKER_REGISTRY_PORT}:5000"
      networks:
        - backend

### DOCKER-WEB-UI ################################################
    docker-web-ui:
      build:
        context: ./docker-web-ui
      environment:
        - TZ=${WORKSPACE_TIMEZONE}
        - ENV_DOCKER_REGISTRY_HOST=${DOCKER_WEBUI_REGISTRY_HOST}
        - ENV_DOCKER_REGISTRY_PORT=${DOCKER_WEBUI_REGISTRY_PORT}
        - ENV_DOCKER_REGISTRY_USE_SSL=${DOCKER_REGISTRY_USE_SSL}
        - ENV_MODE_BROWSE_ONLY=${DOCKER_REGISTRY_BROWSE_ONLY}
      volumes:
        - /etc/localtime:/etc/localtime:ro
      ports:
        - "${DOCKER_WEBUI_PORT}:80"
      networks:
        - frontend
        - backend

### MAILU ################################################
    mailu:
      image: mailu/admin:${MAILU_VERSION}
      volumes:
        - "${DATA_PATH_HOST}/mailu/data:/data"
        - "${DATA_PATH_HOST}/mailu/dkim:/dkim"
        - "${DATA_PATH_HOST}/mailu/webmail:/webmail"
        - /var/run/docker.sock:/var/run/docker.sock:ro
      depends_on:
        - mailu-front
        - mailu-imap
        - mailu-smtp
        - mailu-antispam
        - mailu-antivirus
        - mailu-webdav
        - mailu-admin
        - mailu-webmail
        - mailu-fetchmail
      command: ["sh", "-c", "echo ${MAILU_INIT_ADMIN_USERNAME}@${MAILU_DOMAIN} ${MAILU_INIT_ADMIN_PASSWORD} ;python manage.py advertise ; python manage.py db upgrade ; python manage.py admin ${MAILU_INIT_ADMIN_USERNAME} ${MAILU_DOMAIN} ${MAILU_INIT_ADMIN_PASSWORD} || true;sed -i -- \"s/= Off/= On/g\" /webmail/_data_/_default_/configs/config.ini || true;if grep -Fq \"registration_link_url\" /webmail/_data_/_default_/configs/config.ini;then echo Already set!;else echo \"\" >> /webmail/_data_/_default_/configs/config.ini; echo \"[login]\" >> /webmail/_data_/_default_/configs/config.ini;echo \"registration_link_url = '${MAILU_WEBSITE}${MAILU_WEB_ADMIN}/ui/user/signup'\" >> /webmail/_data_/_default_/configs/config.ini;fi"]
      networks:
        - backend
    mailu-front:
      image: mailu/nginx:${MAILU_VERSION}
      environment:
        - ADMIN=${MAILU_ADMIN}
        - WEB_ADMIN=${MAILU_WEB_ADMIN}
        - WEB_WEBMAIL=${MAILU_WEB_WEBMAIL}
        - WEBDAV=${MAILU_WEBDAV}
        - HOSTNAMES=${MAILU_HOSTNAMES}
        - TLS_FLAVOR=${MAILU_TLS_FLAVOR}
        - MESSAGE_SIZE_LIMIT=${MAILU_MESSAGE_SIZE_LIMIT}
      ports:
        - "${MAILU_HTTP_PORT}:80"
        - "${MAILU_HTTPS_PORT}:443"
        - "110:110"
        - "143:143"
        - "993:993"
        - "995:995"
        - "25:25"
        - "465:465"
        - "587:587"
      volumes:
        - "${DATA_PATH_HOST}/mailu/certs:/certs"
      networks:
        backend:
          aliases:
            - front
    mailu-imap:
      image: mailu/dovecot:${MAILU_VERSION}
      environment:
        - DOMAIN=${MAILU_DOMAIN}
        - HOSTNAMES=${MAILU_HOSTNAMES}
        - POSTMASTER=${MAILU_POSTMASTER}
        - WEBMAIL=${MAILU_WEBMAIL}
        - RECIPIENT_DELIMITER=${MAILU_RECIPIENT_DELIMITER}
      volumes:
        - "${DATA_PATH_HOST}/mailu/data:/data"
        - "${DATA_PATH_HOST}/mailu/mail:/mail"
        - "${DATA_PATH_HOST}/mailu/overrides:/overrides"
      depends_on:
        - mailu-front
      networks:
        backend:
          aliases:
            - imap
    mailu-smtp:
      image: mailu/postfix:${MAILU_VERSION}
      environment:
        - DOMAIN=${MAILU_DOMAIN}
        - HOSTNAMES=${MAILU_HOSTNAMES}
        - RELAYHOST=${MAILU_RELAYHOST}
        - RELAYNETS=${MAILU_RELAYNETS}
        - RECIPIENT_DELIMITER=${MAILU_RECIPIENT_DELIMITER}
        - MESSAGE_SIZE_LIMIT=${MAILU_MESSAGE_SIZE_LIMIT}
      volumes:
        - "${DATA_PATH_HOST}/mailu/data:/data"
        - "${DATA_PATH_HOST}/mailu/overrides:/overrides"
      depends_on:
        - mailu-front
      networks:
        backend:
          aliases:
            - smtp
    mailu-antispam:
      image: mailu/rspamd:${MAILU_VERSION}
      volumes:
        - "${DATA_PATH_HOST}/mailu/filter:/var/lib/rspamd"
        - "${DATA_PATH_HOST}/mailu/dkim:/dkim"
        - "${DATA_PATH_HOST}/mailu/overrides/rspamd:/etc/rspamd/override.d"
      depends_on:
        - mailu-front
      networks:
        backend:
          aliases:
            - antispam
    mailu-antivirus:
      image: mailu/clamav:${MAILU_VERSION}
      volumes:
        - "${DATA_PATH_HOST}/mailu/filter:/data"
      networks:
        backend:
          aliases:
            - antivirus
    mailu-webdav:
      image: mailu/${MAILU_WEBDAV}:${MAILU_VERSION}
      volumes:
        - "${DATA_PATH_HOST}/mailu/dav:/data"
      networks:
        backend:
          aliases:
            - webdav
    mailu-admin:
      image: mailu/admin:${MAILU_VERSION}
      environment:
        - DOMAIN=${MAILU_DOMAIN}
        - HOSTNAMES=${MAILU_HOSTNAMES}
        - POSTMASTER=${MAILU_POSTMASTER}
        - SECRET_KEY=${MAILU_SECRET_KEY}
        - AUTH_RATELIMIT=${MAILU_AUTH_RATELIMIT}
        - TLS_FLAVOR=${MAILU_TLS_FLAVOR}
        - DISABLE_STATISTICS=${MAILU_DISABLE_STATISTICS}
        - DMARC_RUA=${MAILU_DMARC_RUA}
        - DMARC_RUF=${MAILU_DMARC_RUF}
        - WELCOME=${MAILU_WELCOME}
        - WELCOME_SUBJECT=${MAILU_WELCOME_SUBJECT}
        - WELCOME_BODY=${MAILU_WELCOME_BODY}
        - WEB_ADMIN=${MAILU_WEB_ADMIN}
        - WEB_WEBMAIL=${MAILU_WEB_WEBMAIL}
        - WEBSITE=${MAILU_WEBSITE}
        - WEBMAIL=${MAILU_WEBMAIL}
        - SITENAME=${MAILU_SITENAME}
        - PASSWORD_SCHEME=${MAILU_PASSWORD_SCHEME}
        - RECAPTCHA_PUBLIC_KEY=${MAILU_RECAPTCHA_PUBLIC_KEY}
        - RECAPTCHA_PRIVATE_KEY=${MAILU_RECAPTCHA_PRIVATE_KEY}
      volumes:
        - "${DATA_PATH_HOST}/mailu/data:/data"
        - "${DATA_PATH_HOST}/mailu/dkim:/dkim"
        - /var/run/docker.sock:/var/run/docker.sock:ro
      depends_on:
        - redis
      networks:
        backend:
          aliases:
            - admin
    mailu-webmail:
      image: "mailu/${MAILU_WEBMAIL}:${MAILU_VERSION}"
      volumes:
        - "${DATA_PATH_HOST}/mailu/webmail:/data"
      networks:
        backend:
          aliases:
            - webmail
    mailu-fetchmail:
      image: mailu/fetchmail:${MAILU_VERSION}
      environment:
        - FETCHMAIL_DELAY=${MAILU_FETCHMAIL_DELAY}
      volumes:
        - "${DATA_PATH_HOST}/mailu/data:/data"
      networks:
        backend:
          aliases:
            - fetchmail

### TRAEFIK #########################################
    traefik:
      build:
        context: ./traefik
      volumes:
        - /var/run/docker.sock:/var/run/docker.sock
        - ./traefik/data:/data
      command:
        - "--api"
        - "--providers.docker.exposedbydefault=false"
        - "--accesslog.filepath=/data/access.log"
        # entrypoints
        - "--entrypoints.http.address=:${TRAEFIK_HOST_HTTP_PORT}"
        - "--entrypoints.http.http.redirections.entrypoint.to=https"
        - "--entrypoints.https.address=:${TRAEFIK_HOST_HTTPS_PORT}"
        - "--entrypoints.traefik.address=:${TRAEFIK_DASHBOARD_PORT}"
        # certificatesresolvers
        - "--certificatesresolvers.letsencrypt.acme.email=${ACME_EMAIL}"
        - "--certificatesresolvers.letsencrypt.acme.storage=/data/acme.json"
        - "--certificatesresolvers.letsencrypt.acme.httpchallenge.entrypoint=http"
      ports:
        - "${TRAEFIK_HOST_HTTP_PORT}:${TRAEFIK_HOST_HTTP_PORT}"
        - "${TRAEFIK_HOST_HTTPS_PORT}:${TRAEFIK_HOST_HTTPS_PORT}"
        - "${TRAEFIK_DASHBOARD_PORT}:${TRAEFIK_DASHBOARD_PORT}"
      networks:
        - frontend
        - backend
      labels:
        - "traefik.enable=true"
        - "traefik.http.routers.traefik.rule=Host(`${ACME_DOMAIN}`)"
        - "traefik.http.routers.traefik.entrypoints=traefik"
        - "traefik.http.routers.traefik.service=api@internal"
        - "traefik.http.routers.traefik.middlewares=access-auth"
        - "traefik.http.routers.traefik.tls.certresolver=letsencrypt"
        - "traefik.http.middlewares.access-auth.basicauth.realm=Login Required"
        - "traefik.http.middlewares.access-auth.basicauth.users=${TRAEFIK_DASHBOARD_USER}"

### MOSQUITTO Broker #########################################
    mosquitto:
      build:
        context: ./mosquitto
      volumes:
        - ${DATA_PATH_HOST}/mosquitto/data:/mosquitto/data
      ports:
        - "${MOSQUITTO_PORT}:9001"
      networks:
        - frontend
        - backend

### COUCHDB ###################################################
    couchdb:
      build:
        context: ./couchdb
      volumes:
        - ${DATA_PATH_HOST}/couchdb/data:/opt/couchdb/data
      ports:
        - "${COUCHDB_PORT}:5984"
      networks:
        - backend

### Manticore Search ###########################################
    manticore:
      build:
        context: ./manticore
      volumes:
        - ${MANTICORE_CONFIG_PATH}:/etc/sphinxsearch
        - ${DATA_PATH_HOST}/manticore/data:/var/lib/manticore/data
        - ${DATA_PATH_HOST}/manticore/log:/var/log/manticore
      ports:
        - "${MANTICORE_API_PORT}:9312"
        - "${MANTICORE_SPHINXQL_PORT}:9306"
        - "${MANTICORE_HTTP_PORT}:9308"
      networks:
        - backend

### SONARQUBE ################################################
    sonarqube:
      build:
        context: ./sonarqube
      hostname: "${SONARQUBE_HOSTNAME}"
      volumes:
        - ${DATA_PATH_HOST}/sonarqube/conf:/opt/sonarqube/conf
        - ${DATA_PATH_HOST}/sonarqube/data:/opt/sonarqube/data
        - ${DATA_PATH_HOST}/sonarqube/logs:/opt/sonarqube/logs
        - ${DATA_PATH_HOST}/sonarqube/extensions:/opt/sonarqube/extensions
        - ${DATA_PATH_HOST}/sonarqube/plugins:/opt/sonarqube/lib/bundled-plugins
      ports:
        - ${SONARQUBE_PORT}:9000
      depends_on:
        - postgres
      environment:
        - sonar.jdbc.username=${SONARQUBE_POSTGRES_USER}
        - sonar.jdbc.password=${SONARQUBE_POSTGRES_PASSWORD}
        - sonar.jdbc.url=jdbc:postgresql://${SONARQUBE_POSTGRES_HOST}:5432/${SONARQUBE_POSTGRES_DB}
      networks:
        - backend
        - frontend

### CONFLUENCE ################################################
    confluence:
      container_name: Confluence
      image: atlassian/confluence-server:${CONFLUENCE_VERSION}
      restart: always
      ports:
        - "${CONFLUENCE_HOST_HTTP_PORT}:8090"
      networks:
        - frontend
        - backend
      depends_on:
        - postgres
      volumes:
        - ${DATA_PATH_HOST}/Confluence:/var/atlassian/application-data

### SWAGGER EDITOR ############################################
    swagger-editor:
      build: ./swagger-editor
      ports:
        - ${SWAGGER_EDITOR_PORT}:8080
      networks:
        - backend

### SWAGGER UI ################################################
    swagger-ui:
      build: ./swagger-ui
      environment:
        - API_URL=${SWAGGER_API_URL}
      ports:
        - ${SWAGGER_UI_PORT}:8080
      networks:
        - backend

### tomcat ####################################################
    tomcat:
      container_name: tomcat
      image: tomcat:${TOMCAT_VERSION}
      ports:
        - "${TOMCAT_HOST_HTTP_PORT}:8080"
      networks:
        - frontend
        - backend
      volumes:
        - ${DATA_PATH_HOST}/tomcat/webapps:/usr/local/tomcat/webapps
        - ${DATA_PATH_HOST}/tomcat/logs:/usr/local/tomcat/logs
      # restart: always

### react #####################################################
    react:
      build:
        context: ./react
      ports:
        - "3000:3000"
      container_name: react
      stdin_open: true
      environment:
        - CHOKIDAR_USEPOLLING=true
      networks:
          - frontend
          - backend
      volumes:
        - ./react:/usr/src/app/react
        - /usr/src/app/react/node_modules

### kafka ####################################################
    kafka:
      image: wurstmeister/kafka
      ports:
        - "9092:9092"
      environment:
        KAFKA_BROKER_ID: 1
        KAFKA_ADVERTISED_HOST_NAME: 127.0.0.1
        KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://127.0.0.1:9092
        KAFKA_MESSAGE_MAX_BYTES: 2000000
        KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      volumes:
        - ${DATA_PATH_HOST}/kafka:/kafka
        - /var/run/docker.sock:/var/run/docker.sock
      networks:
        - backend

### kafka-manager ###########################################
    kafka-manager:
      image: sheepkiller/kafka-manager
      ports:
        - 9020:9000
      environment:
        ZK_HOSTS: zookeeper:2181
      networks:
        - backend


### tarantool ###########################################
    tarantool:
      build: ./tarantool
      volumes:
        - ${DATA_PATH_HOST}/tarantool:/var/lib/tarantool
        - ./tarantool/lua:/opt/tarantool
      ports:
        - ${TARANTOOL_PORT}:3301
      networks:
        - backend
    tarantool-admin:
      build: ./tarantool-admin
      ports:
        - ${TARANTOOL_ADMIN_PORT}:80
      networks:
        - backend

  ### onedev ####################################################
    onedev:
      image: 1dev/server
      ports:
        - "${ONEDEV_HTTP_PORT}:6610"
        - "${ONEDEV_SSH_PORT}:6611"
      volumes:
        - ${DATA_PATH_HOST}/onedev:/opt/onedev
        - /var/run/docker.sock:/var/run/docker.sock
      networks:
        - frontend
        - backend
      restart: always
  
  ### keycloak ##################################################
    keycloak:
      image: bitnami/keycloak:${KEYCLOAK_VERSION}
      ports:
        - "${KEYCLOAK_HTTP_PORT}:${KEYCLOAK_HTTP_PORT}"
      environment:
        - KEYCLOAK_HTTP_PORT=${KEYCLOAK_HTTP_PORT}
        - KEYCLOAK_CREATE_ADMIN_USER=${KEYCLOAK_CREATE_ADMIN_USER}
        - KEYCLOAK_ADMIN_USER=${KEYCLOAK_ADMIN_USER}
        - KEYCLOAK_ADMIN_PASSWORD=${KEYCLOAK_ADMIN_PASSWORD}
        - KEYCLOAK_DATABASE_HOST=${KEYCLOAK_POSTGRES_HOST}
        - KEYCLOAK_DATABASE_PORT=${POSTGRES_PORT}
        - KEYCLOAK_DATABASE_NAME=${KEYCLOAK_POSTGRES_DB}
        - KEYCLOAK_DATABASE_USER=${KEYCLOAK_POSTGRES_USER}
        - KEYCLOAK_DATABASE_PASSWORD=${KEYCLOAK_POSTGRES_PASSWORD}
      depends_on:
        - postgres
      networks:
        - frontend
        - backend
