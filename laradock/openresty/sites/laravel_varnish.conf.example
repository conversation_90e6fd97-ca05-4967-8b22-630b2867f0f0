server {
    listen 80;
    listen [::]:80;
    server_name www.laravel.test;
    rewrite ^(.*) https://laravel.test$1/ permanent;
}

server {
    listen 80;
    listen [::]:80;
    server_name laravel.test;
    rewrite ^(.*) https://laravel.test$1/ permanent;
}

server {
    listen 443 ssl ;
    listen [::]:443 ssl;
    ssl_certificate /etc/nginx/ssl/laravel.test.crt;
    ssl_certificate_key /etc/nginx/ssl/laravel.test.key;
    server_name www.laravel.test;
    rewrite ^(.*) https://laravel.test$1/ permanent;
}

server {
    server_name laravel.test;

    # For https
    listen 443 ssl ;
    listen [::]:443 ssl;
    ssl_certificate /etc/nginx/ssl/laravel.test.crt;
    ssl_certificate_key /etc/nginx/ssl/laravel.test.key;

    port_in_redirect off;

    add_header Strict-Transport-Security "max-age=31536000";
    add_header X-Content-Type-Options nosniff;

    location / {
         proxy_pass http://proxy:6081;
         proxy_set_header Host $http_host;
         proxy_set_header X-Forwarded-Host $http_host;
         proxy_set_header X-Real-IP $remote_addr;
         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
         proxy_set_header X-Forwarded-Proto https;
         proxy_set_header HTTPS "on";
         proxy_redirect     off;
    }
}

server {
    server_name laravel.test;

    listen 81;
    listen [::]:81;

    root /var/www/laravel.test/www;

    index index.php index.html index.htm;

    location / {
         try_files $uri $uri/ /index.php$is_args$args;
    }

    location ~ \.php$ {
        fastcgi_max_temp_file_size 4m;
        fastcgi_pass php-upstream;

        # Additional configs
        fastcgi_pass_header Set-Cookie;
        fastcgi_pass_header Cookie;
        fastcgi_ignore_headers Cache-Control Expires Set-Cookie;
        try_files $uri /index.php =404;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_param  PATH_INFO $fastcgi_path_info;
        fastcgi_param  PATH_TRANSLATED $document_root$fastcgi_path_info;
        fastcgi_param HTTPS on;

        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;

        fastcgi_intercept_errors on;

        #fixes timeouts
        fastcgi_read_timeout 600;
        include fastcgi_params;
    }

    # Caching
    location ~* \.(ico|jpg|webp|jpeg|gif|css|png|js|ico|bmp|zip|woff)$ {
        access_log off;
        log_not_found off;
        add_header Pragma public;
        add_header Cache-Control "public";
        expires 14d;
    }

    location ~* \.(php|html)$ {
        access_log on;
        log_not_found on;
        add_header Pragma public;
        add_header Cache-Control "public";
        expires 14d;
    }

    location ~ /\.ht {
        deny all;
    }
}
