# ssdb-server config
# MUST indent by TAB!

# relative to path of this file, directory must exists
work_dir = /data
pidfile = /run/ssdb.pid

server:
	#ip: 127.0.0.1
	port: 8888
	# bind to public ip
	ip: 0.0.0.0
	# format: allow|deny: all|ip_prefix
	# multiple allows or denys is supported
	deny: all
	allow: 127.0.0.1
	allow: 192.
	allow: 172.
	# auth password must be at least 32 characters
	#auth: very-strong-password

replication:
	binlog: yes
	# Limit sync speed to *MB/s, -1: no limit
	sync_speed: -1
	slaveof:
		# to identify a master even if it moved(ip, port changed)
		# if set to empty or not defined, ip:port will be used.
		#id: svc_2
		# sync|mirror, default is sync
		#type: sync
		#host: s1.ssdb.db
		#port: 8888

logger:
	level: warn
	output: /var/log/ssdb.log
	rotate:
		size: 1000000000

leveldb:
	# in MB
	cache_size: 500
	# in KB
	block_size: 32
	# in MB
	write_buffer_size: 64
	# in MB
	compaction_speed: 1000
	# yes|no
	compression: yes

