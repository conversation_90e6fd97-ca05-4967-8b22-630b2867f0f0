name: Build Deploy Docs

on:
  push:
    branches:
      - master
    paths:
      - '.github/workflows/build-deploy-docs.yml'
      - 'DOCUMENTATION/**'

permissions: {}
jobs:
  build-deploy-docs:
    permissions:
      contents: write # to push pages branch (peaceiris/actions-gh-pages)

    if: github.repository == 'laradock/laradock'
    runs-on: ubuntu-22.04
    concurrency:
      group: ${{ github.workflow }}-${{ github.ref }}
    defaults:
      run:
        working-directory: ./DOCUMENTATION
    steps:
      - name: Checkout Source Code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Install DocuSource Dependencies
        run: npm install

      - name: Build DocuSource Site
        run: npm run build

      - name: Deploy DocuSource Site to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_branch: gh-pages
          publish_dir: ./DOCUMENTATION/build
