# Buzfi Laravel Project - Docker Setup

This document provides comprehensive instructions for running the Buzfi Laravel project using Docker with <PERSON><PERSON>.

## 🚀 Quick Start

### Prerequisites
- Docker installed on your system
- Docker Compose installed
- Make utility (optional, for shortcuts)

### Initial Setup

1. **Clone and Setup** (if not already done):
   ```bash
   git clone <your-repo-url>
   cd buzfi-new-backend
   ```

2. **Start the Application**:
   ```bash
   make install
   ```
   
   Or manually:
   ```bash
   cd laradock
   docker-compose build nginx php-fpm mysql phpmyadmin redis workspace
   docker-compose up -d nginx php-fpm mysql phpmyadmin redis workspace
   ```

3. **Access the Application**:
   - **Main Application**: http://localhost/buzfi-new-backend
   - **phpMyAdmin**: http://localhost/phpmyadmin

## 📋 Available Commands

### Using Makefile (Recommended)

```bash
make help              # Show all available commands
make start             # Start all services
make stop              # Stop all services
make restart           # Restart all services
make status            # Show container status
make logs              # Show logs from all services
make shell             # Access workspace shell
make mysql-shell       # Access MySQL shell
make import-db         # Import database
make clean             # Remove all containers and volumes
```

### Manual Docker Commands

```bash
# Start services
cd laradock && docker-compose up -d nginx php-fpm mysql phpmyadmin redis workspace

# Stop services
cd laradock && docker-compose stop

# View logs
cd laradock && docker-compose logs -f

# Access workspace shell
cd laradock && docker-compose exec workspace bash

# Access MySQL shell
cd laradock && docker-compose exec mysql mysql -u root -proot buzfi
```

## 🗄️ Database Management

### Automatic Database Import

The database will be automatically imported on first startup. The SQL file is located at:
- `import_database/buzfi.sql`

### Manual Database Import

If you need to import the database manually:

```bash
make import-db
```

Or manually:
```bash
./import_database/import.sh
```

### Database Connection Details

- **Host**: localhost (from host machine) or `mysql` (from containers)
- **Port**: 3306
- **Database**: buzfi
- **Username**: root
- **Password**: root

### phpMyAdmin Access

- **URL**: http://localhost/phpmyadmin
- **Username**: root
- **Password**: root

## 🔧 Configuration

### Services Included

1. **Nginx** - Web server (Port 80)
2. **PHP 8.2-FPM** - PHP processor
3. **MySQL 8.4** - Database server (Port 3306)
4. **phpMyAdmin** - Database management interface
5. **Redis** - Caching server (Port 6379)
6. **Workspace** - Development environment

### Environment Configuration

The Docker environment is configured in:
- `laradock/.env` - Laradock configuration
- `.env` - Laravel application configuration (you need to create this)

### Laravel Environment Setup

1. **Create Laravel .env file**:
   ```bash
   cp .env.example .env
   ```

2. **Update database configuration in .env**:
   ```env
   DB_CONNECTION=mysql
   DB_HOST=mysql
   DB_PORT=3306
   DB_DATABASE=buzfi
   DB_USERNAME=root
   DB_PASSWORD=root
   ```

3. **Install dependencies**:
   ```bash
   make composer-install
   ```

4. **Generate application key**:
   ```bash
   make artisan cmd="key:generate"
   ```

## 🛠️ Development Workflow

### Common Laravel Commands

```bash
# Install PHP dependencies
make composer-install

# Run migrations
make migrate

# Run seeders
make seed

# Fresh migration with seeding
make fresh

# Run any artisan command
make artisan cmd="your-command-here"

# Examples:
make artisan cmd="migrate"
make artisan cmd="db:seed"
make artisan cmd="queue:work"
make artisan cmd="cache:clear"
```

### File Permissions

If you encounter permission issues:

```bash
# Access workspace shell
make shell

# Fix permissions
sudo chown -R www-data:www-data /var/www/storage
sudo chown -R www-data:www-data /var/www/bootstrap/cache
chmod -R 775 /var/www/storage
chmod -R 775 /var/www/bootstrap/cache
```

## 🔍 Troubleshooting

### Common Issues

1. **Port Already in Use**:
   ```bash
   # Check what's using port 80
   sudo lsof -i :80
   
   # Stop conflicting services
   sudo systemctl stop apache2  # If Apache is running
   sudo systemctl stop nginx    # If Nginx is running
   ```

2. **Database Connection Issues**:
   ```bash
   # Check MySQL container status
   make status
   
   # View MySQL logs
   make logs-mysql
   
   # Restart MySQL
   cd laradock && docker-compose restart mysql
   ```

3. **Permission Denied**:
   ```bash
   # Make scripts executable
   chmod +x import_database/import.sh
   
   # Fix Laravel permissions
   make shell
   sudo chown -R www-data:www-data /var/www
   ```

### Logs and Debugging

```bash
# View all logs
make logs

# View specific service logs
make logs-nginx
make logs-php
make logs-mysql

# Follow logs in real-time
cd laradock && docker-compose logs -f nginx php-fpm mysql
```

## 🧹 Maintenance

### Updating Containers

```bash
# Rebuild containers
make build

# Pull latest images
cd laradock && docker-compose pull
```

### Cleanup

```bash
# Remove all containers and volumes (WARNING: Deletes all data!)
make clean

# Remove unused Docker resources
docker system prune -f
```

### Backup Database

```bash
# Create database backup
docker exec buzfi-backend-mysql-1 mysqldump -u root -proot buzfi > backup_$(date +%Y%m%d_%H%M%S).sql
```

## 📁 Project Structure

```
buzfi-new-backend/
├── laradock/                 # Docker configuration
│   ├── .env                 # Laradock environment
│   ├── nginx/sites/         # Nginx configurations
│   └── mysql/               # MySQL configurations
├── import_database/         # Database import files
│   ├── buzfi.sql           # Main database file
│   └── import.sh           # Import script
├── Makefile                # Command shortcuts
├── DOCKER_README.md        # This documentation
└── [Laravel files...]      # Your Laravel application
```

## 🆘 Support

If you encounter any issues:

1. Check the logs: `make logs`
2. Verify container status: `make status`
3. Try restarting services: `make restart`
4. For persistent issues, try: `make clean` then `make install`

## 📝 Notes

- The application runs on PHP 8.2 as requested
- Database is automatically imported on first startup
- All services are configured to work together seamlessly
- Use the Makefile commands for easier management
- phpMyAdmin is accessible at http://localhost/phpmyadmin
