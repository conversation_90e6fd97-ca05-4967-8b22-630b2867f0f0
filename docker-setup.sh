#!/bin/bash

# Buz<PERSON> Laravel Project - Docker Setup Script
# This script helps you set up the project quickly

set -e

echo "🚀 Buzfi Laravel Project - Docker Setup"
echo "========================================"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker is not installed. Please install Docker first.${NC}"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose is not installed. Please install Docker Compose first.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker and Docker Compose are installed${NC}"

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo -e "${YELLOW}📝 Creating Laravel .env file...${NC}"
    cp .env.docker .env
    echo -e "${GREEN}✅ .env file created${NC}"
else
    echo -e "${YELLOW}⚠️  .env file already exists. Skipping...${NC}"
fi

# Make scripts executable
echo -e "${YELLOW}🔧 Making scripts executable...${NC}"
chmod +x import_database/import.sh
chmod +x docker-setup.sh

# Build and start containers
echo -e "${YELLOW}🏗️  Building Docker containers...${NC}"
cd laradock
docker-compose build nginx php-fpm mysql phpmyadmin redis workspace

echo -e "${YELLOW}🚀 Starting Docker containers...${NC}"
docker-compose up -d nginx php-fpm mysql phpmyadmin redis workspace

echo -e "${YELLOW}⏳ Waiting for services to be ready...${NC}"
sleep 30

# Check if containers are running
echo -e "${YELLOW}🔍 Checking container status...${NC}"
docker-compose ps

cd ..

# Install Composer dependencies
echo -e "${YELLOW}📦 Installing PHP dependencies...${NC}"
cd laradock
docker-compose exec -T workspace composer install --no-interaction

# Generate application key
echo -e "${YELLOW}🔑 Generating application key...${NC}"
docker-compose exec -T workspace php artisan key:generate

# Set permissions
echo -e "${YELLOW}🔒 Setting file permissions...${NC}"
docker-compose exec -T workspace chown -R www-data:www-data /var/www/storage
docker-compose exec -T workspace chown -R www-data:www-data /var/www/bootstrap/cache
docker-compose exec -T workspace chmod -R 775 /var/www/storage
docker-compose exec -T workspace chmod -R 775 /var/www/bootstrap/cache

cd ..

echo ""
echo -e "${GREEN}🎉 Setup completed successfully!${NC}"
echo ""
echo -e "${YELLOW}📋 Access Information:${NC}"
echo -e "   🌐 Application: ${GREEN}http://localhost/buzfi-new-backend${NC}"
echo -e "   🗄️  phpMyAdmin: ${GREEN}http://localhost/phpmyadmin${NC}"
echo -e "   📊 Database: buzfi (root/root)"
echo ""
echo -e "${YELLOW}🛠️  Useful Commands:${NC}"
echo -e "   make start     - Start all services"
echo -e "   make stop      - Stop all services"
echo -e "   make status    - Check container status"
echo -e "   make logs      - View logs"
echo -e "   make shell     - Access workspace shell"
echo -e "   make help      - Show all available commands"
echo ""
echo -e "${GREEN}✨ Your Buzfi Laravel application is ready!${NC}"
